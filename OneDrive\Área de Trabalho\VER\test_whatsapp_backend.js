#!/usr/bin/env node

/**
 * Teste para verificar se o backend WhatsApp está funcionando
 */

const axios = require('axios');

async function testBackend() {
    console.log('🔍 Testando conectividade do backend WhatsApp...\n');
    
    const tests = [
        {
            name: 'Conectividade básica',
            url: 'http://localhost:3001/',
            method: 'GET'
        },
        {
            name: 'Status endpoint',
            url: 'http://localhost:3001/status',
            method: 'GET'
        },
        {
            name: 'QR Code endpoint',
            url: 'http://localhost:3001/qr-code',
            method: 'GET'
        }
    ];
    
    for (const test of tests) {
        try {
            console.log(`🔄 Testando: ${test.name}`);
            
            const response = await axios({
                method: test.method,
                url: test.url,
                timeout: 5000
            });
            
            console.log(`✅ ${test.name}: OK (${response.status})`);
            console.log(`   Resposta: ${JSON.stringify(response.data).substring(0, 100)}...`);
            
        } catch (error) {
            console.log(`❌ ${test.name}: FALHOU`);
            
            if (error.code === 'ECONNREFUSED') {
                console.log(`   Erro: Conexão recusada - servidor não está rodando na porta 3001`);
            } else if (error.code === 'ETIMEDOUT') {
                console.log(`   Erro: Timeout - servidor não responde`);
            } else if (error.response) {
                console.log(`   Erro HTTP: ${error.response.status} - ${error.response.statusText}`);
            } else {
                console.log(`   Erro: ${error.message}`);
            }
        }
        
        console.log('');
    }
    
    // Teste adicional: verificar se a porta está em uso
    console.log('🔍 Verificando se a porta 3001 está em uso...');
    
    try {
        const net = require('net');
        const server = net.createServer();
        
        server.listen(3001, () => {
            console.log('❌ Porta 3001 está LIVRE (backend não está rodando)');
            server.close();
        });
        
        server.on('error', (err) => {
            if (err.code === 'EADDRINUSE') {
                console.log('✅ Porta 3001 está EM USO (backend provavelmente rodando)');
            } else {
                console.log(`❌ Erro ao verificar porta: ${err.message}`);
            }
        });
        
    } catch (error) {
        console.log(`❌ Erro ao verificar porta: ${error.message}`);
    }
}

// Executar teste
testBackend().catch(console.error);
