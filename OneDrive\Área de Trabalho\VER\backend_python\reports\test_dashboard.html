
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard de Testes - <PERSON><PERSON> da <PERSON></title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #4CAF50;
            transition: transform 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
        }
        
        .metric-card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        
        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }
        
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .charts-section {
            padding: 30px;
            background: #f8f9fa;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status-pass { color: #4CAF50; }
        .status-warn { color: #FF9800; }
        .status-fail { color: #F44336; }
        
        .details-section {
            padding: 30px;
        }
        
        .category-section {
            margin-bottom: 30px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .category-title {
            font-size: 1.4em;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4CAF50;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-name {
            font-weight: 500;
        }
        
        .test-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status-pass-bg {
            background: #e8f5e8;
            color: #4CAF50;
        }
        
        .status-warn-bg {
            background: #fff3e0;
            color: #FF9800;
        }
        
        .status-fail-bg {
            background: #ffebee;
            color: #F44336;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Dashboard de Testes</h1>
            <p>Chat da Persona - Vereadora Rafaela de Nilda</p>
            <p>Gerado em: 13/07/2025 13:31:50</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>📊 Taxa de Sucesso</h3>
                <div class="metric-value">83.3%</div>
                <div class="metric-label">10/12 testes aprovados</div>
            </div>
            
            <div class="metric-card">
                <h3>⏱️ Tempo de Execução</h3>
                <div class="metric-value">0.04s</div>
                <div class="metric-label">Duração total dos testes</div>
            </div>
            
            <div class="metric-card">
                <h3>🚀 Performance</h3>
                <div class="metric-value">50027</div>
                <div class="metric-label">Requisições por segundo</div>
            </div>
            
            <div class="metric-card">
                <h3>📋 Cobertura</h3>
                <div class="metric-value">100%</div>
                <div class="metric-label">Keywords testadas</div>
            </div>
            
            <div class="metric-card">
                <h3>🎯 Qualidade</h3>
                <div class="metric-value">58%</div>
                <div class="metric-label">Qualidade das respostas</div>
            </div>
            
            <div class="metric-card">
                <h3>💾 Memória</h3>
                <div class="metric-value">0.0MB</div>
                <div class="metric-label">Aumento de memória</div>
            </div>
        </div>
        
        <div class="charts-section">
            <div class="charts-grid">
                <div class="chart-container">
                    <div class="chart-title">📊 Resultados por Categoria</div>
                    <canvas id="categoryChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">🎯 Distribuição de Status</div>
                    <canvas id="statusChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">📈 Métricas de Performance</div>
                    <canvas id="performanceChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">📋 Cobertura de Testes</div>
                    <canvas id="coverageChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="details-section">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">📋 Detalhes dos Testes</h2>
    
            <div class="category-section">
                <div class="category-title">BASIC</div>
        
                <div class="test-item">
                    <div class="test-name">Persona Config</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            
                <div class="test-item">
                    <div class="test-name">Post Processing</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            
                <div class="test-item">
                    <div class="test-name">Template Matching</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            </div>
            <div class="category-section">
                <div class="category-title">PERFORMANCE</div>
        
                <div class="test-item">
                    <div class="test-name">Processing Speed</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            
                <div class="test-item">
                    <div class="test-name">Throughput</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            
                <div class="test-item">
                    <div class="test-name">Memory Usage</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            </div>
            <div class="category-section">
                <div class="category-title">COVERAGE</div>
        
                <div class="test-item">
                    <div class="test-name">Keyword Coverage</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            
                <div class="test-item">
                    <div class="test-name">Emoji Coverage</div>
                    <div class="test-status status-warn-bg">WARN</div>
                </div>
            </div>
            <div class="category-section">
                <div class="category-title">QUALITY</div>
        
                <div class="test-item">
                    <div class="test-name">Response Quality</div>
                    <div class="test-status status-warn-bg">WARN</div>
                </div>
            
                <div class="test-item">
                    <div class="test-name">Persona Consistency</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            </div>
            <div class="category-section">
                <div class="category-title">STRESS</div>
        
                <div class="test-item">
                    <div class="test-name">Concurrent Processing</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            
                <div class="test-item">
                    <div class="test-name">Template Stress</div>
                    <div class="test-status status-pass-bg">PASS</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>Dashboard gerado automaticamente pela Suite de Testes do Chat da Persona</p>
            <p>Vereadora Rafaela de Nilda - Parnamirim/RN</p>
        </div>
    </div>
    
    <script>
        // Gráfico de categorias
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: ['basic', 'performance', 'coverage', 'quality', 'stress'],
                datasets: [{
                    label: 'Taxa de Sucesso (%)',
                    data: [100.0, 100.0, 50.0, 50.0, 100.0],
                    backgroundColor: [
                        'rgba(76, 175, 80, 0.8)',
                        'rgba(33, 150, 243, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(156, 39, 176, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(76, 175, 80, 1)',
                        'rgba(33, 150, 243, 1)',
                        'rgba(255, 152, 0, 1)',
                        'rgba(156, 39, 176, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
        
        // Gráfico de status
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Aprovados', 'Atenção', 'Falharam'],
                datasets: [{
                    data: [10, 2, 0],
                    backgroundColor: [
                        'rgba(76, 175, 80, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true
            }
        });
        
        // Gráfico de performance
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(performanceCtx, {
            type: 'radar',
            data: {
                labels: ['Velocidade', 'Throughput', 'Memória', 'Concorrência', 'Stress'],
                datasets: [{
                    label: 'Performance Score',
                    data: [95, 90, 100, 85, 95],
                    backgroundColor: 'rgba(76, 175, 80, 0.2)',
                    borderColor: 'rgba(76, 175, 80, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
        
        // Gráfico de cobertura
        const coverageCtx = document.getElementById('coverageChart').getContext('2d');
        new Chart(coverageCtx, {
            type: 'line',
            data: {
                labels: ['Keywords', 'Emojis', 'Hashtags', 'Funções', 'Edge Cases'],
                datasets: [{
                    label: 'Cobertura (%)',
                    data: [100, 46, 75, 90, 80],
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderColor: 'rgba(33, 150, 243, 1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    </script>
</body>
</html>
    