"""
Sistema de Cache Inteligente
Otimiza performance com cache semântico e estratégias avançadas
"""

import asyncio
import hashlib
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, OrderedDict
import threading
import psutil
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity


@dataclass
class CacheEntry:
    """Entrada do cache com metadados"""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl_seconds: int
    size_bytes: int
    similarity_vector: Optional[List[float]] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
    
    @property
    def is_expired(self) -> bool:
        """Verifica se a entrada expirou"""
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl_seconds)
    
    @property
    def age_seconds(self) -> int:
        """Idade da entrada em segundos"""
        return int((datetime.now() - self.created_at).total_seconds())


@dataclass
class CacheStats:
    """Estatísticas do cache"""
    total_requests: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    semantic_hits: int = 0
    evictions: int = 0
    memory_usage_mb: float = 0.0
    avg_response_time_ms: float = 0.0
    
    @property
    def hit_rate(self) -> float:
        """Taxa de acerto do cache"""
        if self.total_requests == 0:
            return 0.0
        return (self.cache_hits + self.semantic_hits) / self.total_requests
    
    @property
    def semantic_hit_rate(self) -> float:
        """Taxa de acerto semântico"""
        if self.total_requests == 0:
            return 0.0
        return self.semantic_hits / self.total_requests


class IntelligentCache:
    """
    Cache inteligente com busca semântica e otimizações avançadas
    """
    
    def __init__(self, max_size_mb: int = 100, semantic_threshold: float = 0.8):
        self.max_size_mb = max_size_mb
        self.semantic_threshold = semantic_threshold
        
        # Armazenamento principal
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.semantic_index: Dict[str, List[float]] = {}
        
        # Estatísticas
        self.stats = CacheStats()
        
        # Configurações
        self.default_ttl = 3600  # 1 hora
        self.cleanup_interval = 300  # 5 minutos
        self.max_entries = 10000
        
        # Vetorizador para busca semântica
        self.vectorizer = TfidfVectorizer(
            max_features=100,
            stop_words=None,
            ngram_range=(1, 2)
        )
        self.is_vectorizer_fitted = False
        
        # Thread para limpeza automática
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
        
        # Lock para thread safety
        self.lock = threading.RLock()
    
    def _generate_key(self, query: str, context: Dict = None) -> str:
        """Gera chave única para a query"""
        key_data = {
            'query': query.lower().strip(),
            'context': context or {}
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _calculate_size(self, value: Any) -> int:
        """Calcula tamanho aproximado do valor em bytes"""
        try:
            return len(json.dumps(value, default=str).encode('utf-8'))
        except:
            return len(str(value).encode('utf-8'))
    
    def _get_similarity_vector(self, text: str) -> Optional[List[float]]:
        """Obtém vetor de similaridade para o texto"""
        try:
            if not self.is_vectorizer_fitted:
                # Treinar vectorizer com textos do cache
                texts = [entry.key for entry in self.cache.values()]
                if len(texts) > 10:  # Mínimo para treinar
                    texts.append(text)
                    self.vectorizer.fit(texts)
                    self.is_vectorizer_fitted = True
                else:
                    return None
            
            vector = self.vectorizer.transform([text]).toarray()[0]
            return vector.tolist()
        except Exception as e:
            print(f"Erro ao calcular vetor de similaridade: {e}")
            return None
    
    def _find_semantic_match(self, query: str) -> Optional[Tuple[str, float]]:
        """Encontra correspondência semântica no cache"""
        if not self.is_vectorizer_fitted or len(self.cache) < 5:
            return None
        
        try:
            query_vector = self._get_similarity_vector(query)
            if query_vector is None:
                return None
            
            best_match = None
            best_similarity = 0.0
            
            for key, entry in self.cache.items():
                if entry.similarity_vector:
                    similarity = cosine_similarity(
                        [query_vector], 
                        [entry.similarity_vector]
                    )[0][0]
                    
                    if similarity > best_similarity and similarity >= self.semantic_threshold:
                        best_similarity = similarity
                        best_match = key
            
            return (best_match, best_similarity) if best_match else None
            
        except Exception as e:
            print(f"Erro na busca semântica: {e}")
            return None
    
    async def get(self, query: str, context: Dict = None) -> Optional[Any]:
        """Obtém valor do cache com busca semântica"""
        start_time = time.time()
        
        with self.lock:
            self.stats.total_requests += 1
            
            # Busca exata
            key = self._generate_key(query, context)
            
            if key in self.cache:
                entry = self.cache[key]
                
                if not entry.is_expired:
                    # Cache hit
                    entry.last_accessed = datetime.now()
                    entry.access_count += 1
                    
                    # Mover para o final (LRU)
                    self.cache.move_to_end(key)
                    
                    self.stats.cache_hits += 1
                    response_time = (time.time() - start_time) * 1000
                    self._update_avg_response_time(response_time)
                    
                    return entry.value
                else:
                    # Entrada expirada
                    del self.cache[key]
            
            # Busca semântica
            semantic_match = self._find_semantic_match(query)
            if semantic_match:
                match_key, similarity = semantic_match
                
                if match_key in self.cache:
                    entry = self.cache[match_key]
                    
                    if not entry.is_expired:
                        # Semantic hit
                        entry.last_accessed = datetime.now()
                        entry.access_count += 1
                        
                        self.cache.move_to_end(match_key)
                        
                        self.stats.semantic_hits += 1
                        response_time = (time.time() - start_time) * 1000
                        self._update_avg_response_time(response_time)
                        
                        print(f"🎯 Cache semântico: '{query}' → '{match_key}' (similaridade: {similarity:.2f})")
                        return entry.value
            
            # Cache miss
            self.stats.cache_misses += 1
            response_time = (time.time() - start_time) * 1000
            self._update_avg_response_time(response_time)
            
            return None
    
    async def set(self, query: str, value: Any, context: Dict = None, ttl: int = None, tags: List[str] = None) -> bool:
        """Armazena valor no cache"""
        with self.lock:
            key = self._generate_key(query, context)
            size_bytes = self._calculate_size(value)
            
            # Verificar limites
            if not self._check_capacity(size_bytes):
                return False
            
            # Calcular vetor de similaridade
            similarity_vector = self._get_similarity_vector(query)
            
            # Criar entrada
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                last_accessed=datetime.now(),
                access_count=1,
                ttl_seconds=ttl or self.default_ttl,
                size_bytes=size_bytes,
                similarity_vector=similarity_vector,
                tags=tags or []
            )
            
            # Armazenar
            self.cache[key] = entry
            
            # Atualizar estatísticas
            self._update_memory_usage()
            
            return True
    
    def _check_capacity(self, new_size_bytes: int) -> bool:
        """Verifica se há capacidade para novo item"""
        current_size_mb = self.stats.memory_usage_mb
        new_size_mb = new_size_bytes / (1024 * 1024)
        
        if current_size_mb + new_size_mb > self.max_size_mb:
            # Tentar fazer espaço
            self._evict_entries(new_size_mb)
            
            # Verificar novamente
            if self.stats.memory_usage_mb + new_size_mb > self.max_size_mb:
                return False
        
        return len(self.cache) < self.max_entries
    
    def _evict_entries(self, space_needed_mb: float):
        """Remove entradas para liberar espaço"""
        space_freed_mb = 0.0
        entries_to_remove = []
        
        # Estratégia: remover entradas menos acessadas e mais antigas
        sorted_entries = sorted(
            self.cache.items(),
            key=lambda x: (x[1].access_count, -x[1].age_seconds)
        )
        
        for key, entry in sorted_entries:
            if space_freed_mb >= space_needed_mb:
                break
            
            entries_to_remove.append(key)
            space_freed_mb += entry.size_bytes / (1024 * 1024)
        
        # Remover entradas
        for key in entries_to_remove:
            del self.cache[key]
            self.stats.evictions += 1
        
        self._update_memory_usage()
        
        if entries_to_remove:
            print(f"🗑️ Cache: {len(entries_to_remove)} entradas removidas para liberar {space_freed_mb:.2f}MB")
    
    def _update_memory_usage(self):
        """Atualiza estatísticas de uso de memória"""
        total_bytes = sum(entry.size_bytes for entry in self.cache.values())
        self.stats.memory_usage_mb = total_bytes / (1024 * 1024)
    
    def _update_avg_response_time(self, response_time_ms: float):
        """Atualiza tempo médio de resposta"""
        if self.stats.avg_response_time_ms == 0:
            self.stats.avg_response_time_ms = response_time_ms
        else:
            # Média móvel
            self.stats.avg_response_time_ms = (
                self.stats.avg_response_time_ms * 0.9 + response_time_ms * 0.1
            )
    
    def _cleanup_loop(self):
        """Loop de limpeza automática"""
        while True:
            try:
                time.sleep(self.cleanup_interval)
                self.cleanup_expired()
            except Exception as e:
                print(f"Erro na limpeza do cache: {e}")
    
    def cleanup_expired(self):
        """Remove entradas expiradas"""
        with self.lock:
            expired_keys = [
                key for key, entry in self.cache.items()
                if entry.is_expired
            ]
            
            for key in expired_keys:
                del self.cache[key]
            
            if expired_keys:
                self._update_memory_usage()
                print(f"🧹 Cache: {len(expired_keys)} entradas expiradas removidas")
    
    def invalidate_by_tags(self, tags: List[str]):
        """Invalida entradas por tags"""
        with self.lock:
            keys_to_remove = []
            
            for key, entry in self.cache.items():
                if any(tag in entry.tags for tag in tags):
                    keys_to_remove.append(key)
            
            for key in keys_to_remove:
                del self.cache[key]
            
            if keys_to_remove:
                self._update_memory_usage()
                print(f"🏷️ Cache: {len(keys_to_remove)} entradas invalidadas por tags")
    
    def clear(self):
        """Limpa todo o cache"""
        with self.lock:
            self.cache.clear()
            self.semantic_index.clear()
            self.stats = CacheStats()
            self.is_vectorizer_fitted = False
            print("🗑️ Cache completamente limpo")
    
    def get_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas do cache"""
        with self.lock:
            return {
                'total_entries': len(self.cache),
                'memory_usage_mb': round(self.stats.memory_usage_mb, 2),
                'hit_rate': round(self.stats.hit_rate * 100, 2),
                'semantic_hit_rate': round(self.stats.semantic_hit_rate * 100, 2),
                'total_requests': self.stats.total_requests,
                'cache_hits': self.stats.cache_hits,
                'semantic_hits': self.stats.semantic_hits,
                'cache_misses': self.stats.cache_misses,
                'evictions': self.stats.evictions,
                'avg_response_time_ms': round(self.stats.avg_response_time_ms, 2)
            }
    
    def get_top_entries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Obtém entradas mais acessadas"""
        with self.lock:
            sorted_entries = sorted(
                self.cache.items(),
                key=lambda x: x[1].access_count,
                reverse=True
            )
            
            return [
                {
                    'key': key[:50] + '...' if len(key) > 50 else key,
                    'access_count': entry.access_count,
                    'age_seconds': entry.age_seconds,
                    'size_bytes': entry.size_bytes,
                    'tags': entry.tags
                }
                for key, entry in sorted_entries[:limit]
            ]


class CacheManager:
    """
    Gerenciador de múltiplos caches especializados
    """
    
    def __init__(self):
        # Caches especializados
        self.response_cache = IntelligentCache(max_size_mb=50, semantic_threshold=0.8)
        self.template_cache = IntelligentCache(max_size_mb=20, semantic_threshold=0.9)
        self.rag_cache = IntelligentCache(max_size_mb=30, semantic_threshold=0.7)
        self.user_context_cache = IntelligentCache(max_size_mb=20, semantic_threshold=0.95)
        
        # Configurações específicas
        self.template_cache.default_ttl = 7200  # 2 horas
        self.rag_cache.default_ttl = 1800  # 30 minutos
        self.user_context_cache.default_ttl = 3600  # 1 hora
    
    async def get_response(self, query: str, context: Dict = None) -> Optional[str]:
        """Obtém resposta do cache de respostas"""
        return await self.response_cache.get(query, context)
    
    async def cache_response(self, query: str, response: str, context: Dict = None, tags: List[str] = None):
        """Armazena resposta no cache"""
        await self.response_cache.set(query, response, context, tags=tags)
    
    async def get_template_match(self, query: str) -> Optional[str]:
        """Obtém match de template do cache"""
        return await self.template_cache.get(query)
    
    async def cache_template_match(self, query: str, template_response: str):
        """Armazena match de template no cache"""
        await self.template_cache.set(query, template_response, tags=['template'])
    
    async def get_rag_context(self, query: str) -> Optional[str]:
        """Obtém contexto RAG do cache"""
        return await self.rag_cache.get(query)
    
    async def cache_rag_context(self, query: str, context: str):
        """Armazena contexto RAG no cache"""
        await self.rag_cache.set(query, context, tags=['rag', 'documents'])
    
    async def get_user_context(self, user_id: str) -> Optional[Dict]:
        """Obtém contexto do usuário do cache"""
        return await self.user_context_cache.get(user_id)
    
    async def cache_user_context(self, user_id: str, context: Dict):
        """Armazena contexto do usuário no cache"""
        await self.user_context_cache.set(user_id, context, tags=['user', 'context'])
    
    def invalidate_user_cache(self, user_id: str):
        """Invalida cache específico do usuário"""
        self.user_context_cache.invalidate_by_tags(['user'])
        self.response_cache.invalidate_by_tags(['user'])
    
    def get_global_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas globais de todos os caches"""
        return {
            'response_cache': self.response_cache.get_stats(),
            'template_cache': self.template_cache.get_stats(),
            'rag_cache': self.rag_cache.get_stats(),
            'user_context_cache': self.user_context_cache.get_stats(),
            'system_memory_usage_mb': psutil.Process().memory_info().rss / 1024 / 1024
        }


# Instância global do gerenciador de cache
cache_manager = CacheManager()
