"""
Testes para Memória Conversacional e Cache Inteligente
"""

import unittest
import asyncio
import sys
import os
import time
from unittest.mock import Mock, patch

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from memory.conversation_memory import ConversationMemory, UserProfile, ConversationContext
from cache.intelligent_cache import IntelligentCache, CacheManager
from load_balancer.auto_scaling import AutoLoadBalancer, LoadBalancerConfig


class TestConversationMemory(unittest.TestCase):
    """
    Testes para o sistema de memória conversacional
    """
    
    def setUp(self):
        """Configurar testes"""
        self.memory = ConversationMemory()
        self.test_user_id = "test_user_123"
        self.test_conversation_id = "test_conv_456"
    
    def test_user_profile_creation(self):
        """Testa criação de perfil de usuário"""
        print("   🧪 Testando criação de perfil...")
        
        async def run_test():
            profile = await self.memory.get_user_profile(self.test_user_id)
            
            self.assertIsInstance(profile, UserProfile)
            self.assertEqual(profile.user_id, self.test_user_id)
            self.assertEqual(profile.interaction_count, 0)
            self.assertIsNone(profile.name)
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Perfil criado com sucesso")
    
    def test_user_info_extraction(self):
        """Testa extração de informações do usuário"""
        print("   🧪 Testando extração de informações...")
        
        async def run_test():
            # Testar extração de nome
            message1 = "Oi, meu nome é João e moro no Centro"
            extracted = await self.memory.extract_user_info_from_message(self.test_user_id, message1)
            
            self.assertIn('name', extracted)
            self.assertEqual(extracted['name'], 'João')
            self.assertIn('neighborhood', extracted)
            self.assertEqual(extracted['neighborhood'], 'Centro')
            
            # Verificar se foi salvo no perfil
            profile = await self.memory.get_user_profile(self.test_user_id)
            self.assertEqual(profile.name, 'João')
            self.assertEqual(profile.neighborhood, 'Centro')
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Informações extraídas corretamente")
    
    def test_conversation_context(self):
        """Testa contexto de conversa"""
        print("   🧪 Testando contexto de conversa...")
        
        async def run_test():
            context = await self.memory.get_conversation_context(self.test_conversation_id, self.test_user_id)
            
            self.assertIsInstance(context, ConversationContext)
            self.assertEqual(context.conversation_id, self.test_conversation_id)
            self.assertEqual(context.user_id, self.test_user_id)
            
            # Atualizar contexto
            await self.memory.update_conversation_context(
                self.test_conversation_id,
                topic="health",
                sentiment="positive",
                urgency_level="normal"
            )
            
            updated_context = await self.memory.get_conversation_context(self.test_conversation_id, self.test_user_id)
            self.assertEqual(updated_context.topic, "health")
            self.assertEqual(updated_context.sentiment, "positive")
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Contexto de conversa funcionando")
    
    def test_intent_analysis(self):
        """Testa análise de intenção e entidades"""
        print("   🧪 Testando análise de intenção...")
        
        async def run_test():
            # Testar diferentes tipos de mensagem
            test_cases = [
                {
                    "message": "Bom dia! Como você está?",
                    "expected_intent": "greeting",
                    "expected_sentiment": "positive"
                },
                {
                    "message": "Preciso de ajuda urgente com cesta básica",
                    "expected_intent": "help_request",
                    "expected_topic": "social_assistance",
                    "expected_urgency": "urgent"
                },
                {
                    "message": "Onde fica a UBS mais próxima?",
                    "expected_intent": "information",
                    "expected_topic": "health"
                }
            ]
            
            for case in test_cases:
                analysis = await self.memory.analyze_message_intent_and_entities(
                    case["message"], self.test_user_id
                )
                
                if "expected_intent" in case:
                    self.assertEqual(analysis["intent"], case["expected_intent"])
                
                if "expected_topic" in case:
                    self.assertEqual(analysis["topic"], case["expected_topic"])
                
                if "expected_sentiment" in case:
                    self.assertEqual(analysis["sentiment"], case["expected_sentiment"])
                
                if "expected_urgency" in case:
                    self.assertEqual(analysis["urgency_level"], case["expected_urgency"])
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Análise de intenção funcionando")
    
    def test_personalized_context(self):
        """Testa contexto personalizado"""
        print("   🧪 Testando contexto personalizado...")
        
        async def run_test():
            # Configurar perfil do usuário
            await self.memory.update_user_profile(
                self.test_user_id,
                name="Maria",
                neighborhood="Ponta Negra",
                preferred_tone="formal"
            )
            
            # Obter contexto personalizado
            context = await self.memory.get_personalized_response_context(
                self.test_user_id, self.test_conversation_id
            )
            
            self.assertEqual(context['user_name'], "Maria")
            self.assertEqual(context['user_neighborhood'], "Ponta Negra")
            self.assertEqual(context['preferred_tone'], "formal")
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Contexto personalizado funcionando")


class TestIntelligentCache(unittest.TestCase):
    """
    Testes para o sistema de cache inteligente
    """
    
    def setUp(self):
        """Configurar testes"""
        self.cache = IntelligentCache(max_size_mb=10, semantic_threshold=0.7)
        self.cache_manager = CacheManager()
    
    def test_basic_cache_operations(self):
        """Testa operações básicas do cache"""
        print("   🧪 Testando operações básicas do cache...")
        
        async def run_test():
            # Testar set e get
            await self.cache.set("test_query", "test_response", ttl=60)
            result = await self.cache.get("test_query")
            
            self.assertEqual(result, "test_response")
            
            # Testar cache miss
            result = await self.cache.get("nonexistent_query")
            self.assertIsNone(result)
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Operações básicas funcionando")
    
    def test_semantic_search(self):
        """Testa busca semântica no cache"""
        print("   🧪 Testando busca semântica...")
        
        async def run_test():
            # Adicionar várias entradas para treinar o vectorizer
            test_data = [
                ("Como solicitar cesta básica?", "Para solicitar cesta básica, vá ao CRAS"),
                ("Onde pegar auxílio alimentação?", "O auxílio alimentação é no CRAS"),
                ("Preciso de ajuda com medicamentos", "Para medicamentos, procure a UBS"),
                ("Como marcar consulta médica?", "Para consultas, ligue para a UBS"),
                ("Informações sobre emprego", "Para empregos, veja no SINE"),
                ("Vagas de trabalho disponíveis", "Vagas de trabalho no SINE"),
                ("Como fazer matrícula escolar?", "Matrícula na Secretaria de Educação"),
                ("Onde estudar meus filhos?", "Escolas na Secretaria de Educação"),
                ("Problema na rua", "Para problemas de rua, ligue 156"),
                ("Buraco na via", "Para buracos, acione a Prefeitura")
            ]
            
            # Adicionar ao cache
            for query, response in test_data:
                await self.cache.set(query, response)
            
            # Testar busca semântica
            # "auxílio comida" deve encontrar algo relacionado a "cesta básica"
            result = await self.cache.get("auxílio comida")
            
            # Se encontrou algo, deve ser relacionado a alimentação
            if result:
                self.assertIn("CRAS", result)
                print(f"   🎯 Busca semântica encontrou: {result}")
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Busca semântica funcionando")
    
    def test_cache_manager(self):
        """Testa gerenciador de cache"""
        print("   🧪 Testando gerenciador de cache...")
        
        async def run_test():
            # Testar cache de respostas
            await self.cache_manager.cache_response("test query", "test response")
            result = await self.cache_manager.get_response("test query")
            self.assertEqual(result, "test response")
            
            # Testar cache de templates
            await self.cache_manager.cache_template_match("bom dia", "Oi! Como posso ajudar?")
            result = await self.cache_manager.get_template_match("bom dia")
            self.assertEqual(result, "Oi! Como posso ajudar?")
            
            # Testar cache RAG
            await self.cache_manager.cache_rag_context("programas sociais", "Contexto sobre programas")
            result = await self.cache_manager.get_rag_context("programas sociais")
            self.assertEqual(result, "Contexto sobre programas")
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Gerenciador de cache funcionando")
    
    def test_cache_stats(self):
        """Testa estatísticas do cache"""
        print("   🧪 Testando estatísticas do cache...")
        
        async def run_test():
            # Adicionar algumas entradas
            await self.cache.set("query1", "response1")
            await self.cache.set("query2", "response2")
            
            # Fazer algumas consultas
            await self.cache.get("query1")  # hit
            await self.cache.get("query1")  # hit
            await self.cache.get("query3")  # miss
            
            stats = self.cache.get_stats()
            
            self.assertGreater(stats['total_requests'], 0)
            self.assertGreater(stats['cache_hits'], 0)
            self.assertGreater(stats['cache_misses'], 0)
            self.assertGreaterEqual(stats['hit_rate'], 0)
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Estatísticas funcionando")


class TestLoadBalancer(unittest.TestCase):
    """
    Testes para o sistema de load balancing
    """
    
    def setUp(self):
        """Configurar testes"""
        def dummy_worker(data):
            """Worker de teste"""
            time.sleep(0.1)  # Simular processamento
            return f"Processed: {data}"
        
        self.worker_function = dummy_worker
        self.config = LoadBalancerConfig(
            max_workers=5,
            min_workers=1,
            scale_up_threshold=50.0,
            scale_down_threshold=20.0
        )
    
    def test_load_balancer_creation(self):
        """Testa criação do load balancer"""
        print("   🧪 Testando criação do load balancer...")
        
        lb = AutoLoadBalancer(self.worker_function, self.config)
        
        self.assertIsNotNone(lb)
        self.assertGreaterEqual(len(lb.worker_pool.workers), self.config.min_workers)
        
        # Limpar
        lb.shutdown()
        print("   ✅ Load balancer criado com sucesso")
    
    def test_request_processing(self):
        """Testa processamento de requisições"""
        print("   🧪 Testando processamento de requisições...")
        
        async def run_test():
            lb = AutoLoadBalancer(self.worker_function, self.config)
            
            try:
                # Processar algumas requisições
                results = []
                for i in range(5):
                    result = await lb.process_request(f"data_{i}", f"req_{i}")
                    results.append(result)
                
                # Verificar resultados
                self.assertEqual(len(results), 5)
                for i, result in enumerate(results):
                    self.assertEqual(result, f"Processed: data_{i}")
                
                return True
            finally:
                lb.shutdown()
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Processamento de requisições funcionando")
    
    def test_load_balancer_stats(self):
        """Testa estatísticas do load balancer"""
        print("   🧪 Testando estatísticas do load balancer...")
        
        async def run_test():
            lb = AutoLoadBalancer(self.worker_function, self.config)
            
            try:
                # Processar algumas requisições
                for i in range(3):
                    await lb.process_request(f"data_{i}")
                
                # Obter estatísticas
                stats = lb.get_load_balancer_stats()
                
                self.assertIn('active_workers', stats)
                self.assertIn('total_requests_processed', stats)
                self.assertIn('worker_details', stats)
                self.assertGreater(stats['total_requests_processed'], 0)
                
                return True
            finally:
                lb.shutdown()
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Estatísticas do load balancer funcionando")


class TestIntegration(unittest.TestCase):
    """
    Testes de integração entre memória, cache e load balancer
    """
    
    def test_memory_cache_integration(self):
        """Testa integração entre memória e cache"""
        print("   🧪 Testando integração memória + cache...")
        
        async def run_test():
            memory = ConversationMemory()
            cache_manager = CacheManager()
            
            user_id = "integration_user"
            
            # Simular fluxo completo
            # 1. Extrair informações do usuário
            await memory.extract_user_info_from_message(user_id, "Meu nome é Ana e moro em Capim Macio")
            
            # 2. Armazenar resposta no cache
            await cache_manager.cache_response("bom dia", "Oi Ana! Como posso ajudar você hoje?")
            
            # 3. Obter resposta do cache
            cached_response = await cache_manager.get_response("bom dia")
            
            # 4. Verificar contexto personalizado
            context = await memory.get_personalized_response_context(user_id, "conv_123")
            
            self.assertEqual(cached_response, "Oi Ana! Como posso ajudar você hoje?")
            self.assertEqual(context['user_name'], "Ana")
            self.assertEqual(context['user_neighborhood'], "Capim Macio")
            
            return True
        
        result = asyncio.run(run_test())
        self.assertTrue(result)
        print("   ✅ Integração memória + cache funcionando")


def run_memory_and_cache_tests():
    """Executa todos os testes de memória e cache"""
    print("🧪 EXECUTANDO TESTES DE MEMÓRIA E CACHE")
    print("=" * 60)
    
    # Executar testes
    test_classes = [
        TestConversationMemory,
        TestIntelligentCache,
        TestLoadBalancer,
        TestIntegration
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for test_class in test_classes:
        print(f"\n📋 {test_class.__name__}")
        print("-" * 40)
        
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        
        for test in suite:
            total_tests += 1
            try:
                test.debug()
                passed_tests += 1
            except Exception as e:
                print(f"   ❌ {test._testMethodName}: {e}")
    
    # Relatório final
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO DE TESTES DE MEMÓRIA E CACHE")
    print("=" * 60)
    print(f"🧪 Testes executados: {total_tests}")
    print(f"✅ Testes aprovados: {passed_tests}")
    print(f"📈 Taxa de sucesso: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print(f"\n🎉 EXCELENTE! Memória e cache funcionando perfeitamente!")
    elif success_rate >= 80:
        print(f"\n✅ BOM! Memória e cache funcionando bem!")
    else:
        print(f"\n⚠️ ATENÇÃO! Alguns testes falharam.")
    
    print("=" * 60)
    
    return success_rate > 80


if __name__ == "__main__":
    run_memory_and_cache_tests()
