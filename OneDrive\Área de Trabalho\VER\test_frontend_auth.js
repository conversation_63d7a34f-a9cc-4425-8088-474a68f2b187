#!/usr/bin/env node

/**
 * Teste para verificar problemas de autenticação no frontend
 */

const axios = require('axios');

async function testFrontendAuth() {
    console.log('🔍 TESTE DE AUTENTICAÇÃO - FRONTEND vs BACKEND\n');
    
    try {
        // 1. Testar login direto (como o backend Python faz)
        console.log('🔐 1. TESTANDO LOGIN DIRETO...');
        
        const directLoginResponse = await axios.post('http://localhost:8000/token', 
            'username=admin&password=admin123',
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
        );
        
        console.log('✅ Login direto funcionou!');
        console.log(`   Token: ${directLoginResponse.data.access_token.substring(0, 50)}...`);
        
        // 2. Testar login como o frontend faz
        console.log('\n🌐 2. TESTANDO LOGIN COMO FRONTEND...');
        
        const params = new URLSearchParams();
        params.append('username', 'admin');
        params.append('password', 'admin123');
        
        const frontendLoginResponse = await axios.post('http://localhost:8000/token', params, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        });
        
        console.log('✅ Login estilo frontend funcionou!');
        console.log(`   Token: ${frontendLoginResponse.data.access_token.substring(0, 50)}...`);
        
        // 3. Testar endpoint protegido
        console.log('\n🔒 3. TESTANDO ENDPOINT PROTEGIDO...');
        
        const token = frontendLoginResponse.data.access_token;
        const headers = { 'Authorization': `Bearer ${token}` };
        
        const protectedResponse = await axios.get('http://localhost:8000/dashboard/stats', { headers });
        console.log('✅ Endpoint protegido funcionou!');
        console.log(`   Resposta: ${JSON.stringify(protectedResponse.data).substring(0, 100)}...`);
        
        // 4. Testar diferentes variações de credenciais
        console.log('\n🧪 4. TESTANDO VARIAÇÕES DE CREDENCIAIS...');
        
        const testCases = [
            { username: 'admin', password: 'admin123', expected: 'success' },
            { username: 'Admin', password: 'admin123', expected: 'fail' },
            { username: 'admin', password: 'Admin123', expected: 'fail' },
            { username: 'admin', password: 'wrong', expected: 'fail' },
            { username: '', password: 'admin123', expected: 'fail' },
            { username: 'admin', password: '', expected: 'fail' }
        ];
        
        for (const testCase of testCases) {
            try {
                const testParams = new URLSearchParams();
                testParams.append('username', testCase.username);
                testParams.append('password', testCase.password);
                
                const testResponse = await axios.post('http://localhost:8000/token', testParams, {
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
                });
                
                if (testCase.expected === 'success') {
                    console.log(`   ✅ "${testCase.username}/${testCase.password}" - Sucesso (esperado)`);
                } else {
                    console.log(`   ⚠️  "${testCase.username}/${testCase.password}" - Sucesso (inesperado!)`);
                }
            } catch (error) {
                if (testCase.expected === 'fail') {
                    console.log(`   ✅ "${testCase.username}/${testCase.password}" - Falhou (esperado) - ${error.response?.status}`);
                } else {
                    console.log(`   ❌ "${testCase.username}/${testCase.password}" - Falhou (inesperado!) - ${error.response?.status}`);
                }
            }
        }
        
        // 5. Verificar se há problemas de CORS
        console.log('\n🌐 5. TESTANDO CORS...');
        
        try {
            const corsResponse = await axios.options('http://localhost:8000/token');
            console.log('✅ CORS preflight funcionou');
            console.log(`   Headers: ${JSON.stringify(corsResponse.headers)}`);
        } catch (error) {
            console.log(`❌ CORS preflight falhou: ${error.message}`);
        }
        
        // 6. Verificar se o frontend está rodando
        console.log('\n🎨 6. VERIFICANDO FRONTEND...');
        
        try {
            const frontendResponse = await axios.get('http://localhost:3000');
            console.log('✅ Frontend está rodando na porta 3000');
        } catch (error) {
            console.log(`❌ Frontend não está respondendo: ${error.message}`);
        }
        
        try {
            const frontendResponse = await axios.get('http://localhost:3002');
            console.log('✅ Frontend está rodando na porta 3002');
        } catch (error) {
            console.log(`❌ Frontend não está na porta 3002: ${error.message}`);
        }
        
        // 7. Resumo e diagnóstico
        console.log('\n' + '='.repeat(60));
        console.log('🎯 DIAGNÓSTICO DE AUTENTICAÇÃO');
        console.log('='.repeat(60));
        
        console.log('\n✅ FUNCIONANDO:');
        console.log('   🔐 Login direto via API');
        console.log('   🌐 Login estilo frontend');
        console.log('   🔒 Endpoints protegidos');
        console.log('   🧪 Validação de credenciais');
        
        console.log('\n💡 POSSÍVEIS CAUSAS DO ERRO 401:');
        console.log('   1. Cache do browser com token expirado');
        console.log('   2. Frontend enviando credenciais em formato incorreto');
        console.log('   3. Problema de timing/race condition');
        console.log('   4. LocalStorage corrompido');
        console.log('   5. Interceptor do axios com problema');
        
        console.log('\n🔧 SOLUÇÕES RECOMENDADAS:');
        console.log('   1. Limpar cache do browser (Ctrl+Shift+Delete)');
        console.log('   2. Abrir DevTools e verificar Network tab');
        console.log('   3. Verificar localStorage no Application tab');
        console.log('   4. Tentar login em aba anônima');
        console.log('   5. Verificar console do browser por erros');
        
        console.log('\n🌐 TESTE MANUAL:');
        console.log('   1. Abra: http://localhost:3000/login');
        console.log('   2. Use: admin / admin123');
        console.log('   3. Abra DevTools (F12)');
        console.log('   4. Vá para Network tab');
        console.log('   5. Tente fazer login');
        console.log('   6. Verifique a requisição POST /token');
        
        console.log('\n✅ BACKEND ESTÁ 100% FUNCIONAL!');
        console.log('='.repeat(60));
        
    } catch (error) {
        console.error('\n❌ ERRO NO TESTE:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 SOLUÇÕES:');
            console.log('   - Verifique se o backend Python está rodando (porta 8000)');
            console.log('   - Comando: cd backend_python && python -m uvicorn app:app --reload');
        }
    }
}

// Executar teste
testFrontendAuth().catch(console.error);
