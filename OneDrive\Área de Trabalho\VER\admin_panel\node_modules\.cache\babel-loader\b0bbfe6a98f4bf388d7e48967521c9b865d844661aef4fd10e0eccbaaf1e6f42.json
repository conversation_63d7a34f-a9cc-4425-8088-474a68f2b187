{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\VER\\\\admin_panel\\\\src\\\\components\\\\whatsapp\\\\ConversationList.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\nimport { List, ListItem, ListItemText, ListItemAvatar, Avatar, Typography, CircularProgress, Alert, Badge, Box, Divider } from '@mui/material';\nimport axiosInstance from '../../api/axiosInstance';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst fetchConversations = async () => {\n  const {\n    data\n  } = await axiosInstance.get('/whatsapp/conversations');\n  return data.filter(chat => chat.id && chat.name);\n};\nconst ConversationList = () => {\n  _s();\n  const queryClient = useQueryClient();\n  const {\n    data: conversations,\n    isLoading,\n    isError,\n    error\n  } = useQuery({\n    queryKey: ['whatsapp-conversations'],\n    queryFn: fetchConversations\n  });\n  useEffect(() => {\n    const ws = new WebSocket('ws://backend-python:8000/ws/notifications');\n    ws.onmessage = event => {\n      const message = JSON.parse(event.data);\n      if (message.type === 'new_message') {\n        queryClient.invalidateQueries(['whatsapp-conversations']);\n      }\n    };\n    ws.onclose = () => console.log('WebSocket disconnected');\n    ws.onerror = err => console.error('WebSocket error:', err);\n    return () => ws.close();\n  }, [queryClient]);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(CircularProgress, {\n      role: \"status\",\n      \"aria-label\": \"Carregando conversas\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 12\n    }, this);\n  }\n  if (isError) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      role: \"alert\",\n      \"aria-live\": \"assertive\",\n      children: [\"Erro ao buscar conversas: \", error.message]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(List, {\n    sx: {\n      width: '100%',\n      bgcolor: 'background.paper',\n      p: 0\n    },\n    role: \"list\",\n    \"aria-label\": \"Lista de conversas do WhatsApp\",\n    children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      sx: {\n        p: 2\n      },\n      role: \"status\",\n      \"aria-live\": \"polite\",\n      children: \"Nenhuma conversa encontrada.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 9\n    }, this) : conversations.map((chat, index) => {\n      var _chat$contact, _chat$contact$profile, _chat$lastMessage;\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(ListItem, {\n          role: \"listitem\",\n          \"aria-label\": `Conversa com ${chat.name || chat.id.user}`,\n          alignItems: \"flex-start\",\n          sx: {\n            '&:hover': {\n              backgroundColor: 'action.hover'\n            },\n            cursor: 'pointer'\n          },\n          tabIndex: 0,\n          children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n            children: /*#__PURE__*/_jsxDEV(Avatar, {\n              alt: chat.name,\n              src: ((_chat$contact = chat.contact) === null || _chat$contact === void 0 ? void 0 : (_chat$contact$profile = _chat$contact.profilePicThumbObj) === null || _chat$contact$profile === void 0 ? void 0 : _chat$contact$profile.eurl) || '',\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: /*#__PURE__*/_jsxDEV(Typography, {\n              noWrap: true,\n              children: chat.name || chat.id.user\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 19\n            }, this),\n            secondary: /*#__PURE__*/_jsxDEV(Box, {\n              component: \"div\",\n              sx: {\n                display: 'flex',\n                flexDirection: 'column'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                component: \"div\",\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: ((_chat$lastMessage = chat.lastMessage) === null || _chat$lastMessage === void 0 ? void 0 : _chat$lastMessage.body) || 'Nenhuma mensagem ainda.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 64,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'flex-end',\n              ml: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              color: \"text.secondary\",\n              children: chat.timestamp ? new Date(chat.timestamp * 1000).toLocaleTimeString([], {\n                hour: '2-digit',\n                minute: '2-digit'\n              }) : ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this), chat.unreadCount > 0 && /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: chat.unreadCount,\n              color: \"primary\",\n              sx: {\n                mt: 1\n              },\n              \"aria-label\": `${chat.unreadCount} mensagens não lidas`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this), index < conversations.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {\n          variant: \"inset\",\n          component: \"li\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 50\n        }, this)]\n      }, chat.id._serialized, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(ConversationList, \"sGcab2ZWtnk87C4AWbq4Ns+cVFc=\", false, function () {\n  return [useQueryClient, useQuery];\n});\n_c = ConversationList;\nexport default ConversationList;\nvar _c;\n$RefreshReg$(_c, \"ConversationList\");", "map": {"version": 3, "names": ["React", "useEffect", "useQuery", "useQueryClient", "List", "ListItem", "ListItemText", "ListItemAvatar", "Avatar", "Typography", "CircularProgress", "<PERSON><PERSON>", "Badge", "Box", "Divider", "axiosInstance", "jsxDEV", "_jsxDEV", "fetchConversations", "data", "get", "filter", "chat", "id", "name", "ConversationList", "_s", "queryClient", "conversations", "isLoading", "isError", "error", "query<PERSON><PERSON>", "queryFn", "ws", "WebSocket", "onmessage", "event", "message", "JSON", "parse", "type", "invalidateQueries", "onclose", "console", "log", "onerror", "err", "close", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "children", "sx", "width", "bgcolor", "p", "length", "map", "index", "_chat$contact", "_chat$contact$profile", "_chat$lastMessage", "Fragment", "user", "alignItems", "backgroundColor", "cursor", "tabIndex", "alt", "src", "contact", "profilePicThumbObj", "eurl", "primary", "noWrap", "secondary", "component", "display", "flexDirection", "variant", "color", "lastMessage", "body", "ml", "timestamp", "Date", "toLocaleTimeString", "hour", "minute", "unreadCount", "badgeContent", "mt", "_serialized", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/VER/admin_panel/src/components/whatsapp/ConversationList.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\nimport { List, ListItem, ListItemText, ListItemAvatar, Avatar, Typography, CircularProgress, Alert, Badge, Box, Divider } from '@mui/material';\nimport axiosInstance from '../../api/axiosInstance';\n\nconst fetchConversations = async () => {\n  const { data } = await axiosInstance.get('/whatsapp/conversations');\n  return data.filter(chat => chat.id && chat.name);\n};\n\nconst ConversationList = () => {\n  const queryClient = useQueryClient();\n  const { data: conversations, isLoading, isError, error } = useQuery({\n    queryKey: ['whatsapp-conversations'],\n    queryFn: fetchConversations,\n  });\n\n  useEffect(() => {\n    const ws = new WebSocket('ws://backend-python:8000/ws/notifications');\n    ws.onmessage = (event) => {\n      const message = JSON.parse(event.data);\n      if (message.type === 'new_message') {\n        queryClient.invalidateQueries(['whatsapp-conversations']);\n      }\n    };\n    ws.onclose = () => console.log('WebSocket disconnected');\n    ws.onerror = (err) => console.error('WebSocket error:', err);\n    return () => ws.close();\n  }, [queryClient]);\n\n  if (isLoading) {\n    return <CircularProgress role=\"status\" aria-label=\"Carregando conversas\" />;\n  }\n\n  if (isError) {\n    return <Alert severity=\"error\" role=\"alert\" aria-live=\"assertive\">Erro ao buscar conversas: {error.message}</Alert>;\n  }\n\n  return (\n    <List sx={{ width: '100%', bgcolor: 'background.paper', p: 0 }} role=\"list\" aria-label=\"Lista de conversas do WhatsApp\">\n      {conversations.length === 0 ? (\n        <Typography sx={{ p: 2 }} role=\"status\" aria-live=\"polite\">Nenhuma conversa encontrada.</Typography>\n      ) : (\n        conversations.map((chat, index) => (\n          <React.Fragment key={chat.id._serialized}>\n            <ListItem\n              role=\"listitem\"\n              aria-label={`Conversa com ${chat.name || chat.id.user}`}\n              alignItems=\"flex-start\"\n              sx={{ '&:hover': { backgroundColor: 'action.hover' }, cursor: 'pointer' }}\n              tabIndex={0}\n            >\n              <ListItemAvatar>\n                <Avatar alt={chat.name} src={chat.contact?.profilePicThumbObj?.eurl || ''} aria-hidden=\"true\" />\n              </ListItemAvatar>\n              <ListItemText\n                primary={\n                  <Typography noWrap>\n                    {chat.name || chat.id.user}\n                  </Typography>\n                }\n                secondary={\n                  <Box component=\"div\" sx={{ display: 'flex', flexDirection: 'column' }}>\n                    <Typography component=\"div\" variant=\"body2\" color=\"text.secondary\">\n                      {chat.lastMessage?.body || 'Nenhuma mensagem ainda.'}\n                    </Typography>\n                  </Box>\n                }\n              />\n              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', ml: 2 }}>\n                <Typography variant=\"caption\" color=\"text.secondary\">\n                  {chat.timestamp ? new Date(chat.timestamp * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''}\n                </Typography>\n                {chat.unreadCount > 0 && (\n                  <Badge badgeContent={chat.unreadCount} color=\"primary\" sx={{ mt: 1 }} aria-label={`${chat.unreadCount} mensagens não lidas`} />\n                )}\n              </Box>\n            </ListItem>\n            {index < conversations.length - 1 && <Divider variant=\"inset\" component=\"li\" />}\n          </React.Fragment>\n        ))\n      )}\n    </List>\n  );\n};\n\nexport default ConversationList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,QAAQ,EAAEC,cAAc,QAAQ,uBAAuB;AAChE,SAASC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,QAAQ,eAAe;AAC9I,OAAOC,aAAa,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EACrC,MAAM;IAAEC;EAAK,CAAC,GAAG,MAAMJ,aAAa,CAACK,GAAG,CAAC,yBAAyB,CAAC;EACnE,OAAOD,IAAI,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,IAAID,IAAI,CAACE,IAAI,CAAC;AAClD,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,WAAW,GAAGxB,cAAc,CAAC,CAAC;EACpC,MAAM;IAAEgB,IAAI,EAAES,aAAa;IAAEC,SAAS;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAG7B,QAAQ,CAAC;IAClE8B,QAAQ,EAAE,CAAC,wBAAwB,CAAC;IACpCC,OAAO,EAAEf;EACX,CAAC,CAAC;EAEFjB,SAAS,CAAC,MAAM;IACd,MAAMiC,EAAE,GAAG,IAAIC,SAAS,CAAC,2CAA2C,CAAC;IACrED,EAAE,CAACE,SAAS,GAAIC,KAAK,IAAK;MACxB,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAAClB,IAAI,CAAC;MACtC,IAAImB,OAAO,CAACG,IAAI,KAAK,aAAa,EAAE;QAClCd,WAAW,CAACe,iBAAiB,CAAC,CAAC,wBAAwB,CAAC,CAAC;MAC3D;IACF,CAAC;IACDR,EAAE,CAACS,OAAO,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACxDX,EAAE,CAACY,OAAO,GAAIC,GAAG,IAAKH,OAAO,CAACb,KAAK,CAAC,kBAAkB,EAAEgB,GAAG,CAAC;IAC5D,OAAO,MAAMb,EAAE,CAACc,KAAK,CAAC,CAAC;EACzB,CAAC,EAAE,CAACrB,WAAW,CAAC,CAAC;EAEjB,IAAIE,SAAS,EAAE;IACb,oBAAOZ,OAAA,CAACP,gBAAgB;MAACuC,IAAI,EAAC,QAAQ;MAAC,cAAW;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7E;EAEA,IAAIvB,OAAO,EAAE;IACX,oBAAOb,OAAA,CAACN,KAAK;MAAC2C,QAAQ,EAAC,OAAO;MAACL,IAAI,EAAC,OAAO;MAAC,aAAU,WAAW;MAAAM,QAAA,GAAC,4BAA0B,EAACxB,KAAK,CAACO,OAAO;IAAA;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EACrH;EAEA,oBACEpC,OAAA,CAACb,IAAI;IAACoD,EAAE,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,CAAC,EAAE;IAAE,CAAE;IAACV,IAAI,EAAC,MAAM;IAAC,cAAW,gCAAgC;IAAAM,QAAA,EACpH3B,aAAa,CAACgC,MAAM,KAAK,CAAC,gBACzB3C,OAAA,CAACR,UAAU;MAAC+C,EAAE,EAAE;QAAEG,CAAC,EAAE;MAAE,CAAE;MAACV,IAAI,EAAC,QAAQ;MAAC,aAAU,QAAQ;MAAAM,QAAA,EAAC;IAA4B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAEpGzB,aAAa,CAACiC,GAAG,CAAC,CAACvC,IAAI,EAAEwC,KAAK;MAAA,IAAAC,aAAA,EAAAC,qBAAA,EAAAC,iBAAA;MAAA,oBAC5BhD,OAAA,CAACjB,KAAK,CAACkE,QAAQ;QAAAX,QAAA,gBACbtC,OAAA,CAACZ,QAAQ;UACP4C,IAAI,EAAC,UAAU;UACf,cAAY,gBAAgB3B,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACC,EAAE,CAAC4C,IAAI,EAAG;UACxDC,UAAU,EAAC,YAAY;UACvBZ,EAAE,EAAE;YAAE,SAAS,EAAE;cAAEa,eAAe,EAAE;YAAe,CAAC;YAAEC,MAAM,EAAE;UAAU,CAAE;UAC1EC,QAAQ,EAAE,CAAE;UAAAhB,QAAA,gBAEZtC,OAAA,CAACV,cAAc;YAAAgD,QAAA,eACbtC,OAAA,CAACT,MAAM;cAACgE,GAAG,EAAElD,IAAI,CAACE,IAAK;cAACiD,GAAG,EAAE,EAAAV,aAAA,GAAAzC,IAAI,CAACoD,OAAO,cAAAX,aAAA,wBAAAC,qBAAA,GAAZD,aAAA,CAAcY,kBAAkB,cAAAX,qBAAA,uBAAhCA,qBAAA,CAAkCY,IAAI,KAAI,EAAG;cAAC,eAAY;YAAM;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACjBpC,OAAA,CAACX,YAAY;YACXuE,OAAO,eACL5D,OAAA,CAACR,UAAU;cAACqE,MAAM;cAAAvB,QAAA,EACfjC,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACC,EAAE,CAAC4C;YAAI;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACb;YACD0B,SAAS,eACP9D,OAAA,CAACJ,GAAG;cAACmE,SAAS,EAAC,KAAK;cAACxB,EAAE,EAAE;gBAAEyB,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE;cAAS,CAAE;cAAA3B,QAAA,eACpEtC,OAAA,CAACR,UAAU;gBAACuE,SAAS,EAAC,KAAK;gBAACG,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAA7B,QAAA,EAC/D,EAAAU,iBAAA,GAAA3C,IAAI,CAAC+D,WAAW,cAAApB,iBAAA,uBAAhBA,iBAAA,CAAkBqB,IAAI,KAAI;cAAyB;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFpC,OAAA,CAACJ,GAAG;YAAC2C,EAAE,EAAE;cAAEyB,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAEd,UAAU,EAAE,UAAU;cAAEmB,EAAE,EAAE;YAAE,CAAE;YAAAhC,QAAA,gBACnFtC,OAAA,CAACR,UAAU;cAAC0E,OAAO,EAAC,SAAS;cAACC,KAAK,EAAC,gBAAgB;cAAA7B,QAAA,EACjDjC,IAAI,CAACkE,SAAS,GAAG,IAAIC,IAAI,CAACnE,IAAI,CAACkE,SAAS,GAAG,IAAI,CAAC,CAACE,kBAAkB,CAAC,EAAE,EAAE;gBAAEC,IAAI,EAAE,SAAS;gBAAEC,MAAM,EAAE;cAAU,CAAC,CAAC,GAAG;YAAE;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G,CAAC,EACZ/B,IAAI,CAACuE,WAAW,GAAG,CAAC,iBACnB5E,OAAA,CAACL,KAAK;cAACkF,YAAY,EAAExE,IAAI,CAACuE,WAAY;cAACT,KAAK,EAAC,SAAS;cAAC5B,EAAE,EAAE;gBAAEuC,EAAE,EAAE;cAAE,CAAE;cAAC,cAAY,GAAGzE,IAAI,CAACuE,WAAW;YAAuB;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAC/H;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACVS,KAAK,GAAGlC,aAAa,CAACgC,MAAM,GAAG,CAAC,iBAAI3C,OAAA,CAACH,OAAO;UAACqE,OAAO,EAAC,OAAO;UAACH,SAAS,EAAC;QAAI;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,GAlC5D/B,IAAI,CAACC,EAAE,CAACyE,WAAW;QAAA9C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAmCxB,CAAC;IAAA,CAClB;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAAC3B,EAAA,CA1EID,gBAAgB;EAAA,QACAtB,cAAc,EACyBD,QAAQ;AAAA;AAAA+F,EAAA,GAF/DxE,gBAAgB;AA4EtB,eAAeA,gBAAgB;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}