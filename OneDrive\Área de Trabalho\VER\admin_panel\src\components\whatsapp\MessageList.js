import React, { useEffect, useRef, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  List,
  ListItem,
  Typography,
  CircularProgress,
  Alert,
  Box,
  Paper,
  Avatar,
  Chip,
  Divider,
  Button,
  IconButton,
  Tooltip
} from '@mui/material';
import { Person, SmartToy, Schedule, History, Refresh } from '@mui/icons-material';
import axiosInstance from '../../api/axiosInstance';

const fetchWhatsAppMessages = async (chatId) => {
  if (!chatId) return [];

  try {
    // Buscar TODAS as mensagens da conversa
    const { data } = await axiosInstance.get(`/whatsapp/conversations/${chatId}/messages`, {
      params: {
        includeAll: 'true', // Buscar todas as mensagens
        limit: 'all'        // Sem limite
      }
    });

    console.log(`📨 Carregadas ${data.returnedMessages} de ${data.totalMessages} mensagens para ${chatId}`);
    return data.messages || [];
  } catch (error) {
    console.error('Erro ao buscar mensagens:', error);
    return [];
  }
};

const MessageList = ({ chatId, chatName }) => {
  const messagesEndRef = useRef(null);
  const queryClient = useQueryClient();
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);

  const { data: messages, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['whatsapp-messages', chatId],
    queryFn: () => fetchWhatsAppMessages(chatId),
    enabled: !!chatId,
    refetchInterval: 10000, // Atualizar a cada 10 segundos para não sobrecarregar
  });

  // Scroll automático para a última mensagem quando mensagens carregam
  useEffect(() => {
    if (messages && messages.length > 0) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Função para forçar carregamento de histórico
  const loadMoreHistory = async () => {
    if (!chatId || isLoadingHistory) return;

    setIsLoadingHistory(true);
    try {
      console.log('🔄 Forçando carregamento de histórico...');

      // Chamar endpoint para carregar histórico
      const response = await axiosInstance.post(`/whatsapp/conversations/${chatId}/load-history`);
      console.log('✅ Histórico carregado:', response.data);

      // Atualizar cache das mensagens
      await refetch();

      // Invalidar cache para forçar nova busca
      queryClient.invalidateQueries(['whatsapp-messages', chatId]);

    } catch (error) {
      console.error('❌ Erro ao carregar histórico:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  if (!chatId) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          Selecione uma conversa para ver as mensagens
        </Typography>
      </Box>
    );
  }

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', p: 4, height: '100%' }}>
        <CircularProgress size={40} />
        <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
          Carregando histórico completo de mensagens...
        </Typography>
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
          Isso pode levar alguns segundos
        </Typography>
      </Box>
    );
  }

  if (isError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Erro ao buscar mensagens: {error.message}
      </Alert>
    );
  }

  const formatMessageTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp * 1000);
    return date.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getMessageSender = (message) => {
    if (message.fromMe) return 'Você';
    if (message.author) return message.author;
    if (message.from) return message.from;
    return 'Contato';
  };

  const getMessageContent = (message) => {
    if (message.body) return message.body;
    if (message.content) return message.content;
    if (message.type === 'image') return '📷 Imagem';
    if (message.type === 'audio') return '🎵 Áudio';
    if (message.type === 'video') return '🎥 Vídeo';
    if (message.type === 'document') return '📄 Documento';
    return 'Mensagem sem conteúdo';
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header da conversa */}
      <Paper sx={{ p: 2, borderRadius: 0, borderBottom: 1, borderColor: 'divider', bgcolor: 'primary.main', color: 'primary.contrastText' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar sx={{ bgcolor: 'primary.contrastText', color: 'primary.main' }}>
            <Person />
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" sx={{ color: 'inherit' }}>{chatName}</Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography variant="caption" sx={{ color: 'inherit', opacity: 0.9 }}>
                {messages ? `${messages.length} mensagens` : 'Carregando...'}
              </Typography>
              {messages && messages.length > 0 && (
                <Chip
                  label="Histórico Completo"
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.2)',
                    color: 'inherit',
                    fontSize: '0.7rem'
                  }}
                />
              )}
            </Box>
          </Box>

          {/* Botões de ação */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Carregar mais histórico">
              <IconButton
                onClick={loadMoreHistory}
                disabled={isLoadingHistory}
                sx={{ color: 'inherit', opacity: 0.8 }}
                size="small"
              >
                {isLoadingHistory ? <CircularProgress size={20} color="inherit" /> : <History />}
              </IconButton>
            </Tooltip>

            <Tooltip title="Atualizar mensagens">
              <IconButton
                onClick={() => refetch()}
                sx={{ color: 'inherit', opacity: 0.8 }}
                size="small"
              >
                <Refresh />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Paper>

      {/* Lista de mensagens */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 1 }}>
        {/* Indicador de carregamento de histórico */}
        {isLoadingHistory && (
          <Box sx={{ p: 2, textAlign: 'center', bgcolor: 'info.light', color: 'info.contrastText', mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
              <CircularProgress size={16} color="inherit" />
              <Typography variant="caption">
                Carregando histórico anterior...
              </Typography>
            </Box>
          </Box>
        )}

        {messages.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Nenhuma mensagem encontrada nesta conversa.
            </Typography>
            <Button
              startIcon={<History />}
              onClick={loadMoreHistory}
              disabled={isLoadingHistory}
              sx={{ mt: 2 }}
              variant="outlined"
              size="small"
            >
              Carregar Histórico
            </Button>
          </Box>
        ) : (
          <List sx={{ p: 1 }}>
            {messages.map((message, index) => {
              const isFromMe = message.fromMe;
              const sender = getMessageSender(message);
              const content = getMessageContent(message);
              const timestamp = formatMessageTime(message.timestamp || message.t);

              // Verificar se é uma nova data
              const currentDate = new Date((message.timestamp || message.t) * 1000).toDateString();
              const previousMessage = index > 0 ? messages[index - 1] : null;
              const previousDate = previousMessage ? new Date((previousMessage.timestamp || previousMessage.t) * 1000).toDateString() : null;
              const isNewDay = currentDate !== previousDate;

              return (
                <React.Fragment key={message.id || index}>
                  {/* Separador de data */}
                  {isNewDay && (
                    <Box sx={{ display: 'flex', alignItems: 'center', my: 2 }}>
                      <Divider sx={{ flexGrow: 1 }} />
                      <Chip
                        label={new Date((message.timestamp || message.t) * 1000).toLocaleDateString('pt-BR', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                        size="small"
                        sx={{ mx: 2, bgcolor: 'grey.100' }}
                      />
                      <Divider sx={{ flexGrow: 1 }} />
                    </Box>
                  )}

                  <ListItem
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: isFromMe ? 'flex-end' : 'flex-start',
                      mb: 0.5,
                      px: 1
                    }}
                  >
                    <Paper
                      elevation={1}
                      sx={{
                        p: 1.5,
                        maxWidth: '75%',
                        minWidth: '120px',
                        backgroundColor: isFromMe ? 'primary.main' : 'grey.50',
                        color: isFromMe ? 'primary.contrastText' : 'text.primary',
                        borderRadius: 2,
                        borderTopRightRadius: isFromMe ? 0.5 : 2,
                        borderTopLeftRadius: isFromMe ? 2 : 0.5,
                        position: 'relative'
                      }}
                    >
                      {/* Cabeçalho da mensagem (apenas para mensagens não suas) */}
                      {!isFromMe && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                          <Avatar sx={{ width: 20, height: 20 }}>
                            <Person fontSize="small" />
                          </Avatar>
                          <Typography variant="caption" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                            {sender}
                          </Typography>
                        </Box>
                      )}

                      {/* Conteúdo da mensagem */}
                      <Typography
                        variant="body2"
                        sx={{
                          mb: 0.5,
                          wordBreak: 'break-word',
                          lineHeight: 1.4
                        }}
                      >
                        {content}
                      </Typography>

                      {/* Timestamp */}
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 0.5 }}>
                        <Schedule sx={{ fontSize: '0.7rem', opacity: 0.6 }} />
                        <Typography
                          variant="caption"
                          sx={{
                            opacity: 0.8,
                            fontSize: '0.7rem'
                          }}
                        >
                          {timestamp}
                        </Typography>
                      </Box>
                    </Paper>
                  </ListItem>
                </React.Fragment>
              );
            })}

            {/* Referência para scroll automático */}
            <div ref={messagesEndRef} />
          </List>
        )}
      </Box>
    </Box>
  );
};

export default MessageList;
