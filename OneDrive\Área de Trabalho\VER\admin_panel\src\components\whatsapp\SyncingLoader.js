import React from 'react';
import { Box, CircularProgress, Typography, Paper } from '@mui/material';

const SyncingLoader = ({ status }) => {
  const messages = {
    INITIALIZING: 'Inicializando cliente do WhatsApp...',
    SYNCING: 'Sincronizando conversas e mensagens...',
    // Adicione outros status se necessário
  };

  return (
    <Paper sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 3 }}>
      <CircularProgress size={60} />
      <Typography variant="h6" color="text.secondary">
        {messages[status] || 'Aguarde um momento...'}
      </Typography>
    </Paper>
  );
};

export default SyncingLoader;
