import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Typography, Paper, Box, List, ListItem, ListItemText, CircularProgress, Alert, Divider } from '@mui/material';
import axiosInstance from '../api/axiosInstance';

const fetchConversations = async () => {
  const { data } = await axiosInstance.get('/conversations/');
  return data.conversations; // Retorna o array de conversas
};

const HistoryPage = () => {
  const navigate = useNavigate();
  const { data: conversations, isLoading, isError, error } = useQuery({
    queryKey: ['conversations'],
    queryFn: fetchConversations,
  });

  if (isLoading) return <CircularProgress />;
  if (isError) return <Alert severity="error">Erro ao buscar histórico: {error.message}</Alert>;

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 4 }}>
        Histórico de Conversas
      </Typography>
      <Paper>
        <List sx={{ p: 0 }}>
          {conversations.map((convo, index) => (
            <React.Fragment key={convo.id}>
              <ListItem 
                onClick={() => navigate(`/history/${convo.id}`)}
                sx={{ '&:hover': { backgroundColor: 'action.hover' }, cursor: 'pointer' }}
              >
                <ListItemText
                  primary={convo.contact_name || convo.contact_id}
                  secondary={`Última mensagem: ${new Date(convo.last_message_at).toLocaleString()}`}
                />
              </ListItem>
              {index < conversations.length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>
      </Paper>
    </Box>
  );
};

export default HistoryPage;
