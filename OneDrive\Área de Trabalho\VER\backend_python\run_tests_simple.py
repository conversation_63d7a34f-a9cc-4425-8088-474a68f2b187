#!/usr/bin/env python3

"""
Testes unitários simplificados para o chat da persona (sem API keys)
"""

import unittest
import sys
import os
import time

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_persona_config():
    """Testa a configuração da persona"""
    print("🧪 Testando configuração da persona...")
    
    try:
        from config.persona import (
            SYSTEM_PROMPT, RESPONSE_TEMPLATES, EMOJIS, HASHTAGS, PERSONA_CONFIG
        )
        
        # Teste 1: System prompt
        assert isinstance(SYSTEM_PROMPT, str), "System prompt deve ser string"
        assert len(SYSTEM_PROMPT) > 100, "System prompt muito curto"
        assert "Vereadora Rafaela de Nilda" in SYSTEM_PROMPT, "Nome não encontrado"
        print("   ✅ System prompt válido")
        
        # Teste 2: Templates
        assert isinstance(RESPONSE_TEMPLATES, dict), "Templates devem ser dict"
        assert len(RESPONSE_TEMPLATES) >= 5, "Poucos templates"
        
        essential_templates = ["saudacao_geral", "assistencia_social", "medicamentos"]
        for template in essential_templates:
            assert template in RESPONSE_TEMPLATES, f"Template {template} não encontrado"
            assert "keywords" in RESPONSE_TEMPLATES[template], f"Keywords ausentes em {template}"
            assert "response" in RESPONSE_TEMPLATES[template], f"Response ausente em {template}"
        print("   ✅ Templates válidos")
        
        # Teste 3: Emojis e hashtags
        assert isinstance(EMOJIS, list), "Emojis devem ser lista"
        assert len(EMOJIS) > 5, "Poucos emojis"
        assert isinstance(HASHTAGS, list), "Hashtags devem ser lista"
        assert all(h.startswith("#") for h in HASHTAGS), "Hashtags devem começar com #"
        print("   ✅ Emojis e hashtags válidos")
        
        # Teste 4: Configuração
        assert isinstance(PERSONA_CONFIG, dict), "Config deve ser dict"
        required_configs = ["max_response_length", "emoji_probability", "temperature"]
        for config in required_configs:
            assert config in PERSONA_CONFIG, f"Config {config} ausente"
        print("   ✅ Configuração válida")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_post_processing():
    """Testa o pós-processamento"""
    print("🧪 Testando pós-processamento...")
    
    try:
        from pipelines.post_processing import PostProcessor, process_response
        
        processor = PostProcessor()
        
        # Teste 1: Limpeza de texto
        dirty_text = "  Olá,   como   você está?  \n\n  "
        clean_text = processor.clean_text(dirty_text)
        assert clean_text == "Olá, como você está?", f"Limpeza falhou: {clean_text}"
        print("   ✅ Limpeza de texto funcionando")
        
        # Teste 2: Adição de emoji
        text = "Olá, como posso ajudar?"
        text_with_emoji = processor.add_emoji(text, force=True)
        assert text != text_with_emoji, "Emoji não foi adicionado"
        print("   ✅ Adição de emoji funcionando")
        
        # Teste 3: Adição de hashtag
        text_with_hashtag = processor.add_hashtag(text, force=True)
        assert "#" in text_with_hashtag, "Hashtag não foi adicionada"
        print("   ✅ Adição de hashtag funcionando")
        
        # Teste 4: Pipeline completo
        processed = process_response("Teste de processamento")
        assert isinstance(processed, str), "Processamento deve retornar string"
        print("   ✅ Pipeline completo funcionando")
        
        # Teste 5: Seleção contextual
        health_emoji = processor._select_contextual_emoji("Vou verificar na UBS")
        assert health_emoji in processor.emojis, "Emoji contextual inválido"
        print("   ✅ Seleção contextual funcionando")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_intent_handlers():
    """Testa os handlers de intenção (sem API)"""
    print("🧪 Testando handlers de intenção...")
    
    try:
        # Mock simples do Supabase
        class MockSupabase:
            def table(self, name):
                return self
            def update(self, data):
                return self
            def eq(self, field, value):
                return self
            def execute(self):
                return self
        
        from handlers.intent_handlers import IntentHandlers
        
        mock_supabase = MockSupabase()
        handler = IntentHandlers(mock_supabase)
        
        # Teste 1: Template query
        response = handler.handle_template_query("Bom dia!")
        assert response is not None, "Template não funcionou"
        assert isinstance(response, str), "Resposta deve ser string"
        print("   ✅ Template query funcionando")
        
        # Teste 2: Template case insensitive
        response2 = handler.handle_template_query("BOM DIA")
        assert response2 is not None, "Case insensitive não funcionou"
        print("   ✅ Case insensitive funcionando")
        
        # Teste 3: Query sem match
        response3 = handler.handle_template_query("Pergunta muito específica")
        assert response3 is None, "Deveria retornar None para query sem match"
        print("   ✅ No match funcionando")
        
        # Teste 4: Fallback
        fallback = handler.get_fallback_response()
        assert isinstance(fallback, str), "Fallback deve ser string"
        assert len(fallback) > 10, "Fallback muito curto"
        print("   ✅ Fallback funcionando")
        
        # Teste 5: Cobertura de templates
        test_cases = [
            ("oi", True),
            ("cesta básica", True),
            ("remédio", True),
            ("emprego", True),
            ("query inexistente", False)
        ]
        
        for query, should_match in test_cases:
            response = handler.handle_template_query(query)
            if should_match:
                assert response is not None, f"Template deveria funcionar para '{query}'"
            else:
                assert response is None, f"Template não deveria funcionar para '{query}'"
        print("   ✅ Cobertura de templates funcionando")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_integration():
    """Testa integração entre módulos"""
    print("🧪 Testando integração...")
    
    try:
        from config.persona import RESPONSE_TEMPLATES
        from pipelines.post_processing import process_response
        
        # Teste 1: Template + pós-processamento
        for template_key, template_data in list(RESPONSE_TEMPLATES.items())[:3]:
            raw_response = template_data["response"]
            processed_response = process_response(raw_response)
            
            assert isinstance(processed_response, str), "Resposta processada deve ser string"
            assert len(processed_response) >= len(raw_response), "Processamento deve manter ou aumentar tamanho"
        print("   ✅ Template + pós-processamento funcionando")
        
        # Teste 2: Consistência da persona
        responses = []
        for template_data in list(RESPONSE_TEMPLATES.values())[:5]:
            responses.append(template_data["response"])
        
        # Verificar elementos da persona
        persona_elements = ["você", "nossa", "juntos", "equipe", "ajudar"]
        for response in responses:
            has_persona = any(element in response.lower() for element in persona_elements)
            assert has_persona, f"Resposta sem elementos da persona: {response}"
        print("   ✅ Consistência da persona funcionando")
        
        # Teste 3: Fluxo de conversa
        conversation_queries = ["Bom dia!", "Preciso de cesta básica", "Obrigado"]
        
        # Mock simples do handler
        class MockSupabase:
            def table(self, name): return self
            def update(self, data): return self
            def eq(self, field, value): return self
            def execute(self): return self
        
        from handlers.intent_handlers import IntentHandlers
        handler = IntentHandlers(MockSupabase())
        
        conversation_responses = []
        for query in conversation_queries:
            response = handler.handle_template_query(query)
            if response:
                conversation_responses.append(response)
        
        assert len(conversation_responses) >= 2, "Poucas respostas na conversa"
        print("   ✅ Fluxo de conversa funcionando")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_edge_cases():
    """Testa casos extremos"""
    print("🧪 Testando casos extremos...")
    
    try:
        from pipelines.post_processing import PostProcessor
        
        processor = PostProcessor()
        
        # Teste 1: Texto vazio
        empty_processed = processor.process_response("")
        assert isinstance(empty_processed, str), "Texto vazio deve retornar string"
        print("   ✅ Texto vazio funcionando")
        
        # Teste 2: Texto muito longo
        long_text = "Palavra " * 1000
        long_processed = processor.process_response(long_text)
        max_length = processor.config["max_response_length"]
        assert len(long_processed) <= max_length, "Texto longo não foi limitado"
        print("   ✅ Limitação de texto funcionando")
        
        # Teste 3: Texto só com espaços
        spaces_text = "   \n\n   "
        spaces_processed = processor.clean_text(spaces_text)
        assert spaces_processed == "", "Texto com espaços não foi limpo"
        print("   ✅ Limpeza de espaços funcionando")
        
        # Teste 4: Handler com query vazia
        class MockSupabase:
            def table(self, name): return self
        
        from handlers.intent_handlers import IntentHandlers
        handler = IntentHandlers(MockSupabase())
        
        empty_response = handler.handle_template_query("")
        assert empty_response is None, "Query vazia deveria retornar None"
        print("   ✅ Query vazia funcionando")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def run_all_tests():
    """Executa todos os testes simplificados"""
    print("🧪 EXECUTANDO TESTES UNITÁRIOS DO CHAT DA PERSONA")
    print("=" * 60)
    
    start_time = time.time()
    
    tests = [
        ("Configuração da Persona", test_persona_config),
        ("Pós-processamento", test_post_processing),
        ("Handlers de Intenção", test_intent_handlers),
        ("Integração", test_integration),
        ("Casos Extremos", test_edge_cases)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"   🎉 {test_name}: PASSOU")
            else:
                print(f"   ❌ {test_name}: FALHOU")
                
        except Exception as e:
            print(f"   ❌ {test_name}: ERRO - {e}")
            results.append((test_name, False))
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Relatório final
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO FINAL DOS TESTES")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"\n⏱️  Tempo de execução: {duration:.2f} segundos")
    print(f"🧪 Testes executados: {total}")
    print(f"✅ Sucessos: {passed}")
    print(f"❌ Falhas: {total - passed}")
    
    if total > 0:
        success_rate = (passed / total) * 100
        print(f"📈 Taxa de sucesso: {success_rate:.1f}%")
    
    # Detalhes dos resultados
    print(f"\n📋 DETALHES:")
    for test_name, success in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {status} - {test_name}")
    
    # Status final
    if passed == total:
        print(f"\n🎉 TODOS OS TESTES PASSARAM COM SUCESSO!")
        print("✅ O sistema de chat da persona está funcionando corretamente!")
        print("\n🎯 FUNCIONALIDADES VALIDADAS:")
        print("   👤 Persona centralizada e configurável")
        print("   🎨 Pós-processamento inteligente")
        print("   🎯 Handlers de intenção modulares")
        print("   🔄 Integração entre componentes")
        print("   🛡️  Tratamento de casos extremos")
    else:
        print(f"\n⚠️  {total - passed} TESTE(S) FALHARAM")
        print("🔧 Verifique os erros acima e corrija antes de prosseguir")
    
    print("=" * 60)
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
