import React from 'react';
import { AppBar, Too<PERSON>bar, Typography, Button, Box, Avatar, IconButton } from '@mui/material';
import { Logout as LogoutIcon, Person as PersonIcon } from '@mui/icons-material';
import useAuthStore from '../../store/authStore';
import NotificationCenter from '../NotificationCenter';

const drawerWidth = 240;

const Header = () => {
  const { logout } = useAuthStore();

  return (
    <AppBar
      position="fixed"
      sx={{
        width: `calc(100% - ${drawerWidth}px)`,
        ml: `${drawerWidth}px`,
        backgroundColor: 'background.paper',
        color: 'text.primary',
        boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.06)',
        borderBottom: '1px solid',
        borderColor: 'divider',
        backdropFilter: 'blur(8px)'
      }}
    >
      <Toolbar sx={{ minHeight: '72px !important' }}>
        <Typography
          variant="h5"
          noWrap
          component="div"
          sx={{
            flexGrow: 1,
            fontWeight: 600,
            background: 'linear-gradient(45deg, #6366f1, #8b5cf6)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}
        >
          Rafaela Admin
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <NotificationCenter />
          <Avatar sx={{ width: 36, height: 36, bgcolor: 'primary.main', mr: 1 }}>
            <PersonIcon fontSize="small" />
          </Avatar>
          <Button
            color="inherit"
            onClick={logout}
            startIcon={<LogoutIcon />}
            sx={{
              borderRadius: 2,
              px: 2,
              py: 1,
              '&:hover': {
                bgcolor: 'action.hover'
              }
            }}
          >
            Sair
          </Button>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
