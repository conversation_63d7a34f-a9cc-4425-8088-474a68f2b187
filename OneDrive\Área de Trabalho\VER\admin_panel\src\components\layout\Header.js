import React from 'react';
import { AppBar, Toolbar, Typo<PERSON>, Button, Box } from '@mui/material';
import useAuthStore from '../../store/authStore';

const drawerWidth = 240;

const Header = () => {
  const { logout } = useAuthStore();

  return (
    <AppBar
      position="fixed"
      sx={{
        width: `calc(100% - ${drawerWidth}px)`,
        ml: `${drawerWidth}px`,
        backgroundColor: 'background.paper',
        color: 'text.primary',
        boxShadow: '0px 1px 4px rgba(0, 0, 0, 0.1)'
      }}
    >
      <Toolbar>
        <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
          Painel Administrativo
        </Typography>
        <Button color="inherit" onClick={logout}>
          Sair
        </Button>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
