{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\VER\\\\admin_panel\\\\src\\\\pages\\\\WhatsAppPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport { Typography, Box, Paper, Alert, CircularProgress } from '@mui/material';\nimport axiosInstance from '../api/axiosInstance';\nimport QRCodeDisplay from '../components/whatsapp/QRCodeDisplay';\nimport SyncingLoader from '../components/whatsapp/SyncingLoader';\nimport ConversationList from '../components/whatsapp/ConversationList';\nimport ConversationDetail from '../components/ConversationDetail';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst fetchWhatsAppStatus = async () => {\n  const {\n    data\n  } = await axiosInstance.get('/whatsapp/status');\n  return data;\n};\nconst WhatsAppPage = () => {\n  _s();\n  const {\n    data,\n    isLoading,\n    isError,\n    error\n  } = useQuery({\n    queryKey: ['whatsapp-status'],\n    queryFn: fetchWhatsAppStatus,\n    refetchInterval: 3000 // Busca o status a cada 3 segundos\n  });\n  const renderContent = () => {\n    if (isLoading) {\n      return /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 14\n      }, this);\n    }\n    if (isError) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: [\"Erro ao obter status do WhatsApp: \", error.message]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 14\n      }, this);\n    }\n    const status = data === null || data === void 0 ? void 0 : data.status;\n    switch (status) {\n      case 'QR_CODE_NEEDED':\n        return /*#__PURE__*/_jsxDEV(QRCodeDisplay, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 16\n        }, this);\n      case 'INITIALIZING':\n      case 'SYNCING':\n        return /*#__PURE__*/_jsxDEV(SyncingLoader, {\n          status: status\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 16\n        }, this);\n      case 'CONNECTED':\n        return /*#__PURE__*/_jsxDEV(ConversationList, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 16\n        }, this);\n      case 'DISCONNECTED':\n      default:\n        return /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"warning\",\n          children: \"O cliente do WhatsApp est\\xE1 desconectado. Reiniciando...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      sx: {\n        mb: 4\n      },\n      children: \"WhatsApp\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: {\n          xs: 1,\n          sm: 2,\n          md: 3\n        }\n      },\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(WhatsAppPage, \"V06AixBC3YZW3DQbDfKtxCcSCGc=\", false, function () {\n  return [useQuery];\n});\n_c = WhatsAppPage;\nexport default WhatsAppPage;\nvar _c;\n$RefreshReg$(_c, \"WhatsAppPage\");", "map": {"version": 3, "names": ["React", "useState", "useQuery", "Typography", "Box", "Paper", "<PERSON><PERSON>", "CircularProgress", "axiosInstance", "QRCodeDisplay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ConversationList", "ConversationDetail", "jsxDEV", "_jsxDEV", "fetchWhatsAppStatus", "data", "get", "WhatsAppPage", "_s", "isLoading", "isError", "error", "query<PERSON><PERSON>", "queryFn", "refetchInterval", "renderContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "children", "message", "status", "variant", "sx", "mb", "p", "xs", "sm", "md", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/VER/admin_panel/src/pages/WhatsAppPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport { Typography, Box, Paper, Alert, CircularProgress } from '@mui/material';\nimport axiosInstance from '../api/axiosInstance';\nimport QRCodeDisplay from '../components/whatsapp/QRCodeDisplay';\nimport SyncingLoader from '../components/whatsapp/SyncingLoader';\nimport ConversationList from '../components/whatsapp/ConversationList';\nimport ConversationDetail from '../components/ConversationDetail';\n\nconst fetchWhatsAppStatus = async () => {\n  const { data } = await axiosInstance.get('/whatsapp/status');\n  return data;\n};\n\nconst WhatsAppPage = () => {\n  const { data, isLoading, isError, error } = useQuery({\n    queryKey: ['whatsapp-status'],\n    queryFn: fetchWhatsAppStatus,\n    refetchInterval: 3000, // Busca o status a cada 3 segundos\n  });\n\n  const renderContent = () => {\n    if (isLoading) {\n      return <CircularProgress />;\n    }\n\n    if (isError) {\n      return <Alert severity=\"error\">Erro ao obter status do WhatsApp: {error.message}</Alert>;\n    }\n\n    const status = data?.status;\n\n    switch (status) {\n      case 'QR_CODE_NEEDED':\n        return <QRCodeDisplay />;\n      case 'INITIALIZING':\n      case 'SYNCING':\n        return <SyncingLoader status={status} />;\n      case 'CONNECTED':\n        return <ConversationList />;\n      case 'DISCONNECTED':\n      default:\n        return <Alert severity=\"warning\">O cliente do WhatsApp está desconectado. Reiniciando...</Alert>;\n    }\n  };\n\n  return (\n    <Box>\n      <Typography variant=\"h4\" sx={{ mb: 4 }}>\n        WhatsApp\n      </Typography>\n      <Paper sx={{ p: { xs: 1, sm: 2, md: 3 } }}>\n        {renderContent()}\n      </Paper>\n    </Box>\n  );\n};\n\nexport default WhatsAppPage;\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,UAAU,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,gBAAgB,QAAQ,eAAe;AAC/E,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,gBAAgB,MAAM,yCAAyC;AACtE,OAAOC,kBAAkB,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;EACtC,MAAM;IAAEC;EAAK,CAAC,GAAG,MAAMR,aAAa,CAACS,GAAG,CAAC,kBAAkB,CAAC;EAC5D,OAAOD,IAAI;AACb,CAAC;AAED,MAAME,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM;IAAEH,IAAI;IAAEI,SAAS;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGpB,QAAQ,CAAC;IACnDqB,QAAQ,EAAE,CAAC,iBAAiB,CAAC;IAC7BC,OAAO,EAAET,mBAAmB;IAC5BU,eAAe,EAAE,IAAI,CAAE;EACzB,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIN,SAAS,EAAE;MACb,oBAAON,OAAA,CAACP,gBAAgB;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAC7B;IAEA,IAAIT,OAAO,EAAE;MACX,oBAAOP,OAAA,CAACR,KAAK;QAACyB,QAAQ,EAAC,OAAO;QAAAC,QAAA,GAAC,oCAAkC,EAACV,KAAK,CAACW,OAAO;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAC1F;IAEA,MAAMI,MAAM,GAAGlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkB,MAAM;IAE3B,QAAQA,MAAM;MACZ,KAAK,gBAAgB;QACnB,oBAAOpB,OAAA,CAACL,aAAa;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1B,KAAK,cAAc;MACnB,KAAK,SAAS;QACZ,oBAAOhB,OAAA,CAACJ,aAAa;UAACwB,MAAM,EAAEA;QAAO;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1C,KAAK,WAAW;QACd,oBAAOhB,OAAA,CAACH,gBAAgB;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,cAAc;MACnB;QACE,oBAAOhB,OAAA,CAACR,KAAK;UAACyB,QAAQ,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAuD;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;IACpG;EACF,CAAC;EAED,oBACEhB,OAAA,CAACV,GAAG;IAAA4B,QAAA,gBACFlB,OAAA,CAACX,UAAU;MAACgC,OAAO,EAAC,IAAI;MAACC,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,EAAC;IAExC;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbhB,OAAA,CAACT,KAAK;MAAC+B,EAAE,EAAE;QAAEE,CAAC,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE;MAAE,CAAE;MAAAT,QAAA,EACvCN,aAAa,CAAC;IAAC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACX,EAAA,CA1CID,YAAY;EAAA,QAC4BhB,QAAQ;AAAA;AAAAwC,EAAA,GADhDxB,YAAY;AA4ClB,eAAeA,YAAY;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}