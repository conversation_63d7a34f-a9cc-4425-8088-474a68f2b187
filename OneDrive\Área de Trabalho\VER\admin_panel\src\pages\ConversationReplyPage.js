import React, { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Container, Typography, Box, CircularProgress, Alert, Paper, 
  List, ListItem, ListItemText, TextField, Button, Avatar, Chip, Grid
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import PersonIcon from '@mui/icons-material/Person';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import FaceIcon from '@mui/icons-material/Face';
import axiosInstance from '../api/axiosInstance';

// --- API Fetchers and Mutators ---
const fetchMessages = async (conversationId) => {
  const { data } = await axiosInstance.get(`/conversations/${conversationId}/messages`);
  return data;
};

const postReply = async ({ conversationId, message }) => {
  const { data } = await axiosInstance.post(`/conversations/${conversationId}/reply`, { message });
  return data;
};

// --- Main Component ---
const ConversationReplyPage = () => {
  const { conversationId } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [reply, setReply] = useState('');
  const messagesEndRef = useRef(null);

  const { data: messages, isLoading, isError, error } = useQuery({
    queryKey: ['messages', conversationId],
    queryFn: () => fetchMessages(conversationId),
    enabled: !!conversationId,
  });

  const mutation = useMutation({
    mutationFn: async ({ conversationId, message }) => {
      // Enviar mensagem via API real
      const response = await fetch('http://localhost:8000/send-message', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          contact_id: conversation?.contact_id || conversationId,
          contact_name: conversation?.contact_name || 'Usuário',
          message: message,
          conversation_id: conversationId,
          channel: 'whatsapp'
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'Erro ao enviar mensagem');
      }

      return response.json();
    },
    onSuccess: (data) => {
      console.log('✅ Mensagem enviada com sucesso:', data);

      // Invalida as mensagens para buscar o histórico atualizado
      queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
      // Invalida a lista da caixa de entrada para remover esta conversa de lá
      queryClient.invalidateQueries({ queryKey: ['conversationsForAttention'] });
      // Limpa o campo de resposta
      setReply('');

      // Mostrar feedback de sucesso
      alert(`Mensagem enviada com sucesso para ${conversation?.contact_name || 'usuário'}!`);
    },
    onError: (error) => {
      console.error('❌ Erro ao enviar mensagem:', error);
      alert(`Erro ao enviar mensagem: ${error.message}`);
    }
  });

  const handleReplySubmit = (e) => {
    e.preventDefault();
    if (reply.trim()) {
      mutation.mutate({ conversationId, message: reply });
    }
  };

  // Efeito para rolar para a última mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const getSenderAvatar = (sender) => {
    if (sender === 'user') return <Avatar><PersonIcon /></Avatar>;
    if (sender === 'ai') return <Avatar sx={{ bgcolor: 'secondary.main' }}><SmartToyIcon /></Avatar>;
    if (sender === 'human') return <Avatar sx={{ bgcolor: 'success.main' }}><FaceIcon /></Avatar>;
    return <Avatar>?</Avatar>;
  };

  return (
    <Container maxWidth="md">
      <Typography variant="h4" sx={{ mb: 2 }}>Responder Conversa</Typography>
      <Paper sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
          {isLoading && <CircularProgress />}
          {isError && <Alert severity="error">Erro ao carregar mensagens: {error.message}</Alert>}
          <List>
            {messages?.map((msg) => (
              <ListItem key={msg.id}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  {getSenderAvatar(msg.sender)}
                  <Chip label={msg.sender} size="small" sx={{ ml: 1 }} />
                </Box>
                <ListItemText 
                  primary={msg.content} 
                  secondary={new Date(msg.created_at).toLocaleString('pt-BR')} 
                  sx={{ ml: 6, p: 1.5, bgcolor: 'grey.100', borderRadius: 2 }}
                />
              </ListItem>
            ))}
            <div ref={messagesEndRef} />
          </List>
        </Box>
        <Box 
          component="form" 
          onSubmit={handleReplySubmit} 
          sx={{ p: 2, borderTop: '1px solid #ddd' }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Digite sua resposta..."
                value={reply}
                onChange={(e) => setReply(e.target.value)}
                disabled={mutation.isPending}
              />
            </Grid>
            <Grid item>
              <Button 
                type="submit" 
                variant="contained" 
                endIcon={<SendIcon />}
                disabled={mutation.isPending || !reply.trim()}
              >
                {mutation.isPending ? 'Enviando...' : 'Enviar'}
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Container>
  );
};

export default ConversationReplyPage;
