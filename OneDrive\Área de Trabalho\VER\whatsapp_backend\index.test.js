const request = require('supertest');
const wppconnect = require('@wppconnect-team/wppconnect');
const axios = require('axios');
const jsonwebtoken = require('jsonwebtoken');
const { createApp } = require('./index');

// Mocking external modules
jest.mock('@wppconnect-team/wppconnect');
jest.mock('axios');

// Set a dummy secret key for testing
process.env.SECRET_KEY = 'test-secret';
process.env.PYTHON_BACKEND_URL = 'http://py-backend:8000';

describe('WhatsApp Backend API', () => {
  let mockClient;
  let app;
  const authToken = jsonwebtoken.sign({ sub: 'test-user' }, process.env.SECRET_KEY);

  beforeEach(() => {
    // Reset mocks before each test
    axios.post.mockClear();
    
    mockClient = {
      onMessage: jest.fn(),
      sendText: jest.fn().mockResolvedValue({}),
      isConnected: false,
      session: 'test-session',
    };
    
    // Create a new app instance for each test with the fresh mock client
    app = createApp(mockClient);
  });

  describe('GET /status', () => {
    it('should return disconnected status without authentication', async () => {
      const res = await request(app).get('/status');
      expect(res.statusCode).toEqual(200);
      expect(res.body).toEqual({ status: 'DISCONNECTED' });
    });

    it('should return disconnected status with mock client disconnected', async () => {
      const res = await request(app).get('/status');
      expect(res.statusCode).toEqual(200);
      expect(res.body).toEqual({ status: 'DISCONNECTED' });
    });

    it('should return connected status with mock client connected', async () => {
      // Create a new app with connected mock client
      mockClient.isConnected = true;
      app = createApp(mockClient);

      const res = await request(app).get('/status');
      expect(res.statusCode).toEqual(200);
      expect(res.body).toEqual({ status: 'CONNECTED' });
    });
  });
});
