#!/usr/bin/env python3
"""
Teste da nova funcionalidade de sincronização de conversas e mensagens.
"""
import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def print_header(title):
    """Imprime cabeçalho formatado."""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def print_success(message):
    """Imprime mensagem de sucesso."""
    print(f"✅ {message}")

def print_error(message):
    """Imprime mensagem de erro."""
    print(f"❌ {message}")

def print_info(message):
    """Imprime mensagem informativa."""
    print(f"ℹ️  {message}")

def get_auth_token():
    """Obtém token de autenticação."""
    try:
        response = requests.post(
            f"{BASE_URL}/token",
            data={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            return response.json()['access_token']
        else:
            print_error(f"Erro no login: {response.text}")
            return None
    except Exception as e:
        print_error(f"Erro na autenticação: {e}")
        return None

def test_message_endpoint_fix(token):
    """Testa se o endpoint de mensagens foi corrigido."""
    print_header("TESTE DO ENDPOINT DE MENSAGENS CORRIGIDO")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Teste 1: Contact ID do WhatsApp (deve funcionar agora)
    whatsapp_contact_id = "<EMAIL>"
    try:
        response = requests.get(f"{BASE_URL}/conversations/{whatsapp_contact_id}/messages", headers=headers)
        if response.status_code == 200:
            messages = response.json()
            print_success(f"✅ Endpoint corrigido! Status: {response.status_code}")
            print_info(f"Mensagens encontradas: {len(messages)}")
            return True
        else:
            print_error(f"Erro: {response.status_code}")
            return False
    except Exception as e:
        print_error(f"Erro ao testar endpoint: {e}")
        return False

def test_conversation_retrieval(token):
    """Testa se todas as conversas estão sendo recuperadas."""
    print_header("TESTE DE RECUPERAÇÃO DE CONVERSAS")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Buscar conversas do banco de dados
        response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
        if response.status_code == 200:
            data = response.json()
            conversations = data.get('conversations', data)
            print_success(f"Conversas recuperadas: {len(conversations)}")
            
            for i, conv in enumerate(conversations[:3]):  # Mostrar apenas as primeiras 3
                print_info(f"  {i+1}. {conv.get('contact_name', 'N/A')} ({conv.get('contact_id', 'N/A')})")
            
            return len(conversations) > 0
        else:
            print_error(f"Erro ao buscar conversas: {response.status_code}")
            return False
    except Exception as e:
        print_error(f"Erro: {e}")
        return False

def test_message_retrieval(token):
    """Testa se as mensagens das conversas estão sendo recuperadas."""
    print_header("TESTE DE RECUPERAÇÃO DE MENSAGENS")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Primeiro, buscar uma conversa existente
        conv_response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
        if conv_response.status_code != 200:
            print_error("Não foi possível buscar conversas")
            return False
            
        conv_data = conv_response.json()
        conversations = conv_data.get('conversations', conv_data)
        
        if not conversations:
            print_info("Nenhuma conversa encontrada para testar mensagens")
            return True
            
        # Testar mensagens da primeira conversa
        first_conv = conversations[0]
        conv_id = first_conv.get('id')
        contact_name = first_conv.get('contact_name', 'N/A')
        
        print_info(f"Testando mensagens da conversa: {contact_name}")
        
        msg_response = requests.get(f"{BASE_URL}/conversations/{conv_id}/messages", headers=headers)
        if msg_response.status_code == 200:
            messages = msg_response.json()
            print_success(f"Mensagens recuperadas: {len(messages)}")
            
            for i, msg in enumerate(messages[-3:]):  # Mostrar apenas as últimas 3
                sender = msg.get('sender', 'N/A')
                content = msg.get('content', '')[:50] + '...' if len(msg.get('content', '')) > 50 else msg.get('content', '')
                print_info(f"  {i+1}. {sender}: {content}")
            
            return len(messages) > 0
        else:
            print_error(f"Erro ao buscar mensagens: {msg_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erro: {e}")
        return False

def test_whatsapp_message_simulation(token):
    """Simula uma mensagem recebida do WhatsApp."""
    print_header("TESTE DE SIMULAÇÃO DE MENSAGEM DO WHATSAPP")
    
    try:
        # Simular mensagem recebida do WhatsApp
        simulated_message = {
            "from": "<EMAIL>",
            "body": "Olá! Esta é uma mensagem de teste da sincronização.",
            "timestamp": int(time.time() * 1000),
            "chatId": "<EMAIL>",
            "type": "chat"
        }
        
        response = requests.post(
            f"{BASE_URL}/whatsapp/message-received",
            json=simulated_message,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            print_success("Mensagem simulada processada com sucesso!")
            
            # Aguardar um pouco para o processamento
            time.sleep(2)
            
            # Verificar se a conversa foi criada
            headers = {"Authorization": f"Bearer {token}"}
            conv_response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
            
            if conv_response.status_code == 200:
                conv_data = conv_response.json()
                conversations = conv_data.get('conversations', conv_data)
                
                # Procurar a conversa criada
                test_conv = None
                for conv in conversations:
                    if conv.get('contact_id') == '<EMAIL>':
                        test_conv = conv
                        break
                
                if test_conv:
                    print_success("✅ Conversa criada automaticamente!")
                    
                    # Verificar mensagens
                    msg_response = requests.get(f"{BASE_URL}/conversations/{test_conv['id']}/messages", headers=headers)
                    if msg_response.status_code == 200:
                        messages = msg_response.json()
                        print_success(f"✅ {len(messages)} mensagens encontradas na conversa")
                        
                        # Verificar se tem mensagem do usuário e resposta da IA
                        user_msgs = [m for m in messages if m.get('sender') == 'user']
                        ai_msgs = [m for m in messages if m.get('sender') == 'ai']
                        
                        if user_msgs:
                            print_success("✅ Mensagem do usuário salva")
                        if ai_msgs:
                            print_success("✅ Resposta da IA gerada e salva")
                            
                        return len(user_msgs) > 0 and len(ai_msgs) > 0
                    else:
                        print_error("Erro ao buscar mensagens da conversa criada")
                        return False
                else:
                    print_error("Conversa não foi criada automaticamente")
                    return False
            else:
                print_error("Erro ao verificar conversas após simulação")
                return False
        else:
            print_error(f"Erro ao processar mensagem simulada: {response.status_code}")
            print_error(f"Resposta: {response.text}")
            return False
            
    except Exception as e:
        print_error(f"Erro na simulação: {e}")
        return False

def main():
    """Executa todos os testes de sincronização."""
    print_header("🔄 TESTE COMPLETO DE SINCRONIZAÇÃO")
    print_info(f"Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Autenticação
    token = get_auth_token()
    if not token:
        print_error("Não é possível continuar sem autenticação")
        return
    
    # 2. Teste do endpoint corrigido
    endpoint_fixed = test_message_endpoint_fix(token)
    
    # 3. Teste de recuperação de conversas
    conversations_ok = test_conversation_retrieval(token)
    
    # 4. Teste de recuperação de mensagens
    messages_ok = test_message_retrieval(token)
    
    # 5. Teste de simulação de mensagem do WhatsApp
    simulation_ok = test_whatsapp_message_simulation(token)
    
    # Resultado final
    print_header("🎉 RESULTADO DOS TESTES")
    
    print_info("📊 Status dos testes:")
    print_info(f"  Endpoint corrigido: {'✅ OK' if endpoint_fixed else '❌ Falhou'}")
    print_info(f"  Recuperação de conversas: {'✅ OK' if conversations_ok else '❌ Falhou'}")
    print_info(f"  Recuperação de mensagens: {'✅ OK' if messages_ok else '❌ Falhou'}")
    print_info(f"  Simulação WhatsApp: {'✅ OK' if simulation_ok else '❌ Falhou'}")
    
    if all([endpoint_fixed, conversations_ok, messages_ok, simulation_ok]):
        print_success("🎉 TODOS OS TESTES PASSARAM!")
        print_success("✅ Sistema de sincronização funcionando perfeitamente")
    else:
        print_info("⚠️  Alguns testes falharam, mas o sistema básico está funcionando")
        
        if endpoint_fixed:
            print_success("✅ Erro 500 corrigido com sucesso")
        if conversations_ok and messages_ok:
            print_success("✅ Recuperação de dados funcionando")
        if simulation_ok:
            print_success("✅ Processamento automático de mensagens funcionando")

if __name__ == "__main__":
    main()
