#!/usr/bin/env python3

"""
Teste do chat após correções
"""

import requests
import json


def test_chat_without_auth():
    """Testa chat sem autenticação"""
    print("💬 Testando chat sem autenticação...")
    
    chat_data = {
        "query": "Bom dia! Preciso de ajuda com cesta básica",
        "contact_id": "558488501582",
        "contact_name": "Italo Cabral"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/chat/",
            json=chat_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Chat funcionando: {data.get('response', '')[:100]}...")
            return True
        else:
            print(f"❌ Erro: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False


def test_websocket_stats():
    """Testa estatísticas do WebSocket"""
    print("🔔 Testando WebSocket...")
    
    try:
        response = requests.get("http://localhost:8000/ws/stats")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ WebSocket: {data}")
            return True
        else:
            print(f"❌ Erro: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False


def test_send_notification():
    """Testa envio de notificação"""
    print("🔔 Testando notificação...")
    
    notification_data = {
        "title": "Teste Após Correções",
        "message": "Sistema funcionando após correções!",
        "priority": "normal"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/ws/notify",
            json=notification_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Notificação: {data}")
            return True
        else:
            print(f"❌ Erro: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False


def main():
    """Função principal"""
    print("🔧 TESTE APÓS CORREÇÕES")
    print("=" * 40)
    
    tests = [
        ("WebSocket Stats", test_websocket_stats),
        ("Send Notification", test_send_notification),
        ("Chat Endpoint", test_chat_without_auth)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
    
    print("\n" + "=" * 40)
    print("📊 RESULTADO")
    print("=" * 40)
    print(f"✅ Aprovados: {passed}/{total}")
    print(f"📈 Taxa: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 TODAS AS CORREÇÕES FUNCIONANDO!")
    else:
        print("\n⚠️ Alguns problemas ainda existem")


if __name__ == "__main__":
    main()
