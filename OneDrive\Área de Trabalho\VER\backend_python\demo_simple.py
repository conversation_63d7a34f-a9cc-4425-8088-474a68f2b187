#!/usr/bin/env python3

"""
Demonstração Simplificada das funcionalidades de Memória e Cache
(Sem dependências de APIs externas)
"""

import asyncio
import time
import sys
import os

# Adicionar path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from memory.conversation_memory import ConversationMemory
from cache.intelligent_cache import IntelligentCache, CacheManager


async def demo_memory_simple():
    """Demonstra a memória conversacional de forma simplificada"""
    print("🧠 DEMONSTRAÇÃO: MEMÓRIA CONVERSACIONAL")
    print("=" * 60)
    
    memory = ConversationMemory()
    user_id = "demo_user_123"
    conversation_id = "demo_conv_456"
    
    # Simular conversa
    messages = [
        "Oi, bom dia!",
        "Meu nome é João Silva e moro no Centro",
        "Preciso de informações sobre cesta básica",
        "É urgente, por favor",
        "Obrigado pela ajuda!"
    ]
    
    print("📱 Simulando conversa:")
    print("-" * 30)
    
    for i, message in enumerate(messages, 1):
        print(f"\n👤 Usuário: {message}")
        
        # Extrair informações
        extracted = await memory.extract_user_info_from_message(user_id, message)
        if extracted:
            print(f"   🔍 Extraído: {extracted}")
        
        # Analisar intenção
        analysis = await memory.analyze_message_intent_and_entities(message, user_id)
        print(f"   🎯 Intenção: {analysis['intent']}")
        print(f"   📊 Sentimento: {analysis['sentiment']}")
        if analysis['topic'] != 'general':
            print(f"   📋 Tópico: {analysis['topic']}")
        
        time.sleep(0.3)
    
    # Mostrar perfil final
    print("\n" + "=" * 40)
    print("👤 PERFIL DO USUÁRIO:")
    profile = await memory.get_user_profile(user_id)
    print(f"   Nome: {profile.name}")
    print(f"   Bairro: {profile.neighborhood}")
    print(f"   Interações: {profile.interaction_count}")
    
    return memory


async def demo_cache_simple():
    """Demonstra o cache inteligente de forma simplificada"""
    print("\n\n⚡ DEMONSTRAÇÃO: CACHE INTELIGENTE")
    print("=" * 60)
    
    cache = IntelligentCache(max_size_mb=5, semantic_threshold=0.7)
    
    # Dados de teste
    test_data = [
        ("Como solicitar cesta básica?", "Para cesta básica, vá ao CRAS"),
        ("Onde pegar auxílio alimentação?", "Auxílio alimentação no CRAS"),
        ("Preciso de medicamentos", "Para medicamentos, procure a UBS"),
        ("Como marcar consulta?", "Para consultas, ligue para a UBS"),
        ("Informações sobre emprego", "Para empregos, consulte o SINE"),
        ("Vagas de trabalho", "Vagas disponíveis no SINE"),
        ("Matrícula escolar", "Matrícula na Secretaria de Educação"),
        ("Escola para filhos", "Escolas na Secretaria de Educação")
    ]
    
    print("📚 Populando cache...")
    for query, response in test_data:
        await cache.set(query, response)
        print(f"   ✅ {query[:30]}...")
    
    # Teste de busca exata
    print(f"\n🎯 TESTE 1: Busca Exata")
    query = "Como solicitar cesta básica?"
    start_time = time.time()
    result = await cache.get(query)
    end_time = time.time()
    
    print(f"Query: {query}")
    print(f"Resultado: {result}")
    print(f"Tempo: {(end_time - start_time)*1000:.2f}ms")
    
    # Teste de busca semântica
    print(f"\n🧠 TESTE 2: Busca Semântica")
    semantic_queries = [
        "auxílio comida",
        "remédio grátis", 
        "trabalho disponível",
        "escola criança"
    ]
    
    for query in semantic_queries:
        start_time = time.time()
        result = await cache.get(query)
        end_time = time.time()
        
        print(f"\nQuery: {query}")
        if result:
            print(f"Encontrou: {result}")
            print(f"Tempo: {(end_time - start_time)*1000:.2f}ms")
        else:
            print("Não encontrou")
    
    # Estatísticas
    print(f"\n📊 ESTATÍSTICAS:")
    stats = cache.get_stats()
    print(f"   Entradas: {stats['total_entries']}")
    print(f"   Taxa de acerto: {stats['hit_rate']:.1f}%")
    print(f"   Memória: {stats['memory_usage_mb']:.2f}MB")
    
    return cache


async def demo_performance():
    """Demonstra melhoria de performance"""
    print("\n\n📈 DEMONSTRAÇÃO: PERFORMANCE")
    print("=" * 60)
    
    cache_manager = CacheManager()
    
    # Queries de teste
    queries = [
        "Bom dia",
        "Cesta básica",
        "UBS consulta",
        "Emprego SINE",
        "Escola matrícula"
    ] * 20  # 100 queries
    
    # Popular cache
    responses = [
        "Bom dia! Como posso ajudar?",
        "Para cesta básica, vá ao CRAS",
        "Para consultas, procure a UBS",
        "Para empregos, consulte o SINE",
        "Para matrícula, vá à Secretaria"
    ]
    
    for i, query in enumerate(set(queries)):
        await cache_manager.cache_response(query, responses[i % len(responses)])
    
    # Teste com cache
    print("🚀 Testando com cache...")
    start_time = time.time()
    hits = 0
    
    for query in queries:
        result = await cache_manager.get_response(query)
        if result:
            hits += 1
    
    end_time = time.time()
    cache_time = end_time - start_time
    
    # Simular sem cache
    print("🐌 Simulando sem cache...")
    start_time = time.time()
    
    for query in queries:
        time.sleep(0.005)  # 5ms de processamento simulado
    
    end_time = time.time()
    no_cache_time = end_time - start_time
    
    # Resultados
    print(f"\n🎯 RESULTADOS:")
    print(f"   Queries: {len(queries)}")
    print(f"   Cache hits: {hits}")
    print(f"   Tempo com cache: {cache_time:.3f}s")
    print(f"   Tempo sem cache: {no_cache_time:.3f}s")
    print(f"   Melhoria: {((no_cache_time - cache_time) / no_cache_time * 100):.1f}%")
    print(f"   Speedup: {no_cache_time / cache_time:.1f}x")


async def demo_integration():
    """Demonstra integração simples"""
    print("\n\n🔗 DEMONSTRAÇÃO: INTEGRAÇÃO")
    print("=" * 60)
    
    memory = ConversationMemory()
    cache_manager = CacheManager()
    
    user_id = "integration_user"
    
    # Simular fluxo integrado
    print("💬 Fluxo integrado:")
    
    # 1. Usuário se apresenta
    message1 = "Oi, meu nome é Ana e moro em Ponta Negra"
    print(f"\n👤 {message1}")
    
    extracted = await memory.extract_user_info_from_message(user_id, message1)
    print(f"   🔍 Extraído: {extracted}")
    
    # 2. Armazenar resposta personalizada no cache
    personalized_response = f"Oi {extracted.get('name', 'Ana')}! Que bom falar com você de {extracted.get('neighborhood', 'Ponta Negra')}!"
    await cache_manager.cache_response("saudacao_personalizada", personalized_response)
    
    # 3. Buscar resposta personalizada
    cached = await cache_manager.get_response("saudacao_personalizada")
    print(f"🤖 Resposta: {cached}")
    
    # 4. Mostrar contexto personalizado
    context = await memory.get_personalized_response_context(user_id, "conv_123")
    print(f"   🎯 Contexto: Nome={context.get('user_name')}, Bairro={context.get('user_neighborhood')}")
    
    print("\n✅ Integração funcionando!")


async def main():
    """Função principal"""
    print("🚀 DEMONSTRAÇÃO SIMPLIFICADA")
    print("🎯 Memória Conversacional + Cache Inteligente")
    print("=" * 80)
    
    try:
        await demo_memory_simple()
        await demo_cache_simple()
        await demo_performance()
        await demo_integration()
        
        print("\n" + "=" * 80)
        print("🎉 DEMONSTRAÇÃO CONCLUÍDA!")
        print("=" * 80)
        print("✅ Memória conversacional: Funcionando")
        print("✅ Cache inteligente: Funcionando")
        print("✅ Busca semântica: Funcionando")
        print("✅ Integração: Funcionando")
        print("✅ Performance: Melhorada significativamente")
        
        print(f"\n📊 Dashboard de monitoramento disponível em:")
        print(f"   reports/monitoring_dashboard.html")
        
    except Exception as e:
        print(f"\n❌ Erro: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
