{"test_api.py::test_persona_assistencia_social": true, "test_api.py::test_persona_medicamentos": true, "test_api.py::test_persona_pedido_emprego": true, "test_api.py::test_persona_cirurgias_exames": true, "test_api.py::test_persona_tratamentos_dentarios": true, "test_api.py::test_persona_mensagem_fe": true, "test_api.py::test_persona_encontro_presencial": true, "test_api.py::test_ingest_document_and_list": true, "test_api.py::test_delete_non_existent_document": true, "test_api.py::test_transcribe_audio_api_error": true, "test_api.py::test_get_whatsapp_qr_code_success": true, "test_api.py::test_get_whatsapp_qr_code_error": true, "test_api.py::test_get_whatsapp_conversations_success": true, "test_api.py::test_get_whatsapp_conversations_error": true, "test_api.py::test_delete_document": true, "test_api.py::test_rag_success_with_document": true}