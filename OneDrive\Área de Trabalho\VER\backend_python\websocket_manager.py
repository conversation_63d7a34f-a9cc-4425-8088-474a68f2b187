"""
Sistema WebSocket para Notificações Instantâneas
Gerencia conexões, notificações e mensagens em tempo real
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Set, Optional
from fastapi import WebSocket, WebSocketDisconnect
from dataclasses import dataclass, asdict
import uuid


@dataclass
class WebSocketConnection:
    """Representa uma conexão WebSocket"""
    websocket: WebSocket
    client_id: str
    user_id: Optional[str] = None
    connected_at: datetime = None
    last_ping: datetime = None
    
    def __post_init__(self):
        if self.connected_at is None:
            self.connected_at = datetime.now()
        if self.last_ping is None:
            self.last_ping = datetime.now()


@dataclass
class NotificationMessage:
    """Estrutura de uma notificação"""
    type: str  # new_message, ai_response, system_alert, etc.
    title: str
    message: str
    id: str = None
    data: Dict = None
    timestamp: datetime = None
    priority: str = "normal"  # low, normal, high, urgent

    def __post_init__(self):
        if self.id is None:
            self.id = str(uuid.uuid4())
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.data is None:
            self.data = {}
    
    def to_dict(self):
        """Converte para dicionário para envio via WebSocket"""
        return {
            "id": self.id,
            "type": self.type,
            "title": self.title,
            "message": self.message,
            "data": self.data,
            "timestamp": self.timestamp.isoformat(),
            "priority": self.priority
        }


class WebSocketManager:
    """
    Gerenciador de conexões WebSocket e notificações
    """
    
    def __init__(self):
        # Armazenamento de conexões
        self.active_connections: Dict[str, WebSocketConnection] = {}
        self.user_connections: Dict[str, Set[str]] = {}  # user_id -> set of client_ids
        
        # Configurações
        self.ping_interval = 30  # segundos
        self.connection_timeout = 300  # 5 minutos
        
        # Estatísticas
        self.total_connections = 0
        self.total_messages_sent = 0
        self.total_notifications_sent = 0
        
        # Task de limpeza será iniciada quando necessário
        self._cleanup_task_started = False
        
        # Logger
        self.logger = logging.getLogger(__name__)
    
    async def connect(self, websocket: WebSocket, client_id: str = None, user_id: str = None) -> str:
        """Conecta um novo cliente WebSocket"""
        # Iniciar task de limpeza se ainda não foi iniciada
        if not self._cleanup_task_started:
            asyncio.create_task(self._cleanup_task())
            self._cleanup_task_started = True

        await websocket.accept()
        
        if client_id is None:
            client_id = str(uuid.uuid4())
        
        # Criar conexão
        connection = WebSocketConnection(
            websocket=websocket,
            client_id=client_id,
            user_id=user_id
        )
        
        # Armazenar conexão
        self.active_connections[client_id] = connection
        
        # Mapear usuário para conexão
        if user_id:
            if user_id not in self.user_connections:
                self.user_connections[user_id] = set()
            self.user_connections[user_id].add(client_id)
        
        self.total_connections += 1
        
        # Enviar confirmação de conexão
        await self._send_to_client(client_id, {
            "type": "connection_established",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat(),
            "message": "Conectado com sucesso!"
        })
        
        self.logger.info(f"Cliente {client_id} conectado (usuário: {user_id})")
        return client_id
    
    async def disconnect(self, client_id: str):
        """Desconecta um cliente"""
        if client_id in self.active_connections:
            connection = self.active_connections[client_id]
            
            # Remover do mapeamento de usuário
            if connection.user_id and connection.user_id in self.user_connections:
                self.user_connections[connection.user_id].discard(client_id)
                if not self.user_connections[connection.user_id]:
                    del self.user_connections[connection.user_id]
            
            # Remover conexão
            del self.active_connections[client_id]
            
            self.logger.info(f"Cliente {client_id} desconectado")
    
    async def send_notification(self, notification: NotificationMessage, target_users: List[str] = None, target_clients: List[str] = None):
        """Envia notificação para usuários ou clientes específicos"""
        clients_to_notify = set()
        
        # Determinar clientes alvo
        if target_users:
            for user_id in target_users:
                if user_id in self.user_connections:
                    clients_to_notify.update(self.user_connections[user_id])
        
        if target_clients:
            clients_to_notify.update(target_clients)
        
        # Se não especificou alvos, enviar para todos
        if not target_users and not target_clients:
            clients_to_notify = set(self.active_connections.keys())
        
        # Enviar notificação
        message_data = {
            "type": "notification",
            "notification": notification.to_dict()
        }
        
        successful_sends = 0
        for client_id in clients_to_notify:
            if await self._send_to_client(client_id, message_data):
                successful_sends += 1
        
        self.total_notifications_sent += successful_sends
        self.logger.info(f"Notificação enviada para {successful_sends}/{len(clients_to_notify)} clientes")
        
        return successful_sends
    
    async def send_message_notification(self, contact_name: str, message: str, conversation_id: str = None):
        """Envia notificação de nova mensagem"""
        notification = NotificationMessage(
            type="new_message",
            title="Nova Mensagem",
            message=f"{contact_name}: {message[:50]}{'...' if len(message) > 50 else ''}",
            data={
                "contact_name": contact_name,
                "full_message": message,
                "conversation_id": conversation_id
            },
            priority="high"
        )
        
        return await self.send_notification(notification)
    
    async def send_ai_response_notification(self, contact_name: str, response: str, conversation_id: str = None):
        """Envia notificação de resposta da IA"""
        notification = NotificationMessage(
            type="ai_response",
            title="Resposta Enviada",
            message=f"Resposta enviada para {contact_name}",
            data={
                "contact_name": contact_name,
                "response": response,
                "conversation_id": conversation_id
            },
            priority="normal"
        )
        
        return await self.send_notification(notification)
    
    async def send_system_alert(self, title: str, message: str, priority: str = "normal"):
        """Envia alerta do sistema"""
        notification = NotificationMessage(
            type="system_alert",
            title=title,
            message=message,
            priority=priority
        )
        
        return await self.send_notification(notification)
    
    async def broadcast_message(self, message_data: Dict):
        """Envia mensagem para todos os clientes conectados"""
        successful_sends = 0
        
        for client_id in list(self.active_connections.keys()):
            if await self._send_to_client(client_id, message_data):
                successful_sends += 1
        
        self.total_messages_sent += successful_sends
        return successful_sends
    
    async def send_to_user(self, user_id: str, message_data: Dict):
        """Envia mensagem para todas as conexões de um usuário específico"""
        if user_id not in self.user_connections:
            return 0
        
        successful_sends = 0
        client_ids = list(self.user_connections[user_id])
        
        for client_id in client_ids:
            if await self._send_to_client(client_id, message_data):
                successful_sends += 1
        
        return successful_sends
    
    async def _send_to_client(self, client_id: str, message_data: Dict) -> bool:
        """Envia mensagem para um cliente específico"""
        if client_id not in self.active_connections:
            return False
        
        connection = self.active_connections[client_id]
        
        try:
            await connection.websocket.send_text(json.dumps(message_data, default=str))
            return True
        except Exception as e:
            self.logger.error(f"Erro ao enviar mensagem para {client_id}: {e}")
            # Remover conexão com problema
            await self.disconnect(client_id)
            return False
    
    async def handle_client_message(self, client_id: str, message_data: Dict):
        """Processa mensagem recebida do cliente"""
        if client_id not in self.active_connections:
            return
        
        message_type = message_data.get("type")
        
        if message_type == "ping":
            # Responder com pong
            await self._send_to_client(client_id, {
                "type": "pong",
                "timestamp": datetime.now().isoformat()
            })
            
            # Atualizar último ping
            self.active_connections[client_id].last_ping = datetime.now()
        
        elif message_type == "subscribe_user":
            # Associar conexão a um usuário
            user_id = message_data.get("user_id")
            if user_id:
                connection = self.active_connections[client_id]
                connection.user_id = user_id
                
                if user_id not in self.user_connections:
                    self.user_connections[user_id] = set()
                self.user_connections[user_id].add(client_id)
                
                await self._send_to_client(client_id, {
                    "type": "subscription_confirmed",
                    "user_id": user_id
                })
        
        elif message_type == "mark_notification_read":
            # Marcar notificação como lida
            notification_id = message_data.get("notification_id")
            # Aqui você pode implementar lógica para marcar como lida no banco
            pass
    
    async def _cleanup_task(self):
        """Task de limpeza de conexões inativas"""
        while True:
            try:
                await asyncio.sleep(60)  # Executar a cada minuto
                
                current_time = datetime.now()
                inactive_clients = []
                
                for client_id, connection in self.active_connections.items():
                    # Verificar se conexão está inativa
                    time_since_ping = (current_time - connection.last_ping).total_seconds()
                    
                    if time_since_ping > self.connection_timeout:
                        inactive_clients.append(client_id)
                
                # Remover conexões inativas
                for client_id in inactive_clients:
                    await self.disconnect(client_id)
                    self.logger.info(f"Conexão inativa removida: {client_id}")
                
            except Exception as e:
                self.logger.error(f"Erro na limpeza de conexões: {e}")
    
    def get_stats(self) -> Dict:
        """Retorna estatísticas do WebSocket"""
        return {
            "active_connections": len(self.active_connections),
            "connected_users": len(self.user_connections),
            "total_connections": self.total_connections,
            "total_messages_sent": self.total_messages_sent,
            "total_notifications_sent": self.total_notifications_sent,
            "connections_by_user": {
                user_id: len(client_ids) 
                for user_id, client_ids in self.user_connections.items()
            }
        }
    
    def get_active_connections(self) -> List[Dict]:
        """Retorna lista de conexões ativas"""
        return [
            {
                "client_id": connection.client_id,
                "user_id": connection.user_id,
                "connected_at": connection.connected_at.isoformat(),
                "last_ping": connection.last_ping.isoformat()
            }
            for connection in self.active_connections.values()
        ]


# Instância global do gerenciador WebSocket
websocket_manager = WebSocketManager()


# Funções auxiliares para uso em outros módulos
async def notify_new_message(contact_name: str, message: str, conversation_id: str = None):
    """Função auxiliar para notificar nova mensagem"""
    return await websocket_manager.send_message_notification(contact_name, message, conversation_id)


async def notify_ai_response(contact_name: str, response: str, conversation_id: str = None):
    """Função auxiliar para notificar resposta da IA"""
    return await websocket_manager.send_ai_response_notification(contact_name, response, conversation_id)


async def notify_system_alert(title: str, message: str, priority: str = "normal"):
    """Função auxiliar para enviar alerta do sistema"""
    return await websocket_manager.send_system_alert(title, message, priority)
