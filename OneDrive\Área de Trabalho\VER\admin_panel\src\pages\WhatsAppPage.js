import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Typography, Box, Paper, Alert, CircularProgress } from '@mui/material';
import axiosInstance from '../api/axiosInstance';
import QRCodeDisplay from '../components/whatsapp/QRCodeDisplay';
import SyncingLoader from '../components/whatsapp/SyncingLoader';
import ConversationList from '../components/whatsapp/ConversationList';
import TestConversationList from '../components/whatsapp/TestConversationList';
import ConversationDetail from '../components/ConversationDetail';

const fetchWhatsAppStatus = async () => {
  const { data } = await axiosInstance.get('/whatsapp/status');
  return data;
};

const WhatsAppPage = () => {
  const [selectedConversation, setSelectedConversation] = useState(null);

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['whatsapp-status'],
    queryFn: fetchWhatsAppStatus,
    refetchInterval: 3000, // Busca o status a cada 3 segundos
  });

  const renderContent = () => {
    if (isLoading) {
      return <CircularProgress />;
    }

    if (isError) {
      return <Alert severity="error">Erro ao obter status do WhatsApp: {error.message}</Alert>;
    }

    const status = data?.status;

    switch (status) {
      case 'QR_CODE_NEEDED':
        return <QRCodeDisplay />;
      case 'INITIALIZING':
      case 'SYNCING':
        return <SyncingLoader status={status} />;
      case 'CONNECTED':
        return <ConversationList onConversationClick={setSelectedConversation} />;
      case 'DISCONNECTED':
      default:
        // USAR COMPONENTE DE TESTE PARA DEMONSTRAÇÃO
        return <TestConversationList onConversationClick={setSelectedConversation} />;
    }
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 4 }}>
        WhatsApp
      </Typography>
      <Paper sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
        {renderContent()}
      </Paper>

      <ConversationDetail
        open={!!selectedConversation}
        onClose={() => setSelectedConversation(null)}
        conversation={selectedConversation}
      />
    </Box>
  );
};

export default WhatsAppPage;

