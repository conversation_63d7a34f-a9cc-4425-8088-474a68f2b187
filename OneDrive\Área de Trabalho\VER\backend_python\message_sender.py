"""
Sistema de Envio de Mensagens
Integra com WhatsApp e outros canais para envio de mensagens
"""

import asyncio
import requests
import json
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass
import logging


@dataclass
class MessageToSend:
    """Estrutura de uma mensagem para envio"""
    contact_id: str
    contact_name: str
    message: str
    conversation_id: Optional[str] = None
    channel: str = "whatsapp"  # whatsapp, sms, email
    priority: str = "normal"  # low, normal, high, urgent
    scheduled_at: Optional[datetime] = None
    
    def to_dict(self):
        return {
            "contact_id": self.contact_id,
            "contact_name": self.contact_name,
            "message": self.message,
            "conversation_id": self.conversation_id,
            "channel": self.channel,
            "priority": self.priority,
            "scheduled_at": self.scheduled_at.isoformat() if self.scheduled_at else None
        }


class MessageSender:
    """
    Gerenciador de envio de mensagens para múltiplos canais
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Configurações do WhatsApp (WPPConnect)
        self.whatsapp_config = {
            "base_url": "http://localhost:21465",  # URL do WPPConnect
            "session": "rafaela_session",
            "token": "your_token_here"  # Configure conforme necessário
        }
        
        # Estatísticas
        self.messages_sent = 0
        self.messages_failed = 0
        self.messages_pending = []
        
        # Queue para mensagens
        self.message_queue = asyncio.Queue()
        
        # Worker será iniciado quando necessário
        self._worker_started = False
    
    async def send_message(self, message: MessageToSend) -> bool:
        """Envia mensagem através do canal especificado"""
        # Iniciar worker se ainda não foi iniciado
        if not self._worker_started:
            asyncio.create_task(self._message_worker())
            self._worker_started = True

        try:
            if message.channel == "whatsapp":
                return await self._send_whatsapp_message(message)
            elif message.channel == "sms":
                return await self._send_sms_message(message)
            elif message.channel == "email":
                return await self._send_email_message(message)
            else:
                self.logger.error(f"Canal não suportado: {message.channel}")
                return False
                
        except Exception as e:
            self.logger.error(f"Erro ao enviar mensagem: {e}")
            self.messages_failed += 1
            return False
    
    async def queue_message(self, message: MessageToSend):
        """Adiciona mensagem à fila de envio"""
        await self.message_queue.put(message)
        self.logger.info(f"Mensagem adicionada à fila: {message.contact_name}")
    
    async def send_message_to_contact(self, contact_id: str, contact_name: str, message: str, conversation_id: str = None) -> bool:
        """Função simplificada para envio de mensagem"""
        msg = MessageToSend(
            contact_id=contact_id,
            contact_name=contact_name,
            message=message,
            conversation_id=conversation_id
        )
        
        return await self.send_message(msg)
    
    async def _send_whatsapp_message(self, message: MessageToSend) -> bool:
        """Envia mensagem via WhatsApp usando WPPConnect"""
        try:
            # Preparar dados para WPPConnect
            whatsapp_data = {
                "phone": message.contact_id,
                "message": message.message,
                "isGroup": False
            }
            
            # URL do endpoint de envio
            send_url = f"{self.whatsapp_config['base_url']}/api/{self.whatsapp_config['session']}/send-message"
            
            # Headers
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.whatsapp_config['token']}"
            }
            
            # Fazer requisição
            response = requests.post(
                send_url,
                json=whatsapp_data,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success", False):
                    self.messages_sent += 1
                    self.logger.info(f"✅ Mensagem WhatsApp enviada para {message.contact_name}")
                    
                    # Salvar no banco de dados
                    await self._save_sent_message(message, "sent")
                    
                    return True
                else:
                    self.logger.error(f"❌ Falha no envio WhatsApp: {result.get('message', 'Erro desconhecido')}")
                    await self._save_sent_message(message, "failed")
                    return False
            else:
                self.logger.error(f"❌ Erro HTTP WhatsApp: {response.status_code}")
                await self._save_sent_message(message, "failed")
                return False
                
        except requests.exceptions.Timeout:
            self.logger.error("❌ Timeout ao enviar mensagem WhatsApp")
            await self._save_sent_message(message, "timeout")
            return False
        except requests.exceptions.ConnectionError:
            self.logger.error("❌ Erro de conexão com WhatsApp")
            await self._save_sent_message(message, "connection_error")
            return False
        except Exception as e:
            self.logger.error(f"❌ Erro inesperado no WhatsApp: {e}")
            await self._save_sent_message(message, "error")
            return False
    
    async def _send_sms_message(self, message: MessageToSend) -> bool:
        """Envia mensagem via SMS (implementação futura)"""
        self.logger.info(f"📱 SMS para {message.contact_name}: {message.message}")
        # Implementar integração com provedor de SMS
        return True
    
    async def _send_email_message(self, message: MessageToSend) -> bool:
        """Envia mensagem via Email (implementação futura)"""
        self.logger.info(f"📧 Email para {message.contact_name}: {message.message}")
        # Implementar integração com provedor de email
        return True
    
    async def _save_sent_message(self, message: MessageToSend, status: str):
        """Salva mensagem enviada no banco de dados"""
        try:
            # Aqui você integraria com o Supabase para salvar
            # Por enquanto, apenas log
            self.logger.info(f"💾 Mensagem salva: {message.contact_name} - Status: {status}")
            
            # Exemplo de estrutura para salvar:
            message_data = {
                "conversation_id": message.conversation_id,
                "sender": "admin",  # ou user_id do admin que enviou
                "content": message.message,
                "channel": message.channel,
                "status": status,
                "sent_at": datetime.now().isoformat()
            }
            
            # supabase.table('messages').insert(message_data).execute()
            
        except Exception as e:
            self.logger.error(f"Erro ao salvar mensagem enviada: {e}")
    
    async def _message_worker(self):
        """Worker que processa fila de mensagens"""
        while True:
            try:
                # Aguardar mensagem na fila
                message = await self.message_queue.get()
                
                # Verificar se é mensagem agendada
                if message.scheduled_at and message.scheduled_at > datetime.now():
                    # Reagendar para mais tarde
                    await asyncio.sleep(1)
                    await self.message_queue.put(message)
                    continue
                
                # Enviar mensagem
                success = await self.send_message(message)
                
                if success:
                    self.logger.info(f"✅ Mensagem processada: {message.contact_name}")
                else:
                    self.logger.error(f"❌ Falha ao processar mensagem: {message.contact_name}")
                
                # Marcar como processada
                self.message_queue.task_done()
                
                # Pequena pausa para evitar spam
                await asyncio.sleep(0.5)
                
            except Exception as e:
                self.logger.error(f"Erro no worker de mensagens: {e}")
                await asyncio.sleep(5)
    
    def get_stats(self) -> Dict:
        """Retorna estatísticas do sistema de envio"""
        return {
            "messages_sent": self.messages_sent,
            "messages_failed": self.messages_failed,
            "messages_pending": self.message_queue.qsize(),
            "success_rate": (self.messages_sent / (self.messages_sent + self.messages_failed)) * 100 if (self.messages_sent + self.messages_failed) > 0 else 0
        }
    
    async def test_whatsapp_connection(self) -> bool:
        """Testa conexão com WhatsApp"""
        try:
            status_url = f"{self.whatsapp_config['base_url']}/api/{self.whatsapp_config['session']}/status"
            
            response = requests.get(status_url, timeout=5)
            
            if response.status_code == 200:
                result = response.json()
                is_connected = result.get("connected", False)
                
                if is_connected:
                    self.logger.info("✅ WhatsApp conectado e funcionando")
                    return True
                else:
                    self.logger.warning("⚠️ WhatsApp não conectado")
                    return False
            else:
                self.logger.error(f"❌ Erro ao verificar status WhatsApp: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao testar conexão WhatsApp: {e}")
            return False
    
    async def get_whatsapp_qr_code(self) -> Optional[str]:
        """Obtém QR Code para conectar WhatsApp"""
        try:
            qr_url = f"{self.whatsapp_config['base_url']}/api/{self.whatsapp_config['session']}/qr-code"
            
            response = requests.get(qr_url, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                return result.get("qr_code")
            else:
                self.logger.error(f"Erro ao obter QR Code: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"Erro ao obter QR Code: {e}")
            return None


# Instância global do sistema de envio
message_sender = MessageSender()


# Funções auxiliares para uso em outros módulos
async def send_message_to_contact(contact_id: str, contact_name: str, message: str, conversation_id: str = None) -> bool:
    """Função auxiliar para envio de mensagem"""
    return await message_sender.send_message_to_contact(contact_id, contact_name, message, conversation_id)


async def queue_message_for_sending(contact_id: str, contact_name: str, message: str, conversation_id: str = None):
    """Função auxiliar para adicionar mensagem à fila"""
    msg = MessageToSend(
        contact_id=contact_id,
        contact_name=contact_name,
        message=message,
        conversation_id=conversation_id
    )
    await message_sender.queue_message(msg)


async def test_whatsapp_connection() -> bool:
    """Função auxiliar para testar conexão WhatsApp"""
    return await message_sender.test_whatsapp_connection()


def get_message_sender_stats() -> Dict:
    """Função auxiliar para obter estatísticas"""
    return message_sender.get_stats()
