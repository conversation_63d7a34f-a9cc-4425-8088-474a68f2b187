name: Continuous Integration

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  test-backend-python:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      - name: Install dependencies
        run: |
          python -m venv venv
          source venv/bin/activate
          pip install -r backend_python/requirements.txt
      - name: Run Python tests
        run: |
          source venv/bin/activate
          python -m pytest backend_python/

  test-frontend-react:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install frontend dependencies
        working-directory: ./frontend_admin
        run: npm install --legacy-peer-deps
      - name: Run frontend tests
        working-directory: ./frontend_admin
        run: npm test -- --watchAll=false

  test-backend-whatsapp:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install whatsapp backend dependencies
        working-directory: ./whatsapp_backend
        run: npm install
      - name: Run whatsapp backend tests
        working-directory: ./whatsapp_backend
        run: npm test
