import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useAuthStore = create(
  persist(
    (set) => ({
      authToken: null,
      user: null,
      isAuthenticated: false,

      setToken: (token) => {
        set({ authToken: token, isAuthenticated: !!token });
      },

      setUser: (userData) => {
        set({ user: userData });
      },

      logout: () => {
        set({ authToken: null, user: null, isAuthenticated: false });
      },
    }),
    {
      name: 'auth-storage', // Nome da chave no localStorage
      getStorage: () => localStorage, // Usar localStorage para persistência
    }
  )
);

export default useAuthStore;
