#!/usr/bin/env node

/**
 * Teste para verificar se as conversas estão sendo exibidas no frontend
 */

const axios = require('axios');

async function testFrontendConversations() {
    console.log('🔍 Testando exibição de conversas no frontend...\n');
    
    try {
        // 1. Fazer login
        console.log('🔐 Fazendo login...');
        const loginResponse = await axios.post('http://localhost:8000/token', 
            'username=admin&password=admin123',
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
        );
        
        const token = loginResponse.data.access_token;
        const headers = { 'Authorization': `Bearer ${token}` };
        console.log('✅ Login realizado com sucesso');
        
        // 2. Buscar conversas do backend
        console.log('\n📋 Buscando conversas do backend...');
        const conversationsResponse = await axios.get('http://localhost:8000/whatsapp/conversations', { headers });
        const conversations = conversationsResponse.data;
        
        console.log(`✅ ${conversations.length} conversas encontradas no backend`);
        
        if (conversations.length === 0) {
            console.log('⚠️  Nenhuma conversa encontrada - não há dados para testar');
            return;
        }
        
        // 3. Analisar estrutura das conversas
        console.log('\n🔍 Analisando estrutura das conversas...');
        
        let conversasComNome = 0;
        let conversasSemNome = 0;
        let conversasComId = 0;
        
        conversations.forEach((chat, index) => {
            const hasId = chat.id && chat.id._serialized;
            const hasName = chat.name;
            const hasFormattedName = chat.contact && chat.contact.formattedName;
            const hasUser = chat.id && chat.id.user;
            
            if (hasId) conversasComId++;
            if (hasName) conversasComNome++;
            else conversasSemNome++;
            
            console.log(`\n📱 Conversa ${index + 1}:`);
            console.log(`   ID: ${hasId ? '✅' : '❌'} (${chat.id?._serialized || 'N/A'})`);
            console.log(`   Nome: ${hasName ? '✅' : '❌'} (${chat.name || 'N/A'})`);
            console.log(`   Nome formatado: ${hasFormattedName ? '✅' : '❌'} (${chat.contact?.formattedName || 'N/A'})`);
            console.log(`   Usuário: ${hasUser ? '✅' : '❌'} (${chat.id?.user || 'N/A'})`);
            
            // Simular o que o frontend faria
            const displayName = chat.name || chat.contact?.formattedName || chat.id?.user || 'Contato sem nome';
            console.log(`   Nome que será exibido: "${displayName}"`);
        });
        
        // 4. Verificar filtros
        console.log('\n🔍 Testando filtros do frontend...');
        
        // Filtro antigo (problemático)
        const filtroAntigo = conversations.filter(chat => chat.id && chat.name);
        console.log(`❌ Filtro antigo (chat.id && chat.name): ${filtroAntigo.length} conversas`);
        
        // Filtro novo (corrigido)
        const filtroNovo = conversations.filter(chat => chat.id && chat.id._serialized);
        console.log(`✅ Filtro novo (chat.id && chat.id._serialized): ${filtroNovo.length} conversas`);
        
        // 5. Resumo
        console.log('\n📊 RESUMO:');
        console.log(`   Total de conversas: ${conversations.length}`);
        console.log(`   Conversas com ID válido: ${conversasComId}`);
        console.log(`   Conversas com nome: ${conversasComNome}`);
        console.log(`   Conversas sem nome: ${conversasSemNome}`);
        console.log(`   Filtro antigo passaria: ${filtroAntigo.length}`);
        console.log(`   Filtro novo passará: ${filtroNovo.length}`);
        
        // 6. Resultado
        console.log('\n🎯 RESULTADO:');
        if (filtroNovo.length > filtroAntigo.length) {
            console.log('🎉 CORREÇÃO FUNCIONOU!');
            console.log(`   Antes: ${filtroAntigo.length} conversas exibidas`);
            console.log(`   Depois: ${filtroNovo.length} conversas exibidas`);
            console.log(`   Ganho: +${filtroNovo.length - filtroAntigo.length} conversas`);
        } else if (filtroNovo.length === filtroAntigo.length && filtroNovo.length > 0) {
            console.log('✅ Filtros equivalentes, mas conversas serão exibidas');
        } else {
            console.log('⚠️  Ainda pode haver problemas com os filtros');
        }
        
        // 7. Instruções para o usuário
        console.log('\n💡 PRÓXIMOS PASSOS:');
        console.log('   1. Abra http://localhost:3000/whatsapp no browser');
        console.log('   2. Faça login com admin/admin123');
        console.log('   3. Clique em "WhatsApp" no sidebar');
        console.log('   4. Verifique se as conversas aparecem agora');
        
    } catch (error) {
        console.error('❌ Erro no teste:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Possíveis soluções:');
            console.log('   - Verifique se o backend Python está rodando (porta 8000)');
            console.log('   - Verifique se o frontend está rodando (porta 3000)');
        }
    }
}

// Executar teste
testFrontendConversations().catch(console.error);
