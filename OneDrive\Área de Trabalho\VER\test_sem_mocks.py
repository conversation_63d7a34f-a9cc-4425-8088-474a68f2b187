#!/usr/bin/env python3
"""
Teste para verificar se o sistema está funcionando sem dados mockados.
"""
import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"
WHATSAPP_URL = "http://localhost:3001"

def print_header(title):
    """Imprime cabeçalho formatado."""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def print_success(message):
    """Imprime mensagem de sucesso."""
    print(f"✅ {message}")

def print_error(message):
    """Imprime mensagem de erro."""
    print(f"❌ {message}")

def print_info(message):
    """Imprime mensagem informativa."""
    print(f"ℹ️  {message}")

def get_auth_token():
    """Obtém token de autenticação."""
    try:
        response = requests.post(
            f"{BASE_URL}/token",
            data={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            return response.json()['access_token']
        else:
            print_error(f"Erro no login: {response.text}")
            return None
    except Exception as e:
        print_error(f"Erro na autenticação: {e}")
        return None

def test_whatsapp_no_mocks(token):
    """Testa se o WhatsApp não retorna dados mockados."""
    print_header("TESTE WHATSAPP SEM DADOS MOCKADOS")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Teste 1: Status do WhatsApp
        status_response = requests.get(f"{BASE_URL}/whatsapp/status", headers=headers)
        if status_response.status_code == 200:
            status = status_response.json()
            print_info(f"WhatsApp Status: {status.get('status')}")
        
        # Teste 2: Conversas do WhatsApp (deve retornar lista vazia se não conectado)
        conv_response = requests.get(f"{BASE_URL}/whatsapp/conversations", headers=headers)
        if conv_response.status_code == 200:
            conversations = conv_response.json()
            print_info(f"Conversas do WhatsApp: {len(conversations)}")
            
            if len(conversations) == 0:
                print_success("✅ Sem dados mockados - lista vazia quando não conectado")
                return True
            else:
                # Verificar se são dados reais (não mockados)
                mock_indicators = ['João Silva', 'Maria Santos', 'Carlos Oliveira', '558486799022', '5511987654321']
                has_mocks = any(indicator in str(conversations) for indicator in mock_indicators)
                
                if has_mocks:
                    print_error("❌ Ainda contém dados mockados")
                    return False
                else:
                    print_success("✅ Conversas reais encontradas (sem mocks)")
                    return True
        else:
            print_error(f"Erro ao buscar conversas: {conv_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erro ao testar WhatsApp: {e}")
        return False

def test_database_no_mocks(token):
    """Testa se o banco não contém dados mockados."""
    print_header("TESTE BANCO DE DADOS SEM DADOS MOCKADOS")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Buscar todas as conversas
        conv_response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
        if conv_response.status_code == 200:
            data = conv_response.json()
            conversations = data.get('conversations', data)
            print_info(f"Conversas no banco: {len(conversations)}")
            
            if len(conversations) == 0:
                print_success("✅ Banco limpo - sem dados mockados")
                return True
            
            # Verificar se há dados mockados
            mock_indicators = ['João Silva', 'Maria Santos', 'Carlos Oliveira']
            mock_contacts = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
            
            has_mock_names = any(
                conv.get('contact_name') in mock_indicators 
                for conv in conversations
            )
            
            has_mock_contacts = any(
                conv.get('contact_id') in mock_contacts 
                for conv in conversations
            )
            
            if has_mock_names or has_mock_contacts:
                print_error("❌ Ainda contém dados mockados no banco")
                for conv in conversations:
                    if conv.get('contact_name') in mock_indicators or conv.get('contact_id') in mock_contacts:
                        print_error(f"  Mock encontrado: {conv.get('contact_name')} ({conv.get('contact_id')})")
                return False
            else:
                print_success("✅ Apenas dados reais no banco")
                for conv in conversations:
                    print_info(f"  Real: {conv.get('contact_name')} ({conv.get('contact_id')})")
                return True
        else:
            print_error(f"Erro ao buscar conversas: {conv_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erro: {e}")
        return False

def test_endpoint_with_mock_id(token):
    """Testa se endpoint não cria dados mockados automaticamente."""
    print_header("TESTE ENDPOINT COM ID MOCKADO")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Tentar buscar mensagens com ID mockado
        mock_id = "<EMAIL>"
        response = requests.get(f"{BASE_URL}/conversations/{mock_id}/messages", headers=headers)
        
        if response.status_code == 200:
            messages = response.json()
            print_info(f"Mensagens retornadas: {len(messages)}")
            
            if len(messages) == 0:
                print_success("✅ Endpoint não cria dados mockados automaticamente")
                return True
            else:
                print_error(f"❌ Endpoint ainda cria dados mockados: {len(messages)} mensagens")
                return False
        else:
            print_error(f"Erro no endpoint: {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erro: {e}")
        return False

def test_create_real_conversation(token):
    """Testa criação de conversa real via chat."""
    print_header("TESTE CRIAÇÃO DE CONVERSA REAL")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Criar uma conversa real via chat
        real_contact_id = f"real_test_{int(time.time())}@c.us"
        real_contact_name = "Usuário Real Teste"
        test_message = "Esta é uma mensagem real de teste"
        
        chat_response = requests.post(
            f"{BASE_URL}/chat/",
            json={
                "query": test_message,
                "contact_id": real_contact_id,
                "contact_name": real_contact_name
            },
            headers=headers
        )
        
        if chat_response.status_code == 200:
            print_success("Conversa real criada via chat")
            
            # Verificar se foi salva no banco
            time.sleep(1)
            conv_response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
            
            if conv_response.status_code == 200:
                data = conv_response.json()
                conversations = data.get('conversations', data)
                
                # Procurar nossa conversa
                found = False
                for conv in conversations:
                    if conv.get('contact_id') == real_contact_id:
                        found = True
                        print_success(f"✅ Conversa real encontrada: {conv.get('contact_name')}")
                        
                        # Verificar mensagens
                        msg_response = requests.get(f"{BASE_URL}/conversations/{conv['id']}/messages", headers=headers)
                        if msg_response.status_code == 200:
                            messages = msg_response.json()
                            print_success(f"✅ {len(messages)} mensagens reais encontradas")
                            return True
                        break
                
                if not found:
                    print_error("❌ Conversa real não foi salva")
                    return False
            else:
                print_error("Erro ao verificar conversas")
                return False
        else:
            print_error(f"Erro ao criar conversa: {chat_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erro: {e}")
        return False

def main():
    """Executa todos os testes sem dados mockados."""
    print_header("🚫 TESTE COMPLETO - SEM DADOS MOCKADOS")
    print_info(f"Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Autenticação
    token = get_auth_token()
    if not token:
        print_error("Não é possível continuar sem autenticação")
        return
    
    # 2. Teste WhatsApp sem mocks
    whatsapp_clean = test_whatsapp_no_mocks(token)
    
    # 3. Teste banco sem mocks
    database_clean = test_database_no_mocks(token)
    
    # 4. Teste endpoint não cria mocks
    endpoint_clean = test_endpoint_with_mock_id(token)
    
    # 5. Teste criação de dados reais
    real_creation = test_create_real_conversation(token)
    
    # Resultado final
    print_header("🎉 RESULTADO FINAL")
    
    print_info("📊 Status dos testes:")
    print_info(f"  WhatsApp sem mocks: {'✅ OK' if whatsapp_clean else '❌ Falhou'}")
    print_info(f"  Banco sem mocks: {'✅ OK' if database_clean else '❌ Falhou'}")
    print_info(f"  Endpoint sem mocks: {'✅ OK' if endpoint_clean else '❌ Falhou'}")
    print_info(f"  Criação de dados reais: {'✅ OK' if real_creation else '❌ Falhou'}")
    
    if all([whatsapp_clean, database_clean, endpoint_clean, real_creation]):
        print_success("🎉 SISTEMA 100% SEM DADOS MOCKADOS!")
        print_success("✅ Todos os dados simulados foram removidos")
        print_success("✅ Sistema funciona apenas com dados reais")
        print_success("✅ Pronto para uso em produção")
    else:
        print_error("⚠️  Ainda há dados mockados no sistema")
        
        if not whatsapp_clean:
            print_info("💡 WhatsApp ainda retorna dados simulados")
        if not database_clean:
            print_info("💡 Banco ainda contém dados simulados")
        if not endpoint_clean:
            print_info("💡 Endpoint ainda cria dados simulados")
        if not real_creation:
            print_info("💡 Problema na criação de dados reais")

if __name__ == "__main__":
    main()
