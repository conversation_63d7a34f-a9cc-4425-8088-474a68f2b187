"""
Pipeline de pós-processamento para respostas do chatbot
"""

import random
import re
from typing import Optional
from config.persona import EMOJIS, HASHTAGS, PERSONA_CONFIG


class PostProcessor:
    """
    Classe responsável pelo pós-processamento das respostas do chatbot.
    Adiciona emojis, hashtags e aplica formatação final.
    """
    
    def __init__(self):
        self.emojis = EMOJIS
        self.hashtags = HASHTAGS
        self.config = PERSONA_CONFIG
    
    def add_emoji(self, text: str, force: bool = False) -> str:
        """
        Adiciona emoji ao final do texto baseado na probabilidade configurada.
        
        Args:
            text: Texto original
            force: Se True, força a adição do emoji
            
        Returns:
            Texto com emoji adicionado
        """
        # Verifica se já tem emoji
        if self._has_emoji(text) and not force:
            return text
            
        # Verifica probabilidade
        if not force and random.random() > self.config["emoji_probability"]:
            return text
            
        # Seleciona emoji baseado no contexto
        emoji = self._select_contextual_emoji(text)
        
        # Adiciona emoji com espaço se necessário
        if text.endswith(' '):
            return f"{text}{emoji}"
        else:
            return f"{text} {emoji}"
    
    def add_hashtag(self, text: str, force: bool = False) -> str:
        """
        Adiciona hashtag ao final do texto baseado na probabilidade configurada.
        
        Args:
            text: Texto original
            force: Se True, força a adição da hashtag
            
        Returns:
            Texto com hashtag adicionada
        """
        # Verifica se já tem hashtag
        if self._has_hashtag(text) and not force:
            return text
            
        # Verifica probabilidade
        if not force and random.random() > self.config["hashtag_probability"]:
            return text
            
        # Seleciona hashtag baseada no contexto
        hashtag = self._select_contextual_hashtag(text)
        
        # Adiciona hashtag com espaço se necessário
        if text.endswith(' '):
            return f"{text}{hashtag}"
        else:
            return f"{text} {hashtag}"
    
    def limit_length(self, text: str) -> str:
        """
        Limita o comprimento do texto ao máximo configurado.
        
        Args:
            text: Texto original
            
        Returns:
            Texto limitado
        """
        max_length = self.config["max_response_length"]
        
        if len(text) <= max_length:
            return text
            
        # Corta no último ponto ou exclamação antes do limite
        truncated = text[:max_length]
        
        # Procura pelo último ponto de parada
        last_stop = max(
            truncated.rfind('.'),
            truncated.rfind('!'),
            truncated.rfind('?')
        )
        
        if last_stop > max_length * 0.7:  # Se encontrou um ponto após 70% do texto
            return truncated[:last_stop + 1]
        else:
            # Corta na última palavra completa
            last_space = truncated.rfind(' ')
            if last_space > 0:
                return truncated[:last_space] + "..."
            else:
                return truncated + "..."
    
    def clean_text(self, text: str) -> str:
        """
        Limpa e formata o texto removendo espaços extras e formatação desnecessária.
        
        Args:
            text: Texto original
            
        Returns:
            Texto limpo
        """
        # Remove espaços extras
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove quebras de linha múltiplas (3 ou mais), preserva quebras duplas
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        # Remove quebras de linha com apenas espaços
        text = re.sub(r'\n\s+\n', '\n\n', text)
        
        # Garante espaço após pontuação
        text = re.sub(r'([.!?])([A-Z])', r'\1 \2', text)
        
        return text
    
    def process_response(self, text: str, add_emoji: bool = True, add_hashtag: bool = True) -> str:
        """
        Aplica todo o pipeline de pós-processamento.
        
        Args:
            text: Texto original
            add_emoji: Se deve adicionar emoji
            add_hashtag: Se deve adicionar hashtag
            
        Returns:
            Texto processado
        """
        # 1. Limpar texto
        processed_text = self.clean_text(text)
        
        # 2. Limitar comprimento
        processed_text = self.limit_length(processed_text)
        
        # 3. Adicionar emoji
        if add_emoji:
            processed_text = self.add_emoji(processed_text)
        
        # 4. Adicionar hashtag
        if add_hashtag:
            processed_text = self.add_hashtag(processed_text)
        
        return processed_text
    
    def _has_emoji(self, text: str) -> bool:
        """Verifica se o texto já contém emoji."""
        return any(emoji in text for emoji in self.emojis)
    
    def _has_hashtag(self, text: str) -> bool:
        """Verifica se o texto já contém hashtag."""
        return '#' in text
    
    def _select_contextual_emoji(self, text: str) -> str:
        """
        Seleciona emoji baseado no contexto do texto.
        
        Args:
            text: Texto para análise
            
        Returns:
            Emoji selecionado
        """
        text_lower = text.lower()
        
        # Mapeamento contextual
        context_emojis = {
            'saúde': ['🏥', '💪', '💖'],
            'trabalho': ['💪', '🤝', '🙌'],
            'assistência': ['🤝', '💖', '🙏'],
            'acessibilidade': ['♿', '💪', '🤝'],
            'transparência': ['📊', '✨', '🙌'],
            'fé': ['🙏', '✨'],
            'agenda': ['🗓️', '🤝'],
            'positivo': ['✨', '🙌', '💖'],
            'default': ['✨', '🙏', '💖', '🤝']
        }
        
        # Detecta contexto
        for context, emojis in context_emojis.items():
            if context == 'default':
                continue
                
            context_keywords = {
                'saúde': ['saúde', 'médico', 'ubs', 'medicamento', 'exame'],
                'trabalho': ['trabalho', 'emprego', 'oportunidade', 'vaga'],
                'assistência': ['cras', 'assistência', 'ajuda', 'cesta'],
                'acessibilidade': ['acessibilidade', 'inclusão', 'deficiência'],
                'transparência': ['transparência', 'prestação', 'gastos'],
                'fé': ['deus', 'amém', 'abençoe', 'fé'],
                'agenda': ['agenda', 'reunião', 'encontro'],
                'positivo': ['obrigad', 'parabéns', 'sucesso', 'conquista']
            }
            
            if any(keyword in text_lower for keyword in context_keywords.get(context, [])):
                return random.choice(emojis)
        
        # Emoji padrão
        return random.choice(context_emojis['default'])
    
    def _select_contextual_hashtag(self, text: str) -> str:
        """
        Seleciona hashtag baseada no contexto do texto.
        
        Args:
            text: Texto para análise
            
        Returns:
            Hashtag selecionada
        """
        text_lower = text.lower()
        
        # Mapeamento contextual
        if any(word in text_lower for word in ['inclusão', 'acessibilidade', 'deficiência']):
            return "#AcessibilidadeParaTodos"
        elif any(word in text_lower for word in ['transparência', 'prestação', 'gastos']):
            return "#TransparênciaTotal"
        elif any(word in text_lower for word in ['saúde', 'ubs', 'médico']):
            return "#SaúdePública"
        elif any(word in text_lower for word in ['juntos', 'unidos', 'força']):
            return "#JuntosSomosMaisFortes"
        elif 'parnamirim' in text_lower:
            return "#ParnamirimRN"
        else:
            return random.choice(self.hashtags)


# Instância global do pós-processador
post_processor = PostProcessor()


def process_response(text: str, add_emoji: bool = True, add_hashtag: bool = True) -> str:
    """
    Função de conveniência para processar respostas.
    
    Args:
        text: Texto original
        add_emoji: Se deve adicionar emoji
        add_hashtag: Se deve adicionar hashtag
        
    Returns:
        Texto processado
    """
    return post_processor.process_response(text, add_emoji, add_hashtag)
