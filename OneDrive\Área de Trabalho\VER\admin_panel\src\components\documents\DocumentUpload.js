import React, { useState } from 'react';
import { Button, Input, Box, Typography, LinearProgress, Alert } from '@mui/material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstance from '../../api/axiosInstance';
import UploadFileIcon from '@mui/icons-material/UploadFile';

const uploadDocument = async (file) => {
  const formData = new FormData();
  formData.append('file', file);
  const { data } = await axiosInstance.post('/ingest-document/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data;
};

const DocumentUpload = () => {
  const [selectedFile, setSelectedFile] = useState(null);
  const queryClient = useQueryClient();

  const mutation = useMutation({
    mutationFn: uploadDocument,
    onSuccess: () => {
      // Invalida a query de documentos para forçar o refetch da lista
      queryClient.invalidateQueries({ queryKey: ['documents'] });
      setSelectedFile(null); // Limpa o arquivo selecionado após o sucesso
    },
  });

  const handleFileChange = (event) => {
    setSelectedFile(event.target.files[0]);
  };

  const handleUpload = () => {
    if (selectedFile) {
      mutation.mutate(selectedFile);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <Button
          variant="outlined"
          component="label"
          startIcon={<UploadFileIcon />}
        >
          Escolher Arquivo
          <Input type="file" hidden onChange={handleFileChange} />
        </Button>
        {selectedFile && <Typography>{selectedFile.name}</Typography>}
        <Button
          variant="contained"
          onClick={handleUpload}
          disabled={!selectedFile || mutation.isPending}
        >
          Enviar
        </Button>
      </Box>
      {mutation.isPending && <LinearProgress sx={{ mt: 2 }} />}
      {mutation.isError && (
        <Alert severity="error" sx={{ mt: 2 }}>
          Erro ao enviar o documento: {mutation.error.message}
        </Alert>
      )}
      {mutation.isSuccess && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Documento enviado com sucesso!
        </Alert>
      )}
    </Box>
  );
};

export default DocumentUpload;
