{"accessibility": {"captions": {"soda_registered_language_packs": ["pt-BR"]}}, "autofill": {"ablation_seed": "eZlp2GdgG+c="}, "breadcrumbs": {"enabled": false, "enabled_time": "13396556076698114"}, "browser": {"shortcut_migration_version": "138.0.7204.97", "whats_new": {"enabled_order": ["PdfSearchify"]}}, "chrome_labs_activation_threshold": 41, "chrome_labs_new_badge_dict": {"chrome-refresh-2023": -1, "tab-groups-save": -1}, "hardware_acceleration_mode_previous": true, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "management": {"platform": {"azure_active_directory": 0, "enterprise_mdm_win": 0}}, "network_time": {"network_time_mapping": {"local": 1752231292189.531, "network": 1752231304926.0, "ticks": 45540533998.0, "uncertainty": 10262612.0}}, "optimization_guide": {"model_store_metadata": {}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAACdiKEaEW8VRZUpvNw0kbw9EAAAABwAAABHAG8AbwBnAGwAZQAgAEMAaAByAG8AbQBlAAAAEGYAAAABAAAgAAAA/CGWzW/AUP2zSpV5NFNq+4SVJX9RLdnANPdsfdXFugQAAAAADoAAAAACAAAgAAAAUtKt7Gzkni3g0EhriL59119kcmXrxCvo8wS0BG5qIOgwAAAAi18yT1ZQ1sr4JUqVNeVCpoyAstCSZ8ZIQQKxb+1cxUuN8YsyEGX6Uk21MGkVFl1xQAAAADOXUL1Tl8hm2Lj96chs1Xq4WUzgLZ66Sd+vBSHPDpwHcs7mlkGpcqmDkMU+F6JDRpmNnLszEhdPsNCVwsUIVFo="}, "os_update_handler_enabled": true, "performance_intervention": {"last_daily_sample": "13396704891806181"}, "performance_tuning": {"last_battery_use": {"timestamp": "13396556076839411"}}, "policy": {"last_statistics_update": "*****************"}, "privacy_budget": {"meta_experiment_activation_salt": 0.*****************}, "profile": {"info_cache": {"Default": {"active_time": **********.298515, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_26", "background_apps": false, "default_avatar_fill_color": -2890755, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_given_name": "", "gaia_id": "", "gaia_name": "", "hosted_domain": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_glic_eligible": false, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "Person 1", "profile_color_seed": -********, "profile_highlight_color": -2890755, "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": [], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "session_id_generator_last_value": "********", "signin": {"active_accounts_last_emitted": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": **********, "content": "9.56.0", "format": 36}}, "tab_stats": {"discards_external": 0, "discards_frozen": 0, "discards_proactive": 0, "discards_suggested": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_frozen": 0, "reloads_proactive": 0, "reloads_suggested": 0, "reloads_urgent": 0, "total_tab_count_max": 1, "window_count_max": 1}, "ukm": {"persisted_logs": []}, "uninstall_metrics": {"installation_date2": "**********"}, "updateclientdata": {"apps": {"eeigpngbgcognadeebkilcpcaedhellh": {"cohort": "1:w59:", "cohortname": "Auto", "dlrc": 6766, "installdate": 6764, "pf": "190cb4c3-82be-43af-b5a7-c96b2c95203e"}, "efniojlnjndmcbiieegkicadnoecjjef": {"cohort": "1:18ql:", "cohortname": "Auto Stage3", "dlrc": 6766, "installdate": 6764, "pf": "f56362f7-cc48-4747-a2c1-7cc53f4dd15c"}, "ehgidpndbllacpjalkiimkbadgjfnnmc": {"cohort": "1:ofl:", "cohortname": "stable64", "dlrc": 6766, "installdate": 6764, "pf": "58b94484-ae37-4b14-b01f-2b18170fb14d"}, "gcmjkmgdlgnkkcocmoeiminaijmmjnii": {"cohort": "1:bm1:", "cohortname": "Stable", "dlrc": 6766, "fp": "1.db4723444ca14716421bc880c306291c1c138c07a19ec9a6f1cc15b2ed2db1b5", "installdate": 6764, "pf": "9e8d80ab-4736-4ebc-88f2-32dc18e6f040", "pv": "9.56.0"}, "ggkkehgbnfjpeggfpleeakpidbkibbmn": {"cohort": "1:ut9/1a0f:34p9@0.025", "cohortname": "M108 and Above", "dlrc": 6766, "installdate": 6764, "pf": "a4dc02d6-9202-4284-b50d-b88870b297ac"}, "giekcmmlnklenlaomppkphknjmnnpneh": {"cohort": "1:j5l:", "cohortname": "Auto", "dlrc": 6766, "fp": "1.3eb16d6c28b502ac4cfee8f4a148df05f4d93229fa36a71db8b08d06329ff18a", "installdate": 6764, "pf": "8ee2142b-0fbd-46fb-8795-04c9d529ae7b", "pv": "7"}, "gonpemdgkjcecdgbnaabipppbmgfggbe": {"cohort": "1:z1x:", "cohortname": "Auto", "dlrc": 6766, "installdate": 6764, "pf": "8dd117eb-ba78-4111-aba0-070097a5b9f0"}, "hajigopbbjhghbfimgkfmpenfkclmohk": {"cohort": "1:2tdl:", "cohortname": "Stable", "dlrc": 6766, "installdate": 6764, "pf": "c066ac25-0d93-46b8-875d-c194ec148aaa"}, "hfnkpimlhhgieaddgfemjhofmfblmnib": {"cohort": "1:287f:", "cohortname": "Auto full", "dlrc": 6766, "installdate": 6764, "pf": "046c2c33-3e05-4c31-a8d8-14bbf0d1305e"}, "ihnlcenocehgdaegdmhbidjhnhdchfmm": {"cohort": "1::", "cohortname": "", "dlrc": 6766, "installdate": 6764, "pf": "5887e7a6-26e9-4e17-bf9c-bc8294736449"}, "jamhcnnkihinmdlkakkaopbjbbcngflc": {"cohort": "1:wvr:", "cohortname": "Auto", "dlrc": 6766, "installdate": 6764, "pf": "a0d60972-e6e9-4dd9-ba35-a6ac8a9ad2c1"}, "jflhchccmppkfebkiaminageehmchikm": {"cohort": "1:26yf:", "cohortname": "Stable", "dlrc": 6766, "installdate": 6764, "pf": "f9792b5b-4dfc-46ba-ac6d-cee582c5dbda"}, "jflookgnkcckhobaglndicnbbgbonegd": {"cohort": "1:s7x:", "cohortname": "Auto", "dlrc": 6766, "installdate": 6764, "pf": "31fb1931-9c37-49ea-8aa0-fc6394577a0d"}, "khaoiebndkojlmppeemjhbpbandiljpe": {"cohort": "1:cux:", "cohortname": "Auto", "dlrc": 6766, "installdate": 6764, "pf": "72ec18e9-226e-4a42-943f-9c60e39da4f9"}, "kiabhabjdbkjdpjbpigfodbdjmbglcoo": {"cohort": "1:v3l:", "cohortname": "Auto", "dlrc": 6766, "fp": "1.953969d8ce784f0b3c94317f147792886bc5d9f6423ddf8617961238219a4492", "installdate": 6764, "pf": "de953c28-c3f5-4daa-af66-a8f01abe1b63", "pv": "2025.5.15.1"}, "laoigpblnllgcgjnjnllmfolckpjlhki": {"cohort": "1:10zr:", "cohortname": "Auto", "dlrc": 6766, "installdate": 6764, "pf": "15e44dc5-42d0-43ea-8445-d4bf45324141"}, "ldfkbgjbencjpgjfleiooeldhjdapggh": {"cohort": "1:2v8l:", "cohortname": "Auto", "dlrc": 6766, "installdate": 6764, "pf": "7aadbe32-de71-4ccf-bee0-fd4f3502be8c"}, "llkgjffcdpffmhiakmfcdcblohccpfmo": {"cohort": "1::", "cohortname": "", "dlrc": 6766, "installdate": 6764, "pf": "1263a06d-63c4-40bc-9271-8f72f8de66cb"}, "mcfjlbnicoclaecapilmleaelokfnijm": {"cohort": "1:2ql3:", "cohortname": "Initial upload", "dlrc": 6766, "installdate": 6764, "pf": "5667975c-e064-47eb-87cf-106818bd7984"}, "neifaoindggfcjicffkgpmnlppeffabd": {"cohort": "1:1299:", "cohortname": "Auto", "dlrc": 6766, "installdate": 6764, "pf": "03f5df53-d3d3-4dbf-87e7-6b4cd4101a2c"}, "niikhdgajlphfehepabhhblakbdgeefj": {"cohort": "1:1uh3:", "cohortname": "Auto Main Cohort.", "dlrc": 6766, "fp": "", "installdate": 6764, "max_pv": "2025.5.21.0", "pf": "85b9759c-82ab-497e-b1d5-6547943b9787", "pv": "2025.6.16.0"}, "obedbbhbpmojnkanicioggnmelmoomoc": {"cohort": "1:s6f:", "cohortname": "Auto", "dlrc": 6766, "fp": "1.f0fac1ffee516ccd1505ec8a51acfa6d9c4fca45d78de2059eceaf3dde376216", "installdate": 6764, "pf": "2d72f7cd-b90e-4826-be78-d52a875ef0e3", "pv": "20250629.778704241.14"}, "oimompecagnajdejgnnjijobebaeigek": {"cohort": "1:2qw3:", "cohortname": "Auto", "dlrc": 6766, "installdate": 6764, "pf": "694d7cbb-aa1d-47fd-b443-b32e32694d07"}, "ojhpjlocmbogdgmfpkhlaaeamibhnphh": {"cohort": "1:w0x:", "cohortname": "All users", "dlrc": 6766, "installdate": 6764, "pf": "2a69cc7c-9336-42fe-8617-07c6cb9fd31f"}, "pmagihnlncbcefglppponlgakiphldeh": {"cohort": "1:2ntr:", "cohortname": "General Release", "dlrc": 6766, "installdate": 6764, "pf": "32e5637b-8098-40f9-b9e6-2882ad118fbb"}}}, "user_experience_metrics": {"client_id2": "4db357d2-a82c-4d88-8f69-f8245323c99c", "client_id_timestamp": "**********", "initial_logs2": [{"data": "H4sIAAAAAAAAAONkkuv8u0Ci668Ak1QrC8e9x61r2YTEDY0M9Az0zAwsLfWMjEx0zUx0U1LLUnMkPrTvOsymxJqapxsarBXMxeqTmVdaIaRkqmdoqmdoZq5nopubmVyUX5yfVqJbXJKYl5JYlKIbHuxjZESEGg+GAAajAC62CguzeDMTga82SgwajAYL2CxusDhxcTAIMEgxGDFYMaQyMDTYZ4lx8bin5pVm5qV65pWk5ggs6tWR4FFgbGJk6GJkWMUINWQDI2MAQxJDFg9IuwWDA4MEgwJbAyPYGzMYGX4wLuRsYPoPA4wdTAyTmFRMUpKMTc1TjHQTLYySdU1SLCx0LdLMLHXTLIxMTI2NjJMtLZNXML3h3sR0gDFFzvLStJDf/80fra072sGgVXdiQ87t2cYrufv36t84oLvx0/bnUVp1Okd9toQICzDYTA6R7/1kseoF86nUOmmZaVw9yeVytZXeqzLleNx6H//209sxy+748xzTpPybjolvmnNVvq5JmLuUQUn3hIPLv60C7ZcWLP6+xtzF5dWu1vv61vOzN67Jemx97RjXstXCxz5If2X9aMl9d5G6S7NnY9jepHg+7gP3YgTy7SqV5ZNf5959IRRuNuNxFJPmIW37lu4d/30+88YeYhI0NLbQM9AzNzIw0TM0MNQ1MzHi5QzzPXL5gWysjhSTAKMRP2f8n92W66dfniXAKMUkwGQkyCn+8/QrseKzPQIt7FLMAq3sDxhZXjAyAgC2srUdTAIAAA==", "hash": "rHVXNCgqGIXZCpX0xmNyJdSRzhA=", "signature": "IZ7e1aOKYfJox6H82a0hc8rZv0UiT51Tla7JdCwkrds=", "timestamp": "1752231291"}, {"data": "H4sIAAAAAAAAAONkkuv8u0Ci668As9QLZo4/SzYcZhMSNDS20DPQMzcyMNEzNDDUNTOR+NC+6zCbEmtBia5TkJYEF1d4Zl5Kfnmxgl+IEJehgZ6BnpGZoYGBUREXW4WFWbyZicD1WomGhh+PTv2XV+Lzyywpyldw9AszNNU1NdRgNFjAZnGDxYmLg0GAQYrBiMGKwbf8CK9z6Ls0XudUBoYG+ywJLh731LzSzLxUz7yS1ByBRb06EjwKjBosTYwMXYwMqxih1mxgZPTi4s1VtooTTTlineDFxes44dM20Ybe2lNeXLyTtodvFjVY9+mOFxcvK9+HL2DxAJYoJg6GJBZ/P/egLA6QEywYHBgaGME+nMHI8INxIWcD038YYOxgYpzEpGKSkmRsap5ipJtoYZSsa5JiYaFrkWZmqZtmYWRiamxknGxpmbyC6Q33JqYZjClylpemhfz+b/5obd3RDgatuhMbcm7PNl7J3b9X/8YB3Y2ftj+P0qrTOeqzJURYgMFmcoh87yeLVS+YT6XWSSeXy9VWeq/KlONx6338209vxyy7489zTJPybzomvmnOVTnh4PJvq0D7pQWLv68xd3F5tav1vv61Y1zLVgsf+2DJfXeRukuzZ2PY3qR4Pu4Dya9z774QCjdr6d7x3+czb+xHpqU3IqvsSoPWGfFyhvkeufxANlZHikmA0YifM/7Pbsv10y/PEmCUYhJgMhLkFP95+pVY8dkegRZ2KWaBVvYHjGwvGJkAEOIGWi8CAAA=", "hash": "RhmoJ9fj92/KiGAfUykCMrwzPVs=", "signature": "cTe17LBUVx0VBSb0txJgkOHeuHAergFbWi70nljbrGQ=", "timestamp": "**********"}, {"data": "H4sIAAAAAAAAAONkkuv8u0Ci668Ai9QLZo4/SzYcZhMSNDS20DPQMzcyMNEzNDDUNTOR+NC+6zCbEmtBia5TkJYEF1d4Zl5Kfnmxgl+IEJehgZ6BnpGZoYGBUREXW4WFWbyZicD1WomGhh+PTv2XV+Lzyywpyldw9AszNNU1NdRgNFjAZnGDxYmLg0GAQYrBiMGKwbf8CK9z6Ls0XudUBoYG+ywJLh731LzSzLxUz7yS1ByBRb06EjwKjBosTYwMXYwMqxih1mxgZPTi4s1VtooTTTlineDFxes44dM20Ybe2lNeXLyTtodvFjVY9+mOFxcvK9+HL2DxAJYoJg6GJBZ/P/egLA6QEywYHBgaGME+nMHI8INxIWcD038YYOxgYpzEpGKSkmRsap5ipJtoYZSsa5JiYaFrkWZmqZtmYWRiamxknGxpmbyC6Q33JqYZjClylpemhfz+b/5obd3RDgatuhMbcm7PNl7J3b9X/8YB3Y2ftj+P0qrTOeqzJURYgMFmcoh87yeLVS+YT6XWSSeXy9VWeq/KlONx6338209vxyy7489zTJPybzomvmnOVTnh4PJvq0D7pQWLv68xd3F5tav1vv61Y1zLVgsf+2DJfXeRukuzZ2PY3qR4Pu4Dya9z774QCjdr6d7x3+czb+xHpqMCHxbNOjQjwYiXM8z3yOUHsrE6UkwCjEb8nPF/dluun355lgCjFJMAk5Egp/jP06/Eis/2CLSwSzELtLI/YOR4wcgMAL2VskgvAgAA", "hash": "R6oLVDUSlQIPnTUJTW5vk9Me7tw=", "signature": "3X9f69hp5q/ZBPdbB1NNOssUrgyuT5rIaIbM+GvQAVY=", "timestamp": "1752240632"}], "last_seen": {"CrashpadMetrics": "13396714087536258"}, "limited_entropy_randomization_source": "89C2E9B22402704CA0A090E7CCAF65E1", "log_finalized_record_id": 3, "log_record_id": 9, "low_entropy_source3": 1185, "machine_id": 3371030, "ongoing_logs2": [], "pseudo_low_entropy_source": 1516, "session_id": 5, "stability": {"browser_last_live_timestamp": "13396714296064921", "exited_cleanly": true, "saved_system_profile": "CPyksMMGEhExMzguMC43MjA0LjEwMS02NBjwh7rDBiIFcHQtQlIqGAoKV2luZG93cyBOVBIKMTAuMC4yNjEwMDJyCgZ4ODZfNjQQ130YgID44sr/HyIOTml0cm8gQU5WMTUtNTEoATCgBjjYBEIKCAAQABoAMgA6AE13xA1DVe5mDUNlAACAP2oYCgxHZW51aW5lSW50ZWwQoo0sGAwgASgEggEAigEAqgEGeDg2XzY0sAEBSgoNbSM6XhVkxDtgSgoNQZDythWAjX3KSgoNkrdXsxUwrvLcSgoNBQ7w9BWAjX3KUARaAggAYgRPTkdSaggIABAAOABAAIAB8Ie6wwaYAQD4AaEJgAL///////////8BiAIBkgIkNGRiMzU3ZDItYTgyYy00ZDg4LThmNjktZjgyNDUzMjNjOTljqALsC7ICmAFkHjnSllT7/zfirX7FiAAqfsiwbNubM6kLj70v2MAtsfK351oqfizFTLRUExAAPJNUH43yOKroA8plfhtjdx59eUuqaR4MRo3j+04uuJo+x+dsNWJv2UFh7INtJMhARP61EIfSoKP3rDdEROq6hd8v1sYKpqsTxvA5C92iJ0SDSYFWvWJfDgvAY+tt3egSVzaEi7j/TPMNXfECoKQLR7vg6UI=", "saved_system_profile_hash": "262C90C73F436ECBEE068DFA7837D8797FC78A51", "stats_buildtime": "1751913084", "stats_version": "138.0.7204.101-64", "system_crash_count": 0}, "unsent_log_metadata": {"initial_logs": {"sent_samples_count": 0, "unsent_persisted_size_in_kb": 2, "unsent_samples_count": 3}, "ongoing_logs": {"sent_samples_count": 0, "unsent_persisted_size_in_kb": 0, "unsent_samples_count": 0}}}, "variations_google_groups": {"Default": []}, "variations_limited_entropy_synthetic_trial_seed_v2": "40", "was": {"restarted": false}}