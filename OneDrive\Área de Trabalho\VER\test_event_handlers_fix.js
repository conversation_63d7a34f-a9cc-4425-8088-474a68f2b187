#!/usr/bin/env node

/**
 * Teste para verificar se a correção dos event handlers foi implementada corretamente
 * 
 * Este script verifica se:
 * 1. A função setupEventHandlers recebe o cliente como parâmetro
 * 2. Não há chamadas duplicadas para setupEventHandlers
 * 3. Os event handlers são configurados corretamente
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verificando correção dos event handlers...\n');

// Caminho para o arquivo corrigido
const backendPath = path.join(__dirname, 'whatsapp_backend', 'index.js');

try {
    // Ler o arquivo
    const content = fs.readFileSync(backendPath, 'utf8');
    
    console.log('✅ Arquivo index.js encontrado');
    
    // Verificações específicas
    const checks = [
        {
            name: 'Função setupEventHandlers com parâmetro client',
            pattern: /function setupEventHandlers\(client\)/,
            shouldExist: true,
            description: 'Verifica se a função recebe o cliente como parâmetro'
        },
        {
            name: 'Verificação de cliente válido',
            pattern: /if \(!client\) \{[\s\S]*?Cliente WPP não fornecido/,
            shouldExist: true,
            description: 'Verifica se há validação do parâmetro client'
        },
        {
            name: 'Event handlers usando parâmetro client',
            pattern: /client\.onMessage\(async \(message\)/,
            shouldExist: true,
            description: 'Verifica se onMessage usa o parâmetro client'
        },
        {
            name: 'Event handlers onAnyMessage usando client',
            pattern: /client\.onAnyMessage\(async \(message\)/,
            shouldExist: true,
            description: 'Verifica se onAnyMessage usa o parâmetro client'
        },
        {
            name: 'Chamada correta com wppClientInstance',
            pattern: /setupEventHandlers\(wppClientInstance\)/,
            shouldExist: true,
            description: 'Verifica se setupEventHandlers é chamado com o cliente'
        },
        {
            name: 'Função forwardMessageToPython completa',
            pattern: /async function forwardMessageToPython\(message\)[\s\S]*?await wppClientInstance\.sendText/,
            shouldExist: true,
            description: 'Verifica se forwardMessageToPython processa e responde mensagens'
        }
    ];
    
    let allPassed = true;
    
    checks.forEach((check, index) => {
        const found = check.pattern.test(content);
        const passed = found === check.shouldExist;
        
        console.log(`\n${index + 1}. ${check.name}`);
        console.log(`   ${check.description}`);
        
        if (passed) {
            console.log(`   ✅ PASSOU - ${check.shouldExist ? 'Encontrado' : 'Não encontrado'} como esperado`);
        } else {
            console.log(`   ❌ FALHOU - ${check.shouldExist ? 'Não encontrado' : 'Encontrado'} quando ${check.shouldExist ? 'deveria existir' : 'não deveria existir'}`);
            allPassed = false;
        }
    });
    
    // Verificação adicional: procurar por padrões problemáticos
    console.log('\n🔍 Verificações de problemas:');
    
    const problematicPatterns = [
        {
            name: 'setupEventHandlers sem parâmetro',
            pattern: /setupEventHandlers\(\)/,
            description: 'Chamada sem parâmetro (problemática)'
        },
        {
            name: 'wppClientInstance.onMessage duplicado',
            pattern: /wppClientInstance\.onMessage[\s\S]*?wppClientInstance\.onMessage/,
            description: 'Event handlers duplicados (problemático)'
        },
        {
            name: 'Event handlers na função startWppClient',
            pattern: /async function startWppClient[\s\S]*?wppClientInstance\.onMessage[\s\S]*?console\.log.*Nova mensagem recebida/,
            description: 'Event handlers ainda na função startWppClient (problemático)'
        }
    ];
    
    problematicPatterns.forEach((pattern, index) => {
        const found = pattern.pattern.test(content);
        console.log(`   ${index + 1}. ${pattern.name}: ${found ? '❌ ENCONTRADO (problemático)' : '✅ NÃO ENCONTRADO (bom)'}`);
        if (found) {
            allPassed = false;
        }
    });
    
    // Verificar estrutura da função forwardMessageToPython
    console.log('\n📋 Análise da função forwardMessageToPython:');
    
    const forwardFunctionMatch = content.match(/async function forwardMessageToPython\(message\) \{[\s\S]*?\n\}/);
    if (forwardFunctionMatch) {
        const functionContent = forwardFunctionMatch[0];
        
        const features = [
            { name: 'Ignora mensagens de grupo', pattern: /isGroupMsg.*return/ },
            { name: 'Valida conteúdo da mensagem', pattern: /!message\.body.*return/ },
            { name: 'Extrai informações do contato', pattern: /contactId.*contactName/ },
            { name: 'Envia para backend Python', pattern: /axios\.post.*chat/ },
            { name: 'Responde via WhatsApp', pattern: /sendText.*response\.data\.response/ },
            { name: 'Trata erros', pattern: /catch.*error/ }
        ];
        
        features.forEach(feature => {
            const hasFeature = feature.pattern.test(functionContent);
            console.log(`   ${hasFeature ? '✅' : '❌'} ${feature.name}`);
        });
    } else {
        console.log('   ❌ Função forwardMessageToPython não encontrada');
        allPassed = false;
    }
    
    // Resultado final
    console.log('\n' + '='.repeat(60));
    if (allPassed) {
        console.log('🎉 TODOS OS TESTES PASSARAM!');
        console.log('✅ Event handlers corrigidos com sucesso');
        console.log('✅ Função setupEventHandlers recebe cliente como parâmetro');
        console.log('✅ Não há chamadas duplicadas problemáticas');
        console.log('✅ forwardMessageToPython implementada corretamente');
        console.log('✅ Sistema pronto para processar mensagens quando conectado');
    } else {
        console.log('❌ ALGUNS TESTES FALHARAM');
        console.log('⚠️  Ainda podem existir problemas com event handlers');
        console.log('💡 Verifique as correções necessárias acima');
    }
    console.log('='.repeat(60));
    
    // Mostrar trecho relevante do código corrigido
    console.log('\n📝 Assinatura da função corrigida:');
    const functionMatch = content.match(/function setupEventHandlers\([^)]*\)[\s\S]{0,200}/);
    if (functionMatch) {
        console.log('```javascript');
        console.log(functionMatch[0] + '...');
        console.log('```');
    }
    
} catch (error) {
    console.error('❌ Erro ao verificar o arquivo:', error.message);
    process.exit(1);
}

console.log('\n✨ Verificação concluída!');
