import { useEffect, useState, useCallback } from 'react';
import wsClient from '../utils/websocket';

/**
 * Hook para usar WebSocket com notificações em tempo real
 */
export const useWebSocket = () => {
  const [isConnected, setIsConnected] = useState(false);
  const [lastMessage, setLastMessage] = useState(null);
  const [notifications, setNotifications] = useState([]);

  useEffect(() => {
    // Conectar ao WebSocket
    wsClient.connect();

    // Listeners
    const handleConnected = () => {
      setIsConnected(true);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
    };

    const handleMessage = (data) => {
      setLastMessage(data);
    };

    const handleNewMessage = (data) => {
      console.log('📨 Nova mensagem:', data);
      setNotifications(prev => [...prev, {
        id: Date.now(),
        type: 'message',
        title: `Nova mensagem de ${data.contact_name}`,
        message: data.message,
        timestamp: data.timestamp
      }]);
    };

    const handleStatusChange = (data) => {
      console.log('📊 Mudança de status:', data);
      setNotifications(prev => [...prev, {
        id: Date.now(),
        type: 'status',
        title: 'Status do WhatsApp',
        message: `Status alterado para: ${data.status}`,
        timestamp: data.timestamp
      }]);
    };

    // Registrar listeners
    wsClient.on('connected', handleConnected);
    wsClient.on('disconnected', handleDisconnected);
    wsClient.on('message', handleMessage);
    wsClient.on('new_message', handleNewMessage);
    wsClient.on('status_change', handleStatusChange);

    // Cleanup
    return () => {
      wsClient.off('connected', handleConnected);
      wsClient.off('disconnected', handleDisconnected);
      wsClient.off('message', handleMessage);
      wsClient.off('new_message', handleNewMessage);
      wsClient.off('status_change', handleStatusChange);
    };
  }, []);

  const sendMessage = useCallback((message) => {
    wsClient.send(message);
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const removeNotification = useCallback((id) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  }, []);

  return {
    isConnected,
    lastMessage,
    notifications,
    sendMessage,
    clearNotifications,
    removeNotification
  };
};

export default useWebSocket;
