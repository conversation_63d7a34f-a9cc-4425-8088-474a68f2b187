"""
Sistema de Memória Conversacional Inteligente
Mantém contexto e personalização entre conversas
"""

import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from collections import defaultdict
import re


@dataclass
class UserProfile:
    """Perfil completo do usuário"""
    user_id: str
    name: Optional[str] = None
    neighborhood: Optional[str] = None
    phone: Optional[str] = None
    preferred_tone: str = "friendly"  # friendly, formal, casual
    active_hours: List[str] = None
    interaction_count: int = 0
    first_interaction: Optional[datetime] = None
    last_interaction: Optional[datetime] = None
    satisfaction_score: float = 0.0
    
    def __post_init__(self):
        if self.active_hours is None:
            self.active_hours = []
        if self.first_interaction is None:
            self.first_interaction = datetime.now()


@dataclass
class ConversationContext:
    """Contexto de uma conversa específica"""
    conversation_id: str
    user_id: str
    topic: Optional[str] = None
    intent: Optional[str] = None
    entities: Dict[str, Any] = None
    sentiment: str = "neutral"  # positive, negative, neutral
    urgency_level: str = "normal"  # low, normal, high, urgent
    resolution_status: str = "ongoing"  # ongoing, resolved, escalated
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.entities is None:
            self.entities = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class InteractionHistory:
    """Histórico de interações do usuário"""
    user_id: str
    topics_discussed: List[str] = None
    services_requested: List[str] = None
    common_questions: List[str] = None
    preferred_response_style: str = "detailed"  # brief, detailed, step_by_step
    successful_resolutions: int = 0
    escalations_to_human: int = 0
    average_session_duration: float = 0.0
    
    def __post_init__(self):
        if self.topics_discussed is None:
            self.topics_discussed = []
        if self.services_requested is None:
            self.services_requested = []
        if self.common_questions is None:
            self.common_questions = []


class ConversationMemory:
    """
    Sistema principal de memória conversacional
    """
    
    def __init__(self, supabase_client=None):
        self.supabase = supabase_client
        self.user_profiles: Dict[str, UserProfile] = {}
        self.conversation_contexts: Dict[str, ConversationContext] = {}
        self.interaction_histories: Dict[str, InteractionHistory] = {}
        self.session_cache: Dict[str, Dict] = {}
        
        # Configurações
        self.memory_retention_days = 90
        self.max_context_messages = 10
        self.cleanup_interval_hours = 24
    
    async def get_user_profile(self, user_id: str) -> UserProfile:
        """Obtém ou cria perfil do usuário"""
        if user_id not in self.user_profiles:
            # Tentar carregar do banco
            profile_data = await self._load_user_profile_from_db(user_id)
            
            if profile_data:
                self.user_profiles[user_id] = UserProfile(**profile_data)
            else:
                # Criar novo perfil
                self.user_profiles[user_id] = UserProfile(user_id=user_id)
                await self._save_user_profile_to_db(self.user_profiles[user_id])
        
        return self.user_profiles[user_id]
    
    async def update_user_profile(self, user_id: str, **updates) -> UserProfile:
        """Atualiza perfil do usuário"""
        profile = await self.get_user_profile(user_id)
        
        for key, value in updates.items():
            if hasattr(profile, key):
                setattr(profile, key, value)
        
        profile.last_interaction = datetime.now()
        profile.interaction_count += 1
        
        await self._save_user_profile_to_db(profile)
        return profile
    
    async def extract_user_info_from_message(self, user_id: str, message: str) -> Dict[str, Any]:
        """Extrai informações do usuário da mensagem"""
        extracted_info = {}
        
        # Extrair nome
        name_patterns = [
            r"meu nome é (\w+)",
            r"me chamo (\w+)",
            r"sou (\w+)",
            r"eu sou (\w+)"
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, message.lower())
            if match:
                extracted_info['name'] = match.group(1).title()
                break
        
        # Extrair bairro
        neighborhood_patterns = [
            r"moro no? ([A-Za-zÀ-ÿ\s]+)",
            r"do bairro ([A-Za-zÀ-ÿ\s]+)",
            r"aqui no? ([A-Za-zÀ-ÿ\s]+)",
            r"sou do? ([A-Za-zÀ-ÿ\s]+)",
            r"em ([A-Za-zÀ-ÿ\s]+)"
        ]
        
        for pattern in neighborhood_patterns:
            match = re.search(pattern, message.lower())
            if match:
                neighborhood = match.group(1).strip().title()
                # Limpar palavras comuns que não são bairros
                if neighborhood not in ['E', 'O', 'A', 'Do', 'Da', 'No', 'Na']:
                    extracted_info['neighborhood'] = neighborhood
                break
        
        # Detectar tom preferido
        if any(word in message.lower() for word in ["por favor", "obrigado", "agradeço"]):
            extracted_info['preferred_tone'] = "formal"
        elif any(word in message.lower() for word in ["oi", "opa", "beleza"]):
            extracted_info['preferred_tone'] = "casual"
        
        # Atualizar perfil se encontrou informações
        if extracted_info:
            await self.update_user_profile(user_id, **extracted_info)
        
        return extracted_info
    
    async def get_conversation_context(self, conversation_id: str, user_id: str) -> ConversationContext:
        """Obtém contexto da conversa"""
        if conversation_id not in self.conversation_contexts:
            # Tentar carregar do banco
            context_data = await self._load_conversation_context_from_db(conversation_id)
            
            if context_data:
                self.conversation_contexts[conversation_id] = ConversationContext(**context_data)
            else:
                # Criar novo contexto
                self.conversation_contexts[conversation_id] = ConversationContext(
                    conversation_id=conversation_id,
                    user_id=user_id
                )
                await self._save_conversation_context_to_db(self.conversation_contexts[conversation_id])
        
        return self.conversation_contexts[conversation_id]
    
    async def update_conversation_context(self, conversation_id: str, **updates) -> ConversationContext:
        """Atualiza contexto da conversa"""
        if conversation_id in self.conversation_contexts:
            context = self.conversation_contexts[conversation_id]
            
            for key, value in updates.items():
                if hasattr(context, key):
                    setattr(context, key, value)
            
            context.updated_at = datetime.now()
            await self._save_conversation_context_to_db(context)
            
            return context
        
        return None
    
    async def analyze_message_intent_and_entities(self, message: str, user_id: str) -> Dict[str, Any]:
        """Analisa intenção e entidades da mensagem"""
        analysis = {
            'intent': 'general',
            'entities': {},
            'sentiment': 'neutral',
            'urgency_level': 'normal',
            'topic': 'general'
        }
        
        message_lower = message.lower()
        
        # Detectar intenção
        intent_patterns = {
            'greeting': ['oi', 'olá', 'bom dia', 'boa tarde', 'boa noite'],
            'help_request': ['preciso', 'quero', 'como faço', 'me ajuda'],
            'information': ['qual', 'onde', 'quando', 'como'],
            'complaint': ['problema', 'não funciona', 'ruim', 'demora'],
            'gratitude': ['obrigado', 'obrigada', 'agradeço', 'valeu'],
            'goodbye': ['tchau', 'até logo', 'obrigado', 'é isso']
        }
        
        for intent, keywords in intent_patterns.items():
            if any(keyword in message_lower for keyword in keywords):
                analysis['intent'] = intent
                break
        
        # Detectar tópico
        topic_patterns = {
            'health': ['saúde', 'médico', 'consulta', 'remédio', 'ubs', 'hospital'],
            'social_assistance': ['cesta básica', 'auxílio', 'benefício', 'cras'],
            'employment': ['emprego', 'trabalho', 'vaga', 'curso'],
            'education': ['escola', 'educação', 'matrícula', 'creche'],
            'infrastructure': ['buraco', 'rua', 'iluminação', 'lixo'],
            'documentation': ['documento', 'certidão', 'cpf', 'rg']
        }
        
        for topic, keywords in topic_patterns.items():
            if any(keyword in message_lower for keyword in keywords):
                analysis['topic'] = topic
                break
        
        # Detectar sentimento
        positive_words = ['bom', 'ótimo', 'excelente', 'obrigado', 'parabéns']
        negative_words = ['ruim', 'problema', 'difícil', 'demora', 'não funciona']
        
        if any(word in message_lower for word in positive_words):
            analysis['sentiment'] = 'positive'
        elif any(word in message_lower for word in negative_words):
            analysis['sentiment'] = 'negative'
        
        # Detectar urgência
        urgent_words = ['urgente', 'rápido', 'agora', 'emergência', 'preciso muito']
        if any(word in message_lower for word in urgent_words):
            analysis['urgency_level'] = 'urgent'
        elif analysis['sentiment'] == 'negative':
            analysis['urgency_level'] = 'high'
        
        # Extrair entidades específicas
        entities = {}
        
        # Números de telefone
        phone_pattern = r'(\d{10,11})'
        phone_match = re.search(phone_pattern, message)
        if phone_match:
            entities['phone'] = phone_match.group(1)
        
        # CPF
        cpf_pattern = r'(\d{3}\.?\d{3}\.?\d{3}-?\d{2})'
        cpf_match = re.search(cpf_pattern, message)
        if cpf_match:
            entities['cpf'] = cpf_match.group(1)
        
        analysis['entities'] = entities
        
        return analysis
    
    async def get_personalized_response_context(self, user_id: str, conversation_id: str) -> Dict[str, Any]:
        """Obtém contexto personalizado para resposta"""
        profile = await self.get_user_profile(user_id)
        context = await self.get_conversation_context(conversation_id, user_id)
        history = await self.get_interaction_history(user_id)
        
        return {
            'user_name': profile.name,
            'user_neighborhood': profile.neighborhood,
            'preferred_tone': profile.preferred_tone,
            'interaction_count': profile.interaction_count,
            'current_topic': context.topic,
            'current_sentiment': context.sentiment,
            'urgency_level': context.urgency_level,
            'recent_topics': history.topics_discussed[-3:] if history.topics_discussed else [],
            'common_services': history.services_requested[-3:] if history.services_requested else [],
            'response_style': history.preferred_response_style
        }
    
    async def get_interaction_history(self, user_id: str) -> InteractionHistory:
        """Obtém histórico de interações"""
        if user_id not in self.interaction_histories:
            # Tentar carregar do banco
            history_data = await self._load_interaction_history_from_db(user_id)
            
            if history_data:
                self.interaction_histories[user_id] = InteractionHistory(**history_data)
            else:
                # Criar novo histórico
                self.interaction_histories[user_id] = InteractionHistory(user_id=user_id)
                await self._save_interaction_history_to_db(self.interaction_histories[user_id])
        
        return self.interaction_histories[user_id]
    
    async def update_interaction_history(self, user_id: str, **updates) -> InteractionHistory:
        """Atualiza histórico de interações"""
        history = await self.get_interaction_history(user_id)
        
        for key, value in updates.items():
            if hasattr(history, key):
                if isinstance(getattr(history, key), list):
                    # Para listas, adicionar sem duplicar
                    current_list = getattr(history, key)
                    if value not in current_list:
                        current_list.append(value)
                        # Manter apenas os últimos 10 itens
                        if len(current_list) > 10:
                            current_list.pop(0)
                else:
                    setattr(history, key, value)
        
        await self._save_interaction_history_to_db(history)
        return history
    
    async def cleanup_old_data(self):
        """Remove dados antigos para otimizar performance"""
        cutoff_date = datetime.now() - timedelta(days=self.memory_retention_days)
        
        # Limpar contextos antigos
        old_contexts = [
            conv_id for conv_id, context in self.conversation_contexts.items()
            if context.updated_at < cutoff_date
        ]
        
        for conv_id in old_contexts:
            del self.conversation_contexts[conv_id]
            await self._delete_conversation_context_from_db(conv_id)
        
        print(f"🧹 Limpeza de memória: {len(old_contexts)} contextos antigos removidos")
    
    # Métodos de persistência (implementação com Supabase)
    async def _load_user_profile_from_db(self, user_id: str) -> Optional[Dict]:
        """Carrega perfil do usuário do banco"""
        if not self.supabase:
            return None
        
        try:
            result = self.supabase.table('user_profiles').select('*').eq('user_id', user_id).execute()
            if result.data:
                return result.data[0]
        except Exception as e:
            print(f"Erro ao carregar perfil: {e}")
        
        return None
    
    async def _save_user_profile_to_db(self, profile: UserProfile):
        """Salva perfil do usuário no banco"""
        if not self.supabase:
            return
        
        try:
            profile_dict = asdict(profile)
            # Converter datetime para string
            if profile_dict['first_interaction']:
                profile_dict['first_interaction'] = profile_dict['first_interaction'].isoformat()
            if profile_dict['last_interaction']:
                profile_dict['last_interaction'] = profile_dict['last_interaction'].isoformat()
            
            self.supabase.table('user_profiles').upsert(profile_dict).execute()
        except Exception as e:
            print(f"Erro ao salvar perfil: {e}")
    
    async def _load_conversation_context_from_db(self, conversation_id: str) -> Optional[Dict]:
        """Carrega contexto da conversa do banco"""
        if not self.supabase:
            return None
        
        try:
            result = self.supabase.table('conversation_contexts').select('*').eq('conversation_id', conversation_id).execute()
            if result.data:
                return result.data[0]
        except Exception as e:
            print(f"Erro ao carregar contexto: {e}")
        
        return None
    
    async def _save_conversation_context_to_db(self, context: ConversationContext):
        """Salva contexto da conversa no banco"""
        if not self.supabase:
            return
        
        try:
            context_dict = asdict(context)
            # Converter datetime para string
            if context_dict['created_at']:
                context_dict['created_at'] = context_dict['created_at'].isoformat()
            if context_dict['updated_at']:
                context_dict['updated_at'] = context_dict['updated_at'].isoformat()
            
            self.supabase.table('conversation_contexts').upsert(context_dict).execute()
        except Exception as e:
            print(f"Erro ao salvar contexto: {e}")
    
    async def _load_interaction_history_from_db(self, user_id: str) -> Optional[Dict]:
        """Carrega histórico de interações do banco"""
        if not self.supabase:
            return None
        
        try:
            result = self.supabase.table('interaction_histories').select('*').eq('user_id', user_id).execute()
            if result.data:
                return result.data[0]
        except Exception as e:
            print(f"Erro ao carregar histórico: {e}")
        
        return None
    
    async def _save_interaction_history_to_db(self, history: InteractionHistory):
        """Salva histórico de interações no banco"""
        if not self.supabase:
            return
        
        try:
            history_dict = asdict(history)
            self.supabase.table('interaction_histories').upsert(history_dict).execute()
        except Exception as e:
            print(f"Erro ao salvar histórico: {e}")
    
    async def _delete_conversation_context_from_db(self, conversation_id: str):
        """Remove contexto da conversa do banco"""
        if not self.supabase:
            return
        
        try:
            self.supabase.table('conversation_contexts').delete().eq('conversation_id', conversation_id).execute()
        except Exception as e:
            print(f"Erro ao deletar contexto: {e}")


# Instância global da memória conversacional
conversation_memory = ConversationMemory()
