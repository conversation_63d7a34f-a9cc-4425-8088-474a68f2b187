# Usando uma imagem base do Node.js para desenvolvimento
FROM node:16-alpine

# Define o diretório de trabalho no contêiner
WORKDIR /app

# Copia o package.json e o package-lock.json
COPY package.json ./
COPY package-lock.json ./

# Instala as dependências
RUN npm install

# Copia o restante do código da aplicação
COPY . .

# Expõe a porta que o servidor de desenvolvimento do React usa
EXPOSE 3000

# Comando para iniciar o servidor de desenvolvimento
CMD ["npm", "start"]
