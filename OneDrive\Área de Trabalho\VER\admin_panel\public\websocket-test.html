<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste WebSocket</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        input[type="text"] {
            padding: 8px;
            width: 300px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>🔌 Teste de Conexão WebSocket</h1>
    
    <div id="status" class="status disconnected">
        ❌ Desconectado
    </div>
    
    <div>
        <button id="connectBtn" class="btn-primary" onclick="connect()">Conectar</button>
        <button id="disconnectBtn" class="btn-secondary" onclick="disconnect()" disabled>Desconectar</button>
        <button class="btn-secondary" onclick="clearLog()">Limpar Log</button>
    </div>
    
    <div style="margin: 20px 0;">
        <input type="text" id="messageInput" placeholder="Digite uma mensagem..." />
        <button class="btn-success" onclick="sendMessage()">Enviar</button>
    </div>
    
    <h3>📋 Log de Conexão:</h3>
    <div id="log" class="log"></div>

    <script>
        let ws = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateStatus(connected) {
            const statusElement = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            if (connected) {
                statusElement.textContent = '✅ Conectado';
                statusElement.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
            } else {
                statusElement.textContent = '❌ Desconectado';
                statusElement.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
            }
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('⚠️ Já conectado');
                return;
            }

            const wsUrl = 'ws://localhost:8000/ws';
            log(`🔌 Tentando conectar em: ${wsUrl}`);
            
            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = function(event) {
                    log('✅ Conexão WebSocket estabelecida');
                    updateStatus(true);
                    reconnectAttempts = 0;
                    
                    // Enviar ping inicial
                    setTimeout(() => {
                        if (ws && ws.readyState === WebSocket.OPEN) {
                            ws.send('ping');
                            log('📤 Ping enviado');
                        }
                    }, 1000);
                };

                ws.onmessage = function(event) {
                    log(`📨 Mensagem recebida: ${event.data}`);
                    
                    try {
                        const data = JSON.parse(event.data);
                        log(`📨 JSON recebido: ${JSON.stringify(data, null, 2)}`);
                    } catch (e) {
                        // Não é JSON, apenas texto
                    }
                };

                ws.onclose = function(event) {
                    log(`❌ Conexão fechada. Código: ${event.code}, Razão: ${event.reason}`);
                    updateStatus(false);
                    
                    if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        log(`🔄 Tentativa de reconexão ${reconnectAttempts}/${maxReconnectAttempts} em 3 segundos...`);
                        setTimeout(connect, 3000);
                    }
                };

                ws.onerror = function(error) {
                    log(`❌ Erro WebSocket: ${error}`);
                    console.error('WebSocket error:', error);
                };

            } catch (error) {
                log(`❌ Erro ao criar WebSocket: ${error}`);
                console.error('Error creating WebSocket:', error);
            }
        }

        function disconnect() {
            if (ws) {
                ws.close(1000, 'Desconexão manual');
                ws = null;
                log('🔌 Desconectado manualmente');
                updateStatus(false);
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                log('⚠️ Mensagem vazia');
                return;
            }
            
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket não está conectado');
                return;
            }
            
            try {
                ws.send(message);
                log(`📤 Mensagem enviada: ${message}`);
                input.value = '';
            } catch (error) {
                log(`❌ Erro ao enviar mensagem: ${error}`);
            }
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // Permitir envio com Enter
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Log inicial
        log('🚀 Página de teste WebSocket carregada');
        log('💡 Clique em "Conectar" para iniciar o teste');
    </script>
</body>
</html>
