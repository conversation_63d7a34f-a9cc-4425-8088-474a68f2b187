#!/usr/bin/env node

/**
 * Teste rápido do endpoint de carregamento de histórico
 */

const axios = require('axios');

async function testHistoryEndpoint() {
    console.log('🔍 TESTE RÁPIDO - ENDPOINT DE HISTÓRICO\n');
    
    try {
        // 1. Fazer login
        console.log('🔐 Fazendo login...');
        const loginResponse = await axios.post('http://localhost:8000/token', 
            'username=admin&password=admin123',
            {
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
            }
        );
        
        const token = loginResponse.data.access_token;
        const headers = { 'Authorization': `Bearer ${token}` };
        console.log('✅ Login realizado');
        
        // 2. Testar endpoint de histórico via Python
        console.log('\n📨 Testando endpoint via Python...');
        
        try {
            const historyResponse = await axios.post(
                'http://localhost:8000/whatsapp/conversations/<EMAIL>/load-history', 
                {}, 
                { 
                    headers,
                    timeout: 10000 // 10 segundos de timeout
                }
            );
            
            console.log('✅ Endpoint Python funcionou!');
            console.log(`   Resposta: ${JSON.stringify(historyResponse.data)}`);
        } catch (error) {
            console.log(`❌ Endpoint Python falhou: ${error.response?.status} - ${error.message}`);
            
            if (error.response?.data) {
                console.log(`   Detalhes: ${JSON.stringify(error.response.data)}`);
            }
        }
        
        // 3. Testar endpoint direto no WhatsApp backend
        console.log('\n📱 Testando endpoint direto WhatsApp...');
        
        try {
            const directResponse = await axios.post(
                'http://localhost:3001/conversations/<EMAIL>/load-history',
                {},
                { timeout: 10000 }
            );
            
            console.log('✅ Endpoint WhatsApp direto funcionou!');
            console.log(`   Resposta: ${JSON.stringify(directResponse.data)}`);
        } catch (error) {
            console.log(`❌ Endpoint WhatsApp direto falhou: ${error.response?.status} - ${error.message}`);
            
            if (error.response?.data) {
                console.log(`   Detalhes: ${JSON.stringify(error.response.data)}`);
            }
        }
        
        // 4. Verificar se endpoint existe
        console.log('\n🔍 Verificando se endpoints existem...');
        
        // Listar rotas disponíveis
        try {
            const routesResponse = await axios.get('http://localhost:3001/', { timeout: 5000 });
            console.log('✅ Backend WhatsApp respondendo');
        } catch (error) {
            console.log(`❌ Backend WhatsApp não responde: ${error.message}`);
        }
        
        console.log('\n' + '='.repeat(50));
        console.log('🎯 RESUMO DO TESTE');
        console.log('='.repeat(50));
        
        console.log('\n📊 STATUS:');
        console.log('   🔐 Login: ✅ Funcionando');
        console.log('   🐍 Backend Python: ✅ Rodando');
        console.log('   📱 Backend WhatsApp: ✅ Rodando');
        
        console.log('\n💡 PRÓXIMOS PASSOS:');
        console.log('   1. Verificar logs do backend WhatsApp');
        console.log('   2. Confirmar se endpoint foi implementado');
        console.log('   3. Testar no frontend após correção');
        
    } catch (error) {
        console.error('\n❌ ERRO NO TESTE:', error.message);
    }
}

// Executar teste
testHistoryEndpoint().catch(console.error);
