"""
Testes de cobertura de código para o chat da persona
"""

import unittest
import sys
import os
import inspect
from unittest.mock import Mock, patch, AsyncMock

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.persona import RESPONSE_TEMPLATES, EMOJIS, HASHTAGS, PERSONA_CONFIG
from pipelines.post_processing import PostProcessor
from handlers.intent_handlers import IntentHandlers


class TestCodeCoverage(unittest.TestCase):
    """
    Testes para maximizar cobertura de código
    """
    
    def setUp(self):
        """Configurar testes de cobertura"""
        self.processor = PostProcessor()
        self.mock_supabase = Mock()
        self.handler = IntentHandlers(self.mock_supabase)
        self.coverage_results = {}
    
    def test_all_template_keywords(self):
        """Testa todas as palavras-chave de todos os templates"""
        tested_keywords = set()
        matched_templates = set()
        
        for template_key, template_data in RESPONSE_TEMPLATES.items():
            keywords = template_data["keywords"]
            
            for keyword in keywords:
                # Testar palavra-chave exata
                response = self.handler.handle_template_query(keyword)
                if response:
                    tested_keywords.add(keyword)
                    matched_templates.add(template_key)
                
                # Testar em contexto
                context_query = f"Eu preciso de {keyword}"
                response_context = self.handler.handle_template_query(context_query)
                if response_context:
                    tested_keywords.add(f"context_{keyword}")
                    matched_templates.add(template_key)
        
        total_keywords = sum(len(data["keywords"]) for data in RESPONSE_TEMPLATES.values())
        coverage_rate = len(tested_keywords) / (total_keywords * 2)  # *2 porque testamos contexto também
        
        self.coverage_results['template_keywords'] = {
            'total_keywords': total_keywords,
            'tested_keywords': len(tested_keywords),
            'matched_templates': len(matched_templates),
            'total_templates': len(RESPONSE_TEMPLATES),
            'coverage_rate': coverage_rate
        }
        
        # Verificar cobertura
        self.assertGreater(coverage_rate, 0.8, f"Cobertura de keywords baixa: {coverage_rate:.2%}")
        self.assertEqual(len(matched_templates), len(RESPONSE_TEMPLATES), "Nem todos os templates foram testados")
        
        print(f"   📊 Keywords - {len(tested_keywords)}/{total_keywords*2} testadas ({coverage_rate:.1%})")
    
    def test_all_emojis_usage(self):
        """Testa se todos os emojis podem ser selecionados"""
        used_emojis = set()
        
        # Contextos que devem gerar diferentes emojis
        test_contexts = [
            # Saúde
            "Vou verificar na UBS sobre medicamento",
            "Preciso marcar consulta médica",
            "Como está o atendimento na saúde?",
            
            # Trabalho
            "Vamos trabalhar juntos",
            "Preciso de oportunidades de emprego",
            "Como posso me candidatar a vagas?",
            
            # Assistência
            "Preciso de ajuda social",
            "Como funciona o CRAS?",
            "Onde buscar assistência?",
            
            # Fé
            "Que Deus nos abençoe",
            "Amém, irmã",
            "Glória a Deus",
            
            # Transparência
            "Quero ver os gastos públicos",
            "Como acessar prestação de contas?",
            "Transparência é importante",
            
            # Agenda
            "Quero marcar reunião",
            "Podemos nos encontrar?",
            "Qual sua agenda?",
            
            # Positivo
            "Obrigado pela ajuda",
            "Parabéns pelo trabalho",
            "Que sucesso maravilhoso",
            
            # Contextos genéricos
            "Como você está?",
            "Preciso de informações",
            "Pode me ajudar?",
            "Muito obrigado"
        ]
        
        for context in test_contexts:
            emoji = self.processor._select_contextual_emoji(context)
            used_emojis.add(emoji)
        
        # Testar seleção forçada para garantir cobertura
        for _ in range(50):  # Múltiplas tentativas para capturar aleatoriedade
            emoji = self.processor._select_contextual_emoji("Texto genérico")
            used_emojis.add(emoji)
        
        emoji_coverage = len(used_emojis) / len(EMOJIS)
        
        self.coverage_results['emoji_usage'] = {
            'total_emojis': len(EMOJIS),
            'used_emojis': len(used_emojis),
            'coverage_rate': emoji_coverage,
            'unused_emojis': set(EMOJIS) - used_emojis
        }
        
        # Verificar cobertura
        self.assertGreater(emoji_coverage, 0.7, f"Cobertura de emojis baixa: {emoji_coverage:.2%}")
        
        print(f"   📊 Emojis - {len(used_emojis)}/{len(EMOJIS)} usados ({emoji_coverage:.1%})")
    
    def test_all_hashtags_usage(self):
        """Testa se todas as hashtags podem ser selecionadas"""
        used_hashtags = set()
        
        # Contextos específicos para cada hashtag
        hashtag_contexts = {
            "#AcessibilidadeParaTodos": [
                "A inclusão é fundamental",
                "Precisamos de mais acessibilidade",
                "Pessoas com deficiência merecem respeito"
            ],
            "#TransparênciaTotal": [
                "Todos os gastos estão disponíveis",
                "Transparência na gestão pública",
                "Prestação de contas é importante"
            ],
            "#SaúdePública": [
                "Melhorias na saúde são prioridade",
                "UBS precisa de mais recursos",
                "Atendimento médico de qualidade"
            ],
            "#JuntosSomosMaisFortes": [
                "Unidos podemos mais",
                "Juntos vamos conseguir",
                "A força está na união"
            ],
            "#ParnamirimRN": [
                "Nossa cidade Parnamirim",
                "Parnamirim merece o melhor",
                "Viva Parnamirim"
            ]
        }
        
        for expected_hashtag, contexts in hashtag_contexts.items():
            for context in contexts:
                hashtag = self.processor._select_contextual_hashtag(context)
                used_hashtags.add(hashtag)
                
                # Verificar se o contexto gera a hashtag esperada
                if hashtag == expected_hashtag:
                    print(f"   ✅ {expected_hashtag} gerada corretamente")
        
        # Testar contextos genéricos
        for _ in range(20):
            hashtag = self.processor._select_contextual_hashtag("Texto genérico")
            used_hashtags.add(hashtag)
        
        hashtag_coverage = len(used_hashtags) / len(HASHTAGS)
        
        self.coverage_results['hashtag_usage'] = {
            'total_hashtags': len(HASHTAGS),
            'used_hashtags': len(used_hashtags),
            'coverage_rate': hashtag_coverage,
            'unused_hashtags': set(HASHTAGS) - used_hashtags
        }
        
        # Verificar cobertura
        self.assertGreater(hashtag_coverage, 0.6, f"Cobertura de hashtags baixa: {hashtag_coverage:.2%}")
        
        print(f"   📊 Hashtags - {len(used_hashtags)}/{len(HASHTAGS)} usadas ({hashtag_coverage:.1%})")
    
    def test_edge_cases_coverage(self):
        """Testa casos extremos para máxima cobertura"""
        edge_cases_tested = 0
        total_edge_cases = 0
        
        # Teste 1: Textos vazios e nulos
        total_edge_cases += 1
        try:
            self.processor.clean_text("")
            self.processor.process_response("")
            self.handler.handle_template_query("")
            edge_cases_tested += 1
        except:
            pass
        
        # Teste 2: Textos muito longos
        total_edge_cases += 1
        try:
            long_text = "Palavra " * 10000
            self.processor.process_response(long_text)
            edge_cases_tested += 1
        except:
            pass
        
        # Teste 3: Textos com caracteres especiais
        total_edge_cases += 1
        try:
            special_text = "!@#$%^&*()_+{}|:<>?[]\\;'\",./"
            self.processor.process_response(special_text)
            self.handler.handle_template_query(special_text)
            edge_cases_tested += 1
        except:
            pass
        
        # Teste 4: Textos só com espaços
        total_edge_cases += 1
        try:
            spaces_text = "   \n\n\t\t   "
            self.processor.clean_text(spaces_text)
            edge_cases_tested += 1
        except:
            pass
        
        # Teste 5: Textos com emojis e hashtags existentes
        total_edge_cases += 1
        try:
            emoji_text = "Texto com emoji ✨ e hashtag #teste"
            self.processor.add_emoji(emoji_text, force=False)
            self.processor.add_hashtag(emoji_text, force=False)
            edge_cases_tested += 1
        except:
            pass
        
        # Teste 6: Configurações extremas
        total_edge_cases += 1
        try:
            original_prob = self.processor.config["emoji_probability"]
            self.processor.config["emoji_probability"] = 0.0
            self.processor.add_emoji("teste", force=False)
            self.processor.config["emoji_probability"] = 1.0
            self.processor.add_emoji("teste", force=False)
            self.processor.config["emoji_probability"] = original_prob
            edge_cases_tested += 1
        except:
            pass
        
        edge_coverage = edge_cases_tested / total_edge_cases
        
        self.coverage_results['edge_cases'] = {
            'total_cases': total_edge_cases,
            'tested_cases': edge_cases_tested,
            'coverage_rate': edge_coverage
        }
        
        print(f"   📊 Edge cases - {edge_cases_tested}/{total_edge_cases} testados ({edge_coverage:.1%})")
    
    def test_function_coverage(self):
        """Testa se todas as funções públicas foram chamadas"""
        # Funções do PostProcessor
        processor_functions = [
            'add_emoji', 'add_hashtag', 'limit_length', 'clean_text',
            'process_response', '_has_emoji', '_has_hashtag',
            '_select_contextual_emoji', '_select_contextual_hashtag'
        ]
        
        tested_functions = set()
        
        # Testar funções do processor
        text = "Texto de teste para cobertura"
        
        try:
            self.processor.add_emoji(text, force=True)
            tested_functions.add('add_emoji')
        except: pass
        
        try:
            self.processor.add_hashtag(text, force=True)
            tested_functions.add('add_hashtag')
        except: pass
        
        try:
            self.processor.limit_length(text)
            tested_functions.add('limit_length')
        except: pass
        
        try:
            self.processor.clean_text(text)
            tested_functions.add('clean_text')
        except: pass
        
        try:
            self.processor.process_response(text)
            tested_functions.add('process_response')
        except: pass
        
        try:
            self.processor._has_emoji(text)
            tested_functions.add('_has_emoji')
        except: pass
        
        try:
            self.processor._has_hashtag(text)
            tested_functions.add('_has_hashtag')
        except: pass
        
        try:
            self.processor._select_contextual_emoji(text)
            tested_functions.add('_select_contextual_emoji')
        except: pass
        
        try:
            self.processor._select_contextual_hashtag(text)
            tested_functions.add('_select_contextual_hashtag')
        except: pass
        
        # Funções do handler
        handler_functions = [
            'handle_template_query', 'get_fallback_response'
        ]
        
        try:
            self.handler.handle_template_query("teste")
            tested_functions.add('handle_template_query')
        except: pass
        
        try:
            self.handler.get_fallback_response()
            tested_functions.add('get_fallback_response')
        except: pass
        
        total_functions = len(processor_functions) + len(handler_functions)
        function_coverage = len(tested_functions) / total_functions
        
        self.coverage_results['function_coverage'] = {
            'total_functions': total_functions,
            'tested_functions': len(tested_functions),
            'coverage_rate': function_coverage,
            'tested_function_names': list(tested_functions)
        }
        
        print(f"   📊 Funções - {len(tested_functions)}/{total_functions} testadas ({function_coverage:.1%})")
    
    def get_coverage_report(self):
        """Retorna relatório de cobertura"""
        return self.coverage_results


if __name__ == "__main__":
    unittest.main()
