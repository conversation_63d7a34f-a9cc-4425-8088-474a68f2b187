#!/usr/bin/env python3

"""
Teste do Sistema de Notificações e Envio de Mensagens
"""

import asyncio
import json
import time
from datetime import datetime
from websocket_manager import websocket_manager, NotificationMessage
from message_sender import message_sender, MessageToSend


async def test_websocket_notifications():
    """Testa sistema de notificações WebSocket"""
    print("🔔 TESTE: SISTEMA DE NOTIFICAÇÕES WEBSOCKET")
    print("=" * 60)
    
    # Simular conexões de clientes
    print("📱 Simulando conexões de clientes...")
    
    # Estatísticas iniciais
    initial_stats = websocket_manager.get_stats()
    print(f"   Conexões ativas: {initial_stats['active_connections']}")
    print(f"   Usuários conectados: {initial_stats['connected_users']}")
    
    # Teste 1: Notificação de nova mensagem
    print("\n🔔 Teste 1: Notificação de Nova Mensagem")
    print("-" * 40)
    
    notification = NotificationMessage(
        type="new_message",
        title="Nova Mensagem",
        message="<PERSON>: Bom dia! Preciso de ajuda com cesta básica",
        data={
            "contact_name": "<PERSON>",
            "contact_id": "5511999999999",
            "conversation_id": "conv_123"
        },
        priority="high"
    )
    
    sent_count = await websocket_manager.send_notification(notification)
    print(f"   ✅ Notificação enviada para {sent_count} clientes")
    print(f"   📋 Tipo: {notification.type}")
    print(f"   📝 Mensagem: {notification.message}")
    
    # Teste 2: Notificação de resposta da IA
    print("\n🤖 Teste 2: Notificação de Resposta da IA")
    print("-" * 40)
    
    ai_notification = NotificationMessage(
        type="ai_response",
        title="Resposta Enviada",
        message="Resposta enviada para João Silva",
        data={
            "contact_name": "João Silva",
            "response": "Oi João! Para solicitar cesta básica, você precisa ir ao CRAS mais próximo...",
            "conversation_id": "conv_123"
        },
        priority="normal"
    )
    
    sent_count = await websocket_manager.send_notification(ai_notification)
    print(f"   ✅ Notificação enviada para {sent_count} clientes")
    print(f"   📋 Tipo: {ai_notification.type}")
    print(f"   📝 Mensagem: {ai_notification.message}")
    
    # Teste 3: Alerta do sistema
    print("\n⚠️ Teste 3: Alerta do Sistema")
    print("-" * 40)
    
    sent_count = await websocket_manager.send_system_alert(
        title="Sistema Atualizado",
        message="O sistema foi atualizado com novas funcionalidades de memória conversacional",
        priority="normal"
    )
    print(f"   ✅ Alerta enviado para {sent_count} clientes")
    
    # Teste 4: Broadcast geral
    print("\n📢 Teste 4: Broadcast Geral")
    print("-" * 40)
    
    broadcast_data = {
        "type": "system_announcement",
        "title": "Manutenção Programada",
        "message": "Haverá manutenção do sistema hoje às 23h",
        "timestamp": datetime.now().isoformat()
    }
    
    sent_count = await websocket_manager.broadcast_message(broadcast_data)
    print(f"   ✅ Broadcast enviado para {sent_count} clientes")
    
    # Estatísticas finais
    print("\n📊 ESTATÍSTICAS FINAIS:")
    print("-" * 30)
    final_stats = websocket_manager.get_stats()
    
    for key, value in final_stats.items():
        print(f"   {key}: {value}")
    
    return True


async def test_message_sending():
    """Testa sistema de envio de mensagens"""
    print("\n\n📱 TESTE: SISTEMA DE ENVIO DE MENSAGENS")
    print("=" * 60)
    
    # Teste 1: Verificar conexão WhatsApp
    print("🔗 Teste 1: Verificação de Conexão WhatsApp")
    print("-" * 45)
    
    is_connected = await message_sender.test_whatsapp_connection()
    print(f"   Status WhatsApp: {'✅ Conectado' if is_connected else '❌ Desconectado'}")
    
    if not is_connected:
        print("   ⚠️ WhatsApp não conectado - usando modo simulação")
    
    # Teste 2: Envio de mensagem simples
    print("\n📤 Teste 2: Envio de Mensagem Simples")
    print("-" * 40)
    
    test_message = MessageToSend(
        contact_id="5511999999999",
        contact_name="João Silva (Teste)",
        message="Olá João! Esta é uma mensagem de teste do sistema. Nossa equipe está aqui para ajudar você! 🙏",
        conversation_id="conv_test_123",
        channel="whatsapp",
        priority="normal"
    )
    
    print(f"   📱 Enviando para: {test_message.contact_name}")
    print(f"   📝 Mensagem: {test_message.message[:50]}...")
    
    success = await message_sender.send_message(test_message)
    print(f"   Resultado: {'✅ Enviado' if success else '❌ Falhou'}")
    
    # Teste 3: Fila de mensagens
    print("\n📋 Teste 3: Sistema de Fila de Mensagens")
    print("-" * 42)
    
    # Adicionar várias mensagens à fila
    test_messages = [
        MessageToSend(
            contact_id="5511888888888",
            contact_name="Maria Santos",
            message="Oi Maria! Sua solicitação de cesta básica foi aprovada. Você pode retirar no CRAS do seu bairro.",
            conversation_id="conv_maria_456"
        ),
        MessageToSend(
            contact_id="5511777777777", 
            contact_name="Pedro Oliveira",
            message="Olá Pedro! As vagas de emprego que você procura estão disponíveis no SINE. Nossa equipe pode te orientar!",
            conversation_id="conv_pedro_789"
        ),
        MessageToSend(
            contact_id="5511666666666",
            contact_name="Ana Costa",
            message="Oi Ana! A consulta médica foi agendada para amanhã às 14h na UBS. Lembre-se de levar seus documentos! 📋",
            conversation_id="conv_ana_101"
        )
    ]
    
    print(f"   📤 Adicionando {len(test_messages)} mensagens à fila...")
    
    for msg in test_messages:
        await message_sender.queue_message(msg)
        print(f"   ✅ {msg.contact_name}: {msg.message[:30]}...")
    
    # Aguardar processamento
    print("   ⏳ Aguardando processamento da fila...")
    await asyncio.sleep(3)
    
    # Teste 4: Estatísticas
    print("\n📊 Teste 4: Estatísticas de Envio")
    print("-" * 35)
    
    stats = message_sender.get_stats()
    
    for key, value in stats.items():
        if isinstance(value, float):
            print(f"   {key}: {value:.1f}%")
        else:
            print(f"   {key}: {value}")
    
    return True


async def test_integration():
    """Testa integração entre notificações e envio"""
    print("\n\n🔗 TESTE: INTEGRAÇÃO NOTIFICAÇÕES + ENVIO")
    print("=" * 60)
    
    # Simular fluxo completo
    print("💬 Simulando fluxo completo de conversa...")
    print("-" * 45)
    
    # 1. Nova mensagem recebida
    print("\n1️⃣ Nova mensagem recebida")
    contact_name = "Carlos Silva"
    user_message = "Bom dia! Preciso de informações sobre auxílio emergencial"
    
    # Notificar nova mensagem
    await websocket_manager.send_message_notification(
        contact_name=contact_name,
        message=user_message,
        conversation_id="conv_carlos_202"
    )
    print(f"   🔔 Notificação enviada: Nova mensagem de {contact_name}")
    
    # 2. Processamento da IA (simulado)
    print("\n2️⃣ Processamento da IA")
    await asyncio.sleep(1)  # Simular tempo de processamento
    
    ai_response = "Oi Carlos! Para informações sobre auxílio emergencial, você pode procurar o CRAS mais próximo ou ligar para 156. Nossa equipe está aqui para te ajudar! 🤝"
    
    # 3. Envio da resposta
    print("\n3️⃣ Envio da resposta")
    success = await message_sender.send_message_to_contact(
        contact_id="5511555555555",
        contact_name=contact_name,
        message=ai_response,
        conversation_id="conv_carlos_202"
    )
    
    if success:
        # 4. Notificar resposta enviada
        print("\n4️⃣ Notificação de resposta enviada")
        await websocket_manager.send_ai_response_notification(
            contact_name=contact_name,
            response=ai_response,
            conversation_id="conv_carlos_202"
        )
        print(f"   🔔 Notificação enviada: Resposta para {contact_name}")
        
        print("\n✅ FLUXO COMPLETO EXECUTADO COM SUCESSO!")
    else:
        print("\n❌ Falha no envio da resposta")
    
    return success


async def test_performance():
    """Testa performance do sistema"""
    print("\n\n📈 TESTE: PERFORMANCE DO SISTEMA")
    print("=" * 60)
    
    # Teste de carga de notificações
    print("🔔 Teste de Carga: Notificações")
    print("-" * 35)
    
    start_time = time.time()
    notification_count = 50
    
    print(f"   📤 Enviando {notification_count} notificações...")
    
    tasks = []
    for i in range(notification_count):
        notification = NotificationMessage(
            type="test_notification",
            title=f"Teste {i+1}",
            message=f"Esta é a notificação de teste número {i+1}",
            priority="normal"
        )
        
        task = websocket_manager.send_notification(notification)
        tasks.append(task)
    
    # Executar todas as notificações em paralelo
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    duration = end_time - start_time
    
    total_sent = sum(results)
    notifications_per_second = notification_count / duration
    
    print(f"   ⏱️ Tempo total: {duration:.2f}s")
    print(f"   📊 Notificações/segundo: {notifications_per_second:.1f}")
    print(f"   ✅ Total enviado: {total_sent}")
    
    # Teste de carga de mensagens
    print(f"\n📱 Teste de Carga: Mensagens")
    print("-" * 30)
    
    start_time = time.time()
    message_count = 20
    
    print(f"   📤 Enviando {message_count} mensagens...")
    
    message_tasks = []
    for i in range(message_count):
        message = MessageToSend(
            contact_id=f"551199999{i:04d}",
            contact_name=f"Usuário Teste {i+1}",
            message=f"Esta é a mensagem de teste número {i+1}. Nossa equipe está aqui para ajudar! 🙏",
            conversation_id=f"conv_test_{i+1}"
        )
        
        task = message_sender.send_message(message)
        message_tasks.append(task)
    
    # Executar com limite de concorrência
    message_results = []
    for i in range(0, len(message_tasks), 5):  # Processar 5 por vez
        batch = message_tasks[i:i+5]
        batch_results = await asyncio.gather(*batch)
        message_results.extend(batch_results)
        await asyncio.sleep(0.1)  # Pequena pausa entre batches
    
    end_time = time.time()
    duration = end_time - start_time
    
    successful_messages = sum(message_results)
    messages_per_second = message_count / duration
    
    print(f"   ⏱️ Tempo total: {duration:.2f}s")
    print(f"   📊 Mensagens/segundo: {messages_per_second:.1f}")
    print(f"   ✅ Mensagens enviadas: {successful_messages}/{message_count}")
    
    return True


async def main():
    """Função principal dos testes"""
    print("🧪 TESTE COMPLETO: SISTEMA DE NOTIFICAÇÕES E MENSAGENS")
    print("=" * 80)
    print("🎯 Este teste valida:")
    print("   • Sistema de notificações WebSocket")
    print("   • Sistema de envio de mensagens")
    print("   • Integração entre os sistemas")
    print("   • Performance sob carga")
    print("=" * 80)
    
    try:
        # Executar todos os testes
        test1 = await test_websocket_notifications()
        test2 = await test_message_sending()
        test3 = await test_integration()
        test4 = await test_performance()
        
        # Relatório final
        print("\n" + "=" * 80)
        print("🎉 RELATÓRIO FINAL DOS TESTES")
        print("=" * 80)
        
        tests_results = [
            ("Notificações WebSocket", test1),
            ("Envio de Mensagens", test2),
            ("Integração Completa", test3),
            ("Performance", test4)
        ]
        
        passed_tests = sum(1 for _, result in tests_results if result)
        total_tests = len(tests_results)
        
        for test_name, result in tests_results:
            status = "✅ PASSOU" if result else "❌ FALHOU"
            print(f"   {test_name}: {status}")
        
        success_rate = (passed_tests / total_tests) * 100
        
        print(f"\n📊 RESUMO:")
        print(f"   Testes executados: {total_tests}")
        print(f"   Testes aprovados: {passed_tests}")
        print(f"   Taxa de sucesso: {success_rate:.1f}%")
        
        if success_rate >= 100:
            print(f"\n🎉 PERFEITO! Todos os testes passaram!")
        elif success_rate >= 75:
            print(f"\n✅ BOM! Sistema funcionando bem!")
        else:
            print(f"\n⚠️ ATENÇÃO! Alguns testes falharam.")
        
        print("\n🚀 Sistema de notificações e mensagens pronto para produção!")
        
    except Exception as e:
        print(f"\n❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
