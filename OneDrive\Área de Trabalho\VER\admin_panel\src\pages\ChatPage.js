import React, { useState, useRef, useEffect } from 'react';
import { Typography, Paper, Box, TextField, IconButton, CircularProgress, Alert } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import MicIcon from '@mui/icons-material/Mic';
import StopIcon from '@mui/icons-material/Stop';
import { useMutation } from '@tanstack/react-query';
import axiosInstance from '../api/axiosInstance';

const postQuery = async (query) => {
  const { data } = await axiosInstance.post('/chat/', { query });
  return data;
};

const transcribeAudio = async (audioBlob) => {
  const formData = new FormData();
  formData.append('audio_file', audioBlob, 'audio.webm');
  const { data } = await axiosInstance.post('/transcribe-audio/', formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return data;
};

const ChatPage = () => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [audioError, setAudioError] = useState('');
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const messagesEndRef = useRef(null);

  const ragMutation = useMutation({
    mutationFn: postQuery,
    onSuccess: (data) => {
      setMessages(prev => [...prev.slice(0, -1), { sender: 'ai', text: data.response }]);
    },
    onError: (error) => {
      setMessages(prev => [...prev.slice(0, -1), { sender: 'ai', text: `Erro ao buscar resposta: ${error.message}` }]);
    }
  });

  const transcribeMutation = useMutation({
    mutationFn: transcribeAudio,
    onSuccess: (data) => {
      const transcribedText = data.transcribed_text;
      setMessages(prev => [...prev, { sender: 'user', text: `🎤: "${transcribedText}"` }]);
      ragMutation.mutate(transcribedText);
    },
    onError: (error) => {
      setMessages(prev => [...prev.slice(0, -1)]); // Remove o "transcrevendo..."
      setAudioError(`Erro na transcrição: ${error.message}`);
    }
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  const handleSend = () => {
    if (input.trim() === '') return;
    const userMessage = { sender: 'user', text: input };
    setMessages(prev => [...prev, userMessage, { sender: 'ai', text: '...' }]);
    ragMutation.mutate(input);
    setInput('');
  };

  const handleToggleRecording = async () => {
    if (isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    } else {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        mediaRecorderRef.current = new MediaRecorder(stream);
        audioChunksRef.current = [];

        mediaRecorderRef.current.ondataavailable = (event) => {
          audioChunksRef.current.push(event.data);
        };

        mediaRecorderRef.current.onstop = () => {
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
          setMessages(prev => [...prev, { sender: 'ai', text: 'Transcrevendo áudio...' }]);
          transcribeMutation.mutate(audioBlob);
          stream.getTracks().forEach(track => track.stop()); // Libera o microfone
        };

        mediaRecorderRef.current.start();
        setIsRecording(true);
        setAudioError('');
      } catch (err) {
        console.error("Erro ao acessar o microfone:", err);
        setAudioError('Não foi possível acessar o microfone. Verifique as permissões do navegador.');
      }
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  const isLoading = ragMutation.isPending || transcribeMutation.isPending;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: 'calc(100vh - 120px)' }}>
      <Typography variant="h4" sx={{ mb: 2 }}>
        Chat com Documentos
      </Typography>
      {audioError && <Alert severity="error" sx={{ mb: 2 }}>{audioError}</Alert>}
      <Paper elevation={2} sx={{ flexGrow: 1, p: 2, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
        <Box sx={{ flexGrow: 1, overflowY: 'auto', mb: 2 }}>
          {messages.map((msg, index) => (
            <Box key={index} sx={{ display: 'flex', justifyContent: msg.sender === 'user' ? 'flex-end' : 'flex-start', mb: 1 }}>
              <Paper elevation={1} sx={{ p: 1.5, backgroundColor: msg.sender === 'user' ? 'primary.main' : 'grey.200', color: msg.sender === 'user' ? 'primary.contrastText' : 'text.primary', borderRadius: msg.sender === 'user' ? '20px 20px 5px 20px' : '20px 20px 20px 5px', maxWidth: '70%', whiteSpace: 'pre-wrap' }}>
                {msg.text === '...' || msg.text === 'Transcrevendo áudio...' ? <CircularProgress size={20} color="inherit" /> : msg.text}
              </Paper>
            </Box>
          ))}
          <div ref={messagesEndRef} />
        </Box>
        <Box component="form" onSubmit={(e) => { e.preventDefault(); handleSend(); }} sx={{ display: 'flex', alignItems: 'center', borderTop: '1px solid', borderColor: 'divider', pt: 1 }}>
          <TextField fullWidth variant="outlined" placeholder="Digite sua pergunta..." value={input} onChange={(e) => setInput(e.target.value)} onKeyPress={handleKeyPress} disabled={isLoading} multiline maxRows={4} />
          <IconButton color="primary" type="submit" disabled={isLoading || isRecording}>
            <SendIcon />
          </IconButton>
          <IconButton color={isRecording ? "error" : "primary"} onClick={handleToggleRecording} disabled={ragMutation.isPending}>
            {isRecording ? <StopIcon /> : <MicIcon />}
          </IconButton>
        </Box>
      </Paper>
    </Box>
  );
};

export default ChatPage;

