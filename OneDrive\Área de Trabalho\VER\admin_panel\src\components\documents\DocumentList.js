import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  CircularProgress,
  Alert,
  Typography,
  Box,
  Chip,
  Paper
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Description as DocumentIcon,
  PictureAsPdf as PdfIcon,
  TableChart as CsvIcon,
  TextFields as TextIcon
} from '@mui/icons-material';
import axiosInstance from '../../api/axiosInstance';

const fetchDocuments = async () => {
  const { data } = await axiosInstance.get('/documents/');
  return data.documents;
};

const deleteDocument = async (documentId) => {
  await axiosInstance.delete(`/documents/${documentId}`);
};

const DocumentList = ({ onDocumentClick }) => {
  const queryClient = useQueryClient();
  const { data: documents, isLoading, isError, error } = useQuery({
    queryKey: ['documents'],
    queryFn: fetchDocuments,
  });

  const mutation = useMutation({
    mutationFn: deleteDocument,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
    },
  });

  if (isLoading) {
    return <CircularProgress />;
  }

  if (isError) {
    return <Alert severity="error">Erro ao buscar documentos: {error.message}</Alert>;
  }

  const getFileIcon = (fileType) => {
    switch (fileType?.toLowerCase()) {
      case 'pdf':
        return <PdfIcon sx={{ color: '#d32f2f' }} />;
      case 'csv':
        return <CsvIcon sx={{ color: '#2e7d32' }} />;
      case 'txt':
        return <TextIcon sx={{ color: '#1976d2' }} />;
      default:
        return <DocumentIcon sx={{ color: '#757575' }} />;
    }
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'processed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <Box>
      {documents.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
          <DocumentIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
          <Typography variant="h6" gutterBottom>
            Nenhum documento encontrado
          </Typography>
          <Typography variant="body2">
            Faça upload de documentos para começar
          </Typography>
        </Box>
      ) : (
        <List sx={{ p: 0 }}>
          {documents.map((doc, index) => (
            <Paper
              key={doc.id}
              sx={{
                mb: 1,
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'divider',
                '&:hover': {
                  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                  borderColor: 'primary.main',
                  cursor: 'pointer'
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <ListItem
                onClick={() => onDocumentClick && onDocumentClick(doc)}
                sx={{
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: 'action.hover'
                  }
                }}
                secondaryAction={
                  <IconButton
                    edge="end"
                    aria-label="delete"
                    onClick={(e) => {
                      e.stopPropagation();
                      mutation.mutate(doc.id);
                    }}
                    disabled={mutation.isPending}
                    sx={{
                      color: 'error.main',
                      '&:hover': {
                        bgcolor: 'error.light',
                        color: 'error.contrastText'
                      }
                    }}
                  >
                    <DeleteIcon />
                  </IconButton>
                }
              >
                <ListItemIcon>
                  {getFileIcon(doc.file_type)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                        {doc.filename}
                      </Typography>
                      <Chip
                        label={doc.status || 'Processado'}
                        color={getStatusColor(doc.status)}
                        size="small"
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      component="div"
                      sx={{
                        mt: 0.5,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                        flexWrap: 'wrap'
                      }}
                    >
                      <Typography variant="caption" color="text.secondary" component="span">
                        Tipo: {doc.file_type?.toUpperCase() || 'N/A'}
                      </Typography>
                      {doc.file_size && (
                        <Typography variant="caption" color="text.secondary" component="span">
                          Tamanho: {formatFileSize(doc.file_size)}
                        </Typography>
                      )}
                      {doc.created_at && (
                        <Typography variant="caption" color="text.secondary" component="span">
                          Enviado: {new Date(doc.created_at).toLocaleDateString('pt-BR')}
                        </Typography>
                      )}
                    </Typography>
                  }
                />
              </ListItem>
            </Paper>
          ))}
        </List>
      )}
    </Box>
  );
};

export default DocumentList;
