import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { List, ListItem, ListItemText, IconButton, CircularProgress, Alert, Typography, Box } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import axiosInstance from '../../api/axiosInstance';

const fetchDocuments = async () => {
  const { data } = await axiosInstance.get('/documents/');
  return data.documents;
};

const deleteDocument = async (documentId) => {
  await axiosInstance.delete(`/documents/${documentId}`);
};

const DocumentList = () => {
  const queryClient = useQueryClient();
  const { data: documents, isLoading, isError, error } = useQuery({
    queryKey: ['documents'],
    queryFn: fetchDocuments,
  });

  const mutation = useMutation({
    mutationFn: deleteDocument,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['documents'] });
    },
  });

  if (isLoading) {
    return <CircularProgress />;
  }

  if (isError) {
    return <Alert severity="error">Erro ao buscar documentos: {error.message}</Alert>;
  }

  return (
    <Box>
      {documents.length === 0 ? (
        <Typography>Nenhum documento encontrado.</Typography>
      ) : (
        <List>
          {documents.map((doc) => (
            <ListItem
              key={doc.id}
              secondaryAction={
                <IconButton edge="end" aria-label="delete" onClick={() => mutation.mutate(doc.id)} disabled={mutation.isPending}>
                  <DeleteIcon />
                </IconButton>
              }
            >
              <ListItemText
                primary={doc.filename}
                secondary={`Status: ${doc.status} | Tipo: ${doc.file_type}`}
              />
            </ListItem>
          ))}
        </List>
      )}
    </Box>
  );
};

export default DocumentList;
