# Exemplo de variáveis de ambiente.
# Renomeie este arquivo para .env e preencha com seus valores.


SUPABASE_URL="https://cagbdeaeuvnofmfnecaf.supabase.co"
SUPABASE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNhZ2JkZWFldXZub2ZtZm5lY2FmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDMzOTYsImV4cCI6MjA2NzUxOTM5Nn0.QSC7qWHgZe15_9ZGJp331-SGC5tkghMyXyprwKWBbjA"
GEMINI_API_KEY="091345fc-a209-4a83-9723-aa93908ee802"
WHATSAPP_BACKEND_URL=http://whatsapp-backend:3001
SECRET_KEY="your-super-secret-key"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# URLs dos serviços (usado para comunicação entre contêineres)
# O nome do host deve ser o mesmo nome do serviço no docker-compose.yml
WHATSAPP_BACKEND_URL=http://whatsapp-backend:3001
PYTHON_BACKEND_URL=http://localhost:8000

# Frontend Admin (React)
# A URL da API para o frontend que roda no navegador do usuário
REACT_APP_API_URL=http://localhost:8000
