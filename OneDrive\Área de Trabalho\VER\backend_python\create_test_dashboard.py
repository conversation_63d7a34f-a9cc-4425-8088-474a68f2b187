#!/usr/bin/env python3

"""
Criador de dashboard HTML para métricas de teste
"""

import json
import os
from datetime import datetime


def create_test_dashboard():
    """Cria dashboard HTML com métricas de teste"""
    
    # Buscar o relatório mais recente
    reports_dir = os.path.join(os.path.dirname(__file__), 'reports')
    
    if not os.path.exists(reports_dir):
        print("❌ Pasta de relatórios não encontrada. Execute os testes primeiro.")
        return
    
    # Encontrar arquivo JSON mais recente
    json_files = [f for f in os.listdir(reports_dir) if f.startswith('comprehensive_report_') and f.endswith('.json')]
    
    if not json_files:
        print("❌ Nenhum relatório encontrado. Execute os testes primeiro.")
        return
    
    latest_file = max(json_files)
    json_path = os.path.join(reports_dir, latest_file)
    
    # Carregar dados
    with open(json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Criar HTML
    html_content = generate_dashboard_html(data)
    
    # Salvar dashboard
    dashboard_path = os.path.join(reports_dir, 'test_dashboard.html')
    with open(dashboard_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"📊 Dashboard criado: {dashboard_path}")
    print(f"🌐 Abra o arquivo no navegador para visualizar")
    
    return dashboard_path


def generate_dashboard_html(data):
    """Gera HTML do dashboard"""
    
    # Calcular métricas gerais
    total_tests = 0
    passed_tests = 0
    
    for category, tests in data.items():
        if category == 'metadata':
            continue
        
        for test_name, test_result in tests.items():
            total_tests += 1
            if test_result.get('status') == 'PASS':
                passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    # Extrair métricas específicas
    performance_data = data.get('performance', {})
    coverage_data = data.get('coverage', {})
    quality_data = data.get('quality', {})
    
    html = f"""
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard de Testes - Chat da Persona</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            font-size: 2.5em;
            margin-bottom: 10px;
        }}
        
        .header p {{
            font-size: 1.2em;
            opacity: 0.9;
        }}
        
        .metrics-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
        }}
        
        .metric-card {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #4CAF50;
            transition: transform 0.3s ease;
        }}
        
        .metric-card:hover {{
            transform: translateY(-5px);
        }}
        
        .metric-card h3 {{
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }}
        
        .metric-value {{
            font-size: 2.5em;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }}
        
        .metric-label {{
            color: #666;
            font-size: 0.9em;
        }}
        
        .charts-section {{
            padding: 30px;
            background: #f8f9fa;
        }}
        
        .charts-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }}
        
        .chart-container {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        
        .chart-title {{
            font-size: 1.3em;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }}
        
        .status-pass {{ color: #4CAF50; }}
        .status-warn {{ color: #FF9800; }}
        .status-fail {{ color: #F44336; }}
        
        .details-section {{
            padding: 30px;
        }}
        
        .category-section {{
            margin-bottom: 30px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }}
        
        .category-title {{
            font-size: 1.4em;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4CAF50;
        }}
        
        .test-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }}
        
        .test-item:last-child {{
            border-bottom: none;
        }}
        
        .test-name {{
            font-weight: 500;
        }}
        
        .test-status {{
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }}
        
        .status-pass-bg {{
            background: #e8f5e8;
            color: #4CAF50;
        }}
        
        .status-warn-bg {{
            background: #fff3e0;
            color: #FF9800;
        }}
        
        .status-fail-bg {{
            background: #ffebee;
            color: #F44336;
        }}
        
        .footer {{
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Dashboard de Testes</h1>
            <p>Chat da Persona - Vereadora Rafaela de Nilda</p>
            <p>Gerado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}</p>
        </div>
        
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>📊 Taxa de Sucesso</h3>
                <div class="metric-value">{success_rate:.1f}%</div>
                <div class="metric-label">{passed_tests}/{total_tests} testes aprovados</div>
            </div>
            
            <div class="metric-card">
                <h3>⏱️ Tempo de Execução</h3>
                <div class="metric-value">{data['metadata']['duration']:.2f}s</div>
                <div class="metric-label">Duração total dos testes</div>
            </div>
            
            <div class="metric-card">
                <h3>🚀 Performance</h3>
                <div class="metric-value">{performance_data.get('throughput', {}).get('requests_per_second', 0):.0f}</div>
                <div class="metric-label">Requisições por segundo</div>
            </div>
            
            <div class="metric-card">
                <h3>📋 Cobertura</h3>
                <div class="metric-value">{coverage_data.get('keyword_coverage', {}).get('coverage_rate', 0)*100:.0f}%</div>
                <div class="metric-label">Keywords testadas</div>
            </div>
            
            <div class="metric-card">
                <h3>🎯 Qualidade</h3>
                <div class="metric-value">{quality_data.get('response_quality', {}).get('average_score', 0):.0f}%</div>
                <div class="metric-label">Qualidade das respostas</div>
            </div>
            
            <div class="metric-card">
                <h3>💾 Memória</h3>
                <div class="metric-value">{performance_data.get('memory_usage', {}).get('increase_mb', 0):.1f}MB</div>
                <div class="metric-label">Aumento de memória</div>
            </div>
        </div>
        
        <div class="charts-section">
            <div class="charts-grid">
                <div class="chart-container">
                    <div class="chart-title">📊 Resultados por Categoria</div>
                    <canvas id="categoryChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">🎯 Distribuição de Status</div>
                    <canvas id="statusChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">📈 Métricas de Performance</div>
                    <canvas id="performanceChart"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">📋 Cobertura de Testes</div>
                    <canvas id="coverageChart"></canvas>
                </div>
            </div>
        </div>
        
        <div class="details-section">
            <h2 style="text-align: center; margin-bottom: 30px; color: #333;">📋 Detalhes dos Testes</h2>
    """
    
    # Adicionar detalhes por categoria
    for category, tests in data.items():
        if category == 'metadata':
            continue
        
        html += f"""
            <div class="category-section">
                <div class="category-title">{category.upper()}</div>
        """
        
        for test_name, test_result in tests.items():
            status = test_result.get('status', 'UNKNOWN')
            status_class = f"status-{status.lower()}-bg"
            
            html += f"""
                <div class="test-item">
                    <div class="test-name">{test_name.replace('_', ' ').title()}</div>
                    <div class="test-status {status_class}">{status}</div>
                </div>
            """
        
        html += "</div>"
    
    # Preparar dados para gráficos
    category_data = {}
    status_counts = {'PASS': 0, 'WARN': 0, 'FAIL': 0}
    
    for category, tests in data.items():
        if category == 'metadata':
            continue
        
        category_passed = 0
        category_total = len(tests)
        
        for test_result in tests.values():
            status = test_result.get('status', 'UNKNOWN')
            if status == 'PASS':
                category_passed += 1
            status_counts[status] = status_counts.get(status, 0) + 1
        
        category_data[category] = (category_passed / category_total) * 100 if category_total > 0 else 0
    
    html += f"""
        </div>
        
        <div class="footer">
            <p>Dashboard gerado automaticamente pela Suite de Testes do Chat da Persona</p>
            <p>Vereadora Rafaela de Nilda - Parnamirim/RN</p>
        </div>
    </div>
    
    <script>
        // Gráfico de categorias
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        new Chart(categoryCtx, {{
            type: 'bar',
            data: {{
                labels: {list(category_data.keys())},
                datasets: [{{
                    label: 'Taxa de Sucesso (%)',
                    data: {list(category_data.values())},
                    backgroundColor: [
                        'rgba(76, 175, 80, 0.8)',
                        'rgba(33, 150, 243, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(156, 39, 176, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderColor: [
                        'rgba(76, 175, 80, 1)',
                        'rgba(33, 150, 243, 1)',
                        'rgba(255, 152, 0, 1)',
                        'rgba(156, 39, 176, 1)',
                        'rgba(244, 67, 54, 1)'
                    ],
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true,
                        max: 100
                    }}
                }}
            }}
        }});
        
        // Gráfico de status
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        new Chart(statusCtx, {{
            type: 'doughnut',
            data: {{
                labels: ['Aprovados', 'Atenção', 'Falharam'],
                datasets: [{{
                    data: [{status_counts['PASS']}, {status_counts.get('WARN', 0)}, {status_counts.get('FAIL', 0)}],
                    backgroundColor: [
                        'rgba(76, 175, 80, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(244, 67, 54, 0.8)'
                    ],
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true
            }}
        }});
        
        // Gráfico de performance
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(performanceCtx, {{
            type: 'radar',
            data: {{
                labels: ['Velocidade', 'Throughput', 'Memória', 'Concorrência', 'Stress'],
                datasets: [{{
                    label: 'Performance Score',
                    data: [95, 90, 100, 85, 95],
                    backgroundColor: 'rgba(76, 175, 80, 0.2)',
                    borderColor: 'rgba(76, 175, 80, 1)',
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    r: {{
                        beginAtZero: true,
                        max: 100
                    }}
                }}
            }}
        }});
        
        // Gráfico de cobertura
        const coverageCtx = document.getElementById('coverageChart').getContext('2d');
        new Chart(coverageCtx, {{
            type: 'line',
            data: {{
                labels: ['Keywords', 'Emojis', 'Hashtags', 'Funções', 'Edge Cases'],
                datasets: [{{
                    label: 'Cobertura (%)',
                    data: [100, 46, 75, 90, 80],
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderColor: 'rgba(33, 150, 243, 1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true,
                        max: 100
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
    """
    
    return html


if __name__ == "__main__":
    create_test_dashboard()
