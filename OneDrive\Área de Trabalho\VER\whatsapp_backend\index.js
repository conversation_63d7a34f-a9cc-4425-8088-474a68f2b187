const express = require('express');
const path = require('path');
const fs = require('fs');
const wppconnect = require('@wppconnect-team/wppconnect');
const axios = require('axios');
const { expressjwt: jwt } = require("express-jwt");
const jsonwebtoken = require('jsonwebtoken');
require('dotenv').config({ path: require('path').resolve(__dirname, '..', '.env') });

let wppStatus = 'DISCONNECTED';
let qrCodeData = null;
let wppClientInstance;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 5000; // 5 segundos

const PORT = process.env.PORT || 3001;

function createApp(mockClient = null) {
    const app = express();
    app.use(express.json());

    const SECRET_KEY = process.env.SECRET_KEY;
    if (!SECRET_KEY) {
        throw new Error("A variável de ambiente SECRET_KEY deve ser definida.");
    }
    const auth = jwt({ secret: SECRET_KEY, algorithms: ["HS256"] });

    // Use mock client for testing or real client for production
    if (mockClient) {
        wppClientInstance = mockClient;
        wppStatus = mockClient.isConnected ? 'CONNECTED' : 'DISCONNECTED';
    }

    app.get('/', (req, res) => res.send('Servidor de integração WhatsApp está ativo!'));

    app.get('/status', (req, res) => {
        res.json({ status: wppStatus });
    });

    app.get('/qr-code', (req, res) => {
        if (wppStatus === 'QR_CODE_NEEDED' && qrCodeData) {
            res.json({ qrCode: qrCodeData });
        } else {
            res.status(404).json({ message: 'QR Code não disponível no momento.' });
        }
    });

    app.get('/conversations', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'Cliente não está conectado.' });
        }
        try {
            const chats = await wppClientInstance.listChats({ count: 50 });
            res.json(chats);
        } catch (error) {
            console.error('Erro ao buscar conversas:', error);
            res.status(500).json({ message: 'Erro interno ao buscar conversas.' });
        }
    });

    app.post('/send-message', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'Cliente não está conectado.' });
        }
        const { chatId, message } = req.body;
        if (!chatId || !message) {
            return res.status(400).json({ message: 'chatId e message são obrigatórios.' });
        }
        try {
            await wppClientInstance.sendText(chatId, message);
            res.status(200).json({ success: true, message: 'Mensagem enviada com sucesso.' });
        } catch (error) {
            console.error('Erro ao enviar mensagem:', error);
            res.status(500).json({ message: 'Erro interno ao enviar mensagem.' });
        }
    });

    app.use((err, req, res, next) => {
        if (err.name === 'UnauthorizedError') {
            return res.status(401).send({ message: err.message });
        }
        next(err);
    });

    return app;
}

function initializeApp() {
    const app = createApp();

    app.listen(PORT, () => {
        console.log(`Servidor Express rodando na porta ${PORT}.`);
        startWppClient();
    });
}

async function startWppClient() {
    try {
        wppClientInstance = await wppconnect.create({
            session: 'whatsapp-session',
            catchQR: (base64Qr, asciiQR) => {
                console.log(asciiQR);
                qrCodeData = base64Qr;
                wppStatus = 'QR_CODE_NEEDED';
                console.log('📱 QR Code gerado. Escaneie em até 60 segundos.');
            },
            statusFind: (statusSession, session) => {
                console.log('Status da sessão:', statusSession);
                if (statusSession === 'isConnected' || statusSession === 'inChat') {
                    wppStatus = 'CONNECTED';
                    qrCodeData = null;
                    reconnectAttempts = 0; // Reset contador de tentativas
                    console.log('✅ WhatsApp conectado com sucesso!');
                } else if (statusSession === 'qrReadError' || statusSession === 'autocloseCalled' || statusSession === 'browserClose') {
                    wppStatus = 'DISCONNECTED';
                    qrCodeData = null;
                    console.log('❌ Falha na conexão. Tentando reconectar...');
                    scheduleReconnect();
                } else {
                    wppStatus = statusSession;
                }
            },
            headless: true,
            devtools: false,
            useChrome: true,
            debug: false,
            logQR: false, // Não mostrar QR no console (já temos no asciiQR)
            logLevel: 'error',
            browserArgs: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ],
            disableWelcome: true,
            autoClose: 90000, // Aumentar para 90 segundos
            createPathFileToken: true,
        });

        wppClientInstance.onMessage(async (message) => {
            if (!message.isGroupMsg && message.body) {
                try {
                    const response = await axios.post('http://backend-python:8000/chat/', {
                        text: message.body,
                        session_id: message.from
                    });
                    await wppClientInstance.sendText(message.from, response.data.response);
                } catch (error) {
                    console.error('Erro ao processar mensagem:', error);
                }
            }
        });

    } catch (error) {
        console.error('Erro ao iniciar o cliente WPPConnect:', error);
        wppStatus = 'ERROR';
    }
}

// Export for testing
module.exports = { createApp };

// Initialize app when running directly (not in tests)
if (require.main === module) {
    initializeApp();
}