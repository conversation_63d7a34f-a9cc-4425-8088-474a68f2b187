const express = require('express');
const path = require('path');
const fs = require('fs');
const wppconnect = require('@wppconnect-team/wppconnect');
const axios = require('axios');
const { expressjwt: jwt } = require("express-jwt");
const jsonwebtoken = require('jsonwebtoken');
require('dotenv').config({ path: require('path').resolve(__dirname, '..', '.env') });

let wppStatus = 'DISCONNECTED'; // Status real - sem forçar conexão
let qrCodeData = null;
let wppClientInstance;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 5000; // 5 segundos

// Removidos dados simulados - sistema agora usa apenas dados reais

const PORT = process.env.PORT || 3001;

// Função para sincronizar mensagens de uma conversa específica
async function syncConversationMessages(chatId) {
    if (!wppClientInstance) {
        console.log('❌ Cliente WPP não disponível para sincronização');
        return [];
    }

    try {
        console.log(`🔄 Sincronizando mensagens da conversa: ${chatId}`);

        // Buscar mensagens da conversa usando método disponível
        let messages = [];

        try {
            // Tentar getAllMessagesInChat primeiro
            messages = await wppClientInstance.getAllMessagesInChat(chatId, true, false);
            console.log(`📨 ${messages.length} mensagens encontradas com getAllMessagesInChat`);
        } catch (getAllError) {
            console.log(`ℹ️  getAllMessagesInChat não disponível, tentando alternativa`);

            // Tentar método alternativo
            try {
                const chat = await wppClientInstance.getChatById(chatId);
                if (chat && chat.msgs) {
                    messages = chat.msgs._models || [];
                    console.log(`📨 ${messages.length} mensagens encontradas via getChatById`);
                }
            } catch (getChatError) {
                console.log(`ℹ️  getChatById também não funcionou: ${getChatError.message}`);
                return [];
            }
        }

        // Se conseguimos poucas mensagens, tentar carregar mais
        if (messages.length > 0 && messages.length < 20) {
            console.log(`ℹ️  Poucas mensagens encontradas (${messages.length}), isso é normal para conversas novas`);
        }

        return messages;
    } catch (error) {
        console.error(`❌ Erro ao sincronizar mensagens de ${chatId}:`, error.message);
        return [];
    }
}

// Função para aguardar inicialização completa
async function waitForFullInitialization(maxWaitTime = 30000) {
    console.log('⏳ Aguardando inicialização completa do WhatsApp...');

    const startTime = Date.now();
    while (Date.now() - startTime < maxWaitTime) {
        try {
            // Tentar uma operação simples para verificar se está pronto
            const hostDevice = await wppClientInstance.getHostDevice();
            if (hostDevice && hostDevice.id) {
                console.log(`✅ WhatsApp totalmente inicializado! Dispositivo: ${hostDevice.id.user}`);
                return true;
            }
        } catch (error) {
            // Ainda não está pronto, aguardar mais
        }

        await new Promise(resolve => setTimeout(resolve, 2000)); // Aguardar 2 segundos
    }

    console.log('⚠️  Timeout na inicialização, continuando mesmo assim...');
    return false;
}

// Função para sincronizar todas as conversas com retry e delay
async function syncAllConversations() {
    if (!wppClientInstance) {
        console.log('❌ Cliente WPP não disponível para sincronização completa');
        return;
    }

    try {
        console.log('🔄 Iniciando sincronização ROBUSTA de conversas...');

        // Aguardar inicialização completa
        await waitForFullInitialization();

        // Aguardar mais um pouco para garantir que tudo está carregado
        console.log('⏳ Aguardando 5 segundos adicionais para estabilização...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Tentar diferentes métodos para buscar conversas com retry
        let chats = [];
        const maxRetries = 3;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            console.log(`🔄 Tentativa ${attempt}/${maxRetries} de buscar conversas...`);

            try {
                // Método mais simples primeiro
                console.log('📋 Tentando listChats básico...');
                chats = await wppClientInstance.listChats();

                if (chats && chats.length > 0) {
                    console.log(`📋 ✅ Sucesso: ${chats.length} conversas encontradas!`);
                    break;
                } else {
                    console.log('📋 ⚠️  Método retornou lista vazia');
                }
            } catch (error) {
                console.log(`📋 ❌ Tentativa ${attempt} falhou: ${error.message}`);

                if (attempt < maxRetries) {
                    console.log(`⏳ Aguardando 3 segundos antes da próxima tentativa...`);
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }
            }
        }

        // Se ainda não conseguiu, tentar método alternativo
        if (!chats || chats.length === 0) {
            console.log('🔄 Tentando método alternativo: getChats...');
            try {
                // Método alternativo usando getChats se existir
                if (typeof wppClientInstance.getChats === 'function') {
                    chats = await wppClientInstance.getChats();
                    console.log(`📋 Método alternativo: ${chats?.length || 0} conversas`);
                }
            } catch (error) {
                console.log(`⚠️  Método alternativo falhou: ${error.message}`);
            }
        }

        // Processar conversas encontradas
        if (chats && chats.length > 0) {
            console.log(`🎉 SUCESSO! ${chats.length} conversas encontradas!`);

            // Mostrar detalhes das primeiras conversas
            for (const chat of chats.slice(0, 5)) {
                try {
                    const chatInfo = {
                        id: chat.id?._serialized || chat.id || 'ID não disponível',
                        name: chat.name || chat.formattedTitle || chat.contact?.name || 'Sem nome',
                        isGroup: chat.isGroup || false,
                        unreadCount: chat.unreadCount || 0
                    };
                    console.log(`📱 Conversa: ${chatInfo.name} (${chatInfo.id})`);
                } catch (error) {
                    console.error(`❌ Erro ao processar conversa:`, error.message);
                }
            }
        } else {
            console.log('❌ Nenhuma conversa encontrada após todas as tentativas!');
            console.log('💡 Possíveis causas:');
            console.log('   - Conta realmente sem conversas');
            console.log('   - WhatsApp ainda sincronizando (aguarde mais tempo)');
            console.log('   - Incompatibilidade da versão do WPPConnect');
            console.log('   - Problema de permissões da API');
        }

        console.log(`✅ Sincronização finalizada: ${chats?.length || 0} conversas encontradas`);
        return chats || [];
    } catch (error) {
        console.error('❌ Erro na sincronização:', error.message);
        console.error('Stack trace:', error.stack);
        return [];
    }
}

// Função para configurar event handlers de mensagens em tempo real
function setupEventHandlers(client) {
    if (!client) {
        console.log('❌ Cliente WPP não fornecido para configurar event handlers');
        return;
    }

    console.log('🔧 Configurando event handlers para sincronização em tempo real...');

    // Event handler para mensagens recebidas
    client.onMessage(async (message) => {
        try {
            console.log(`📨 Nova mensagem recebida de ${message.from}: ${message.body}`);

            // Enviar mensagem para o backend Python para processamento
            await forwardMessageToPython(message);

        } catch (error) {
            console.error('❌ Erro ao processar mensagem recebida:', error);
        }
    });

    // Event handler para mensagens enviadas
    client.onAnyMessage(async (message) => {
        try {
            if (message.isGroupMsg) return; // Ignorar mensagens de grupo por enquanto

            console.log(`📤 Mensagem processada: ${message.from} -> ${message.body?.substring(0, 50)}...`);

        } catch (error) {
            console.error('❌ Erro ao processar mensagem:', error);
        }
    });

    // Iniciar sincronização robusta após configurar handlers
    setTimeout(async () => {
        console.log('🔄 Iniciando sincronização robusta após delay...');
        await syncAllConversations();
    }, 10000); // Aguardar 10 segundos para garantir inicialização completa

    console.log('✅ Event handlers configurados com sucesso');
}

// Função para encaminhar mensagem para o backend Python e processar resposta
async function forwardMessageToPython(message) {
    try {
        // Ignorar mensagens de grupo
        if (message.isGroupMsg) {
            console.log('ℹ️  Ignorando mensagem de grupo');
            return;
        }

        // Verificar se a mensagem tem conteúdo
        if (!message.body || message.body.trim() === '') {
            console.log('ℹ️  Ignorando mensagem sem conteúdo');
            return;
        }

        // Extrair informações do contato
        const contactId = message.from.replace('@c.us', '');
        const contactName = message.notifyName || message.from;

        console.log('🔄 Processando mensagem:', {
            from: contactId,
            name: contactName,
            body: message.body.substring(0, 100) + (message.body.length > 100 ? '...' : ''),
            timestamp: new Date().toISOString()
        });

        // Enviar para o backend Python para processamento
        const response = await axios.post('http://localhost:8000/chat/', {
            query: message.body,
            contact_id: contactId,
            contact_name: contactName
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 10000
        });

        if (response.data && response.data.response) {
            console.log('✅ Resposta do chatbot:', response.data.response.substring(0, 100) + '...');

            // Enviar resposta de volta para o WhatsApp
            await wppClientInstance.sendText(message.from, response.data.response);
            console.log('📤 Resposta enviada para:', contactName);
        } else {
            console.log('⚠️  Backend não retornou resposta válida');
        }

    } catch (error) {
        console.error('❌ Erro ao processar mensagem:', error.message);

        // Enviar mensagem de erro para o usuário
        try {
            await wppClientInstance.sendText(message.from, 'Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente em alguns instantes.');
            console.log('📤 Mensagem de erro enviada');
        } catch (sendError) {
            console.error('❌ Erro ao enviar mensagem de erro:', sendError.message);
        }
    }
}

function createApp(mockClient = null) {
    const app = express();
    app.use(express.json());

    const SECRET_KEY = process.env.SECRET_KEY;
    if (!SECRET_KEY) {
        throw new Error("A variável de ambiente SECRET_KEY deve ser definida.");
    }
    const auth = jwt({ secret: SECRET_KEY, algorithms: ["HS256"] });

    // Use mock client for testing or real client for production
    if (mockClient) {
        wppClientInstance = mockClient;
        wppStatus = mockClient.isConnected ? 'CONNECTED' : 'DISCONNECTED';
    }

    app.get('/', (req, res) => res.send('Servidor de integração WhatsApp está ativo!'));

    app.get('/status', (req, res) => {
        // Retornar status real do WhatsApp
        const currentStatus = wppStatus === 'isLogged' ? 'CONNECTED' : wppStatus;
        console.log(`📊 Status real do WhatsApp: ${currentStatus}`);
        res.json({ status: currentStatus });
    });

    app.get('/qr-code', (req, res) => {
        if (wppStatus === 'QR_CODE_NEEDED' && qrCodeData) {
            res.json({ qrCode: qrCodeData });
        } else {
            res.status(404).json({ message: 'QR Code não disponível no momento.' });
        }
    });

    // Endpoint para forçar reconexão
    app.post('/reconnect', auth, (req, res) => {
        console.log('🔄 Reconexão forçada solicitada via API');
        resetReconnectAttempts();

        if (wppClientInstance) {
            try {
                wppClientInstance.close();
            } catch (error) {
                console.log('Erro ao fechar cliente anterior:', error.message);
            }
        }

        wppStatus = 'RECONNECTING';
        startWppClient();

        res.json({
            message: 'Reconexão iniciada',
            status: wppStatus,
            attempts: reconnectAttempts,
            maxAttempts: MAX_RECONNECT_ATTEMPTS
        });
    });

    app.get('/conversations', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            console.log('📋 WhatsApp não conectado, retornando lista vazia');
            return res.json([]);
        }

        try {
            console.log('📋 Buscando conversas reais do WhatsApp...');
            const chats = await wppClientInstance.listChats({ count: 50 });
            console.log(`✅ ${chats.length} conversas encontradas`);
            res.json(chats);
        } catch (error) {
            console.error('❌ Erro ao buscar conversas:', error);
            // Retornar lista vazia em caso de erro (sem dados simulados)
            console.log('📋 Retornando lista vazia devido ao erro');
            res.json([]);
        }
    });

    // Endpoint para sincronizar conversas com o backend Python
    app.post('/sync-conversations', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'Cliente não está conectado.' });
        }

        try {
            console.log('🔄 Iniciando sincronização de conversas...');
            const chats = await wppClientInstance.listChats({ count: 100 });

            let syncedCount = 0;
            let errorCount = 0;

            for (const chat of chats) {
                try {
                    if (!chat.isGroup && chat.contact) {
                        const contactId = chat.id._serialized.replace('@c.us', '');
                        const contactName = chat.contact.pushname || chat.contact.name || contactId;
                        const lastMessageTime = chat.lastMessage ? new Date(chat.lastMessage.timestamp * 1000).toISOString() : new Date().toISOString();

                        // Sincronizar com backend Python
                        await axios.post('http://localhost:8000/sync-conversation', {
                            contact_id: contactId,
                            contact_name: contactName,
                            last_message_at: lastMessageTime,
                            whatsapp_data: {
                                unreadCount: chat.unreadCount || 0,
                                isArchived: chat.archive || false,
                                isPinned: chat.pin || false
                            }
                        });

                        syncedCount++;
                        console.log(`✅ Conversa sincronizada: ${contactName} (${contactId})`);
                    }
                } catch (error) {
                    errorCount++;
                    console.error(`❌ Erro ao sincronizar conversa ${chat.id._serialized}:`, error.message);
                }
            }

            console.log(`🎯 Sincronização concluída: ${syncedCount} sucessos, ${errorCount} erros`);
            res.json({
                message: 'Sincronização concluída',
                synced: syncedCount,
                errors: errorCount,
                total: chats.length
            });
        } catch (error) {
            console.error('❌ Erro na sincronização:', error);
            res.status(500).json({ message: 'Erro interno na sincronização.' });
        }
    });

    app.post('/send-message', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'Cliente não está conectado.' });
        }
        const { chatId, message } = req.body;
        if (!chatId || !message) {
            return res.status(400).json({ message: 'chatId e message são obrigatórios.' });
        }
        try {
            await wppClientInstance.sendText(chatId, message);
            res.status(200).json({ success: true, message: 'Mensagem enviada com sucesso.' });
        } catch (error) {
            console.error('Erro ao enviar mensagem:', error);
            res.status(500).json({ message: 'Erro interno ao enviar mensagem.' });
        }
    });

    // Endpoint para sincronizar mensagens de uma conversa específica
    app.get('/conversations/:chatId/sync', auth, async (req, res) => {
        const { chatId } = req.params;

        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'WhatsApp não está conectado.' });
        }

        try {
            console.log(`🔄 Sincronizando mensagens para: ${chatId}`);
            const messages = await syncConversationMessages(chatId);

            res.json({
                chatId,
                messageCount: messages.length,
                messages: messages.slice(-20) // Retornar apenas as últimas 20 mensagens
            });
        } catch (error) {
            console.error(`❌ Erro ao sincronizar conversa ${chatId}:`, error);
            res.status(500).json({ message: 'Erro ao sincronizar conversa.' });
        }
    });

    // Endpoint para obter mensagens de uma conversa
    app.get('/conversations/:chatId/messages', auth, async (req, res) => {
        const { chatId } = req.params;
        const { limit = 1000, includeAll = 'true' } = req.query; // Aumentar limite padrão

        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'WhatsApp não está conectado.' });
        }

        try {
            console.log(`📨 Buscando TODAS as mensagens para: ${chatId}`);

            let allMessages = [];

            // Método 1: Carregamento agressivo de histórico
            try {
                console.log(`🔄 Iniciando carregamento agressivo de histórico para ${chatId}`);

                // Primeiro, tentar carregar o chat e forçar carregamento de mensagens antigas
                const chat = await wppClientInstance.getChatById(chatId);
                if (chat) {
                    console.log(`📱 Chat encontrado: ${chat.name || 'Sem nome'}`);

                    // Tentar múltiplas estratégias de carregamento
                    let loadAttempts = 0;
                    const maxAttempts = 5;

                    while (loadAttempts < maxAttempts) {
                        try {
                            console.log(`🔄 Tentativa ${loadAttempts + 1}/${maxAttempts} de carregar histórico...`);

                            // Método A: loadEarlierMessages
                            if (typeof chat.loadEarlierMessages === 'function') {
                                await chat.loadEarlierMessages();
                                console.log(`📨 loadEarlierMessages executado`);
                            }

                            // Método B: loadAllEarlierMessages se disponível
                            if (typeof wppClientInstance.loadAllEarlierMessages === 'function') {
                                await wppClientInstance.loadAllEarlierMessages(chatId);
                                console.log(`📨 loadAllEarlierMessages executado`);
                            }

                            // Método C: Forçar carregamento via scroll
                            if (typeof chat.loadEarlierMessages === 'function') {
                                await chat.loadEarlierMessages();
                                await new Promise(resolve => setTimeout(resolve, 1000)); // Aguardar 1 segundo
                            }

                            loadAttempts++;

                            // Verificar se carregou mais mensagens
                            const currentMessages = await wppClientInstance.getAllMessagesInChat(chatId, true, false);
                            console.log(`📊 Após tentativa ${loadAttempts}: ${currentMessages.length} mensagens`);

                            if (currentMessages.length > allMessages.length) {
                                allMessages = currentMessages;
                                console.log(`✅ Progresso: +${currentMessages.length - allMessages.length} mensagens`);
                            } else if (loadAttempts > 2) {
                                console.log(`ℹ️  Sem progresso após ${loadAttempts} tentativas, parando`);
                                break;
                            }

                        } catch (loadError) {
                            console.log(`⚠️  Erro na tentativa ${loadAttempts + 1}: ${loadError.message}`);
                            loadAttempts++;
                        }
                    }
                }

                // Buscar mensagens finais
                allMessages = await wppClientInstance.getAllMessagesInChat(chatId, true, false);
                console.log(`📨 Método 1 FINAL: ${allMessages.length} mensagens encontradas após carregamento agressivo`);

            } catch (error) {
                console.log(`⚠️  Método 1 falhou: ${error.message}`);

                // Método 2: Tentar buscar via getChatById
                try {
                    console.log(`🔄 Tentando método 2: getChatById`);
                    const chat = await wppClientInstance.getChatById(chatId);
                    if (chat) {
                        // Tentar carregar mensagens anteriores primeiro
                        if (typeof chat.loadEarlierMessages === 'function') {
                            try {
                                await chat.loadEarlierMessages();
                                console.log(`📨 Mensagens anteriores carregadas`);
                            } catch (loadError) {
                                console.log(`ℹ️  Não foi possível carregar mensagens anteriores: ${loadError.message}`);
                            }
                        }

                        // Buscar mensagens do chat
                        if (chat.msgs && chat.msgs._models) {
                            allMessages = chat.msgs._models;
                            console.log(`📨 Método 2 (getChatById): ${allMessages.length} mensagens encontradas`);
                        } else if (chat.messages) {
                            allMessages = chat.messages;
                            console.log(`📨 Método 2 (chat.messages): ${allMessages.length} mensagens encontradas`);
                        }
                    }
                } catch (error2) {
                    console.log(`⚠️  Método 2 falhou: ${error2.message}`);

                    // Método 3: Tentar método alternativo
                    try {
                        console.log(`🔄 Tentando método 3: loadAllEarlierChatMessages`);
                        if (typeof wppClientInstance.loadAllEarlierChatMessages === 'function') {
                            const earlierMessages = await wppClientInstance.loadAllEarlierChatMessages(chatId);
                            allMessages = earlierMessages || [];
                            console.log(`📨 Método 3: ${allMessages.length} mensagens encontradas`);
                        }
                    } catch (error3) {
                        console.log(`⚠️  Método 3 falhou: ${error3.message}`);
                        console.log(`ℹ️  Retornando lista vazia - nenhum método funcionou`);
                    }
                }
            }

            // Processar e filtrar mensagens
            const processedMessages = allMessages.map(msg => ({
                id: msg.id || msg._serialized,
                body: msg.body || msg.content || '',
                fromMe: msg.fromMe || false,
                timestamp: msg.timestamp || msg.t,
                type: msg.type || 'chat',
                author: msg.author || msg.from,
                from: msg.from,
                to: msg.to,
                isGroupMsg: msg.isGroupMsg || false,
                quotedMsg: msg.quotedMsg,
                mentionedJidList: msg.mentionedJidList || []
            }));

            // Ordenar por timestamp (mais antigas primeiro)
            processedMessages.sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));

            // Aplicar limite se especificado e não for "todas"
            let finalMessages = processedMessages;
            if (includeAll !== 'true' && limit && limit !== 'all') {
                const limitNum = parseInt(limit);
                finalMessages = processedMessages.slice(-limitNum); // Últimas N mensagens
            }

            console.log(`✅ Retornando ${finalMessages.length} de ${processedMessages.length} mensagens totais`);

            res.json({
                chatId,
                totalMessages: processedMessages.length,
                returnedMessages: finalMessages.length,
                messages: finalMessages,
                hasMore: finalMessages.length < processedMessages.length
            });
        } catch (error) {
            console.error(`❌ Erro ao buscar mensagens de ${chatId}:`, error);
            res.status(500).json({
                message: 'Erro ao buscar mensagens.',
                error: error.message
            });
        }
    });

    // Endpoint para forçar carregamento de histórico de uma conversa específica
    app.post('/conversations/:chatId/load-history', auth, async (req, res) => {
        const { chatId } = req.params;

        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'WhatsApp não está conectado.' });
        }

        try {
            console.log(`🔄 Forçando carregamento de histórico para: ${chatId}`);

            const chat = await wppClientInstance.getChatById(chatId);
            if (!chat) {
                return res.status(404).json({ message: 'Conversa não encontrada.' });
            }

            let totalLoaded = 0;
            const maxAttempts = 10;

            for (let i = 0; i < maxAttempts; i++) {
                try {
                    const beforeCount = await wppClientInstance.getAllMessagesInChat(chatId, true, false);

                    // Tentar diferentes métodos de carregamento
                    if (typeof chat.loadEarlierMessages === 'function') {
                        await chat.loadEarlierMessages();
                    }

                    if (typeof wppClientInstance.loadAllEarlierMessages === 'function') {
                        await wppClientInstance.loadAllEarlierMessages(chatId);
                    }

                    // Aguardar um pouco para o carregamento
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    const afterCount = await wppClientInstance.getAllMessagesInChat(chatId, true, false);
                    const loaded = afterCount.length - beforeCount.length;

                    if (loaded > 0) {
                        totalLoaded += loaded;
                        console.log(`📨 Carregadas +${loaded} mensagens (tentativa ${i + 1})`);
                    } else {
                        console.log(`ℹ️  Sem novas mensagens na tentativa ${i + 1}, parando`);
                        break;
                    }
                } catch (error) {
                    console.log(`⚠️  Erro na tentativa ${i + 1}: ${error.message}`);
                }
            }

            const finalMessages = await wppClientInstance.getAllMessagesInChat(chatId, true, false);

            res.json({
                chatId,
                totalMessagesLoaded: totalLoaded,
                finalMessageCount: finalMessages.length,
                message: `Histórico carregado com sucesso. ${totalLoaded} novas mensagens encontradas.`
            });
        } catch (error) {
            console.error(`❌ Erro ao carregar histórico de ${chatId}:`, error);
            res.status(500).json({
                message: 'Erro ao carregar histórico.',
                error: error.message
            });
        }
    });

    // Endpoint para sincronização completa
    app.post('/sync/all', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'WhatsApp não está conectado.' });
        }

        try {
            console.log('🔄 Iniciando sincronização completa...');
            // Executar sincronização em background
            syncAllConversations();

            res.json({ message: 'Sincronização completa iniciada em background.' });
        } catch (error) {
            console.error('❌ Erro ao iniciar sincronização completa:', error);
            res.status(500).json({ message: 'Erro ao iniciar sincronização.' });
        }
    });

    app.use((err, req, res, next) => {
        if (err.name === 'UnauthorizedError') {
            return res.status(401).send({ message: err.message });
        }
        next(err);
    });

    return app;
}

function initializeApp() {
    const app = createApp();

    app.listen(PORT, () => {
        console.log(`Servidor Express rodando na porta ${PORT}.`);
        console.log('🚀 Iniciando WPPConnect...');
        startWppClient();
    });
}

async function startWppClient() {
    try {
        wppClientInstance = await wppconnect.create({
            session: 'whatsapp-session',
            catchQR: (base64Qr, asciiQR) => {
                console.log(asciiQR);
                qrCodeData = base64Qr;
                wppStatus = 'QR_CODE_NEEDED';
                console.log('📱 QR Code gerado. Escaneie em até 60 segundos.');
            },
            statusFind: (statusSession, session) => {
                console.log('Status da sessão:', statusSession);
                if (statusSession === 'isConnected' || statusSession === 'inChat' || statusSession === 'isLogged') {
                    wppStatus = 'CONNECTED';
                    qrCodeData = null;
                    reconnectAttempts = 0; // Reset contador de tentativas
                    console.log('✅ WhatsApp conectado com sucesso!');

                    // Event handlers serão configurados após a criação completa do cliente
                } else if (statusSession === 'qrReadError' || statusSession === 'autocloseCalled' || statusSession === 'browserClose') {
                    wppStatus = 'DISCONNECTED';
                    qrCodeData = null;
                    console.log('❌ Falha na conexão. Tentando reconectar...');
                    scheduleReconnect();
                } else if (statusSession === 'notLogged') {
                    wppStatus = 'QR_CODE_NEEDED';
                    console.log('📱 QR Code necessário para autenticação');
                } else {
                    wppStatus = statusSession;
                    console.log(`📊 Status da sessão: ${statusSession}`);
                }
            },
            headless: true,
            devtools: false,
            useChrome: true,
            debug: false,
            logQR: false, // Não mostrar QR no console (já temos no asciiQR)
            logLevel: 'error',
            browserArgs: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ],
            disableWelcome: true,
            autoClose: 90000, // Aumentar para 90 segundos
            createPathFileToken: true,
        });

        // Configurar event handlers usando a função dedicada
        setupEventHandlers(wppClientInstance);

        console.log('🚀 Cliente WPPConnect iniciado com sucesso!');

    } catch (error) {
        console.error('❌ Erro ao iniciar o cliente WPPConnect:', error);
        wppStatus = 'ERROR';
        scheduleReconnect();
    }
}

function scheduleReconnect() {
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        reconnectAttempts++;
        console.log(`⏰ Reagendando reconexão em ${RECONNECT_DELAY/1000} segundos... (Tentativa ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);

        setTimeout(() => {
            console.log('🔄 Tentando reconectar...');
            startWppClient();
        }, RECONNECT_DELAY);
    } else {
        console.log('❌ Máximo de tentativas de reconexão atingido. Parando tentativas automáticas.');
        console.log('💡 Para tentar novamente, reinicie o servidor.');
        wppStatus = 'MAX_ATTEMPTS_REACHED';
    }
}

// Função para resetar tentativas de reconexão (pode ser chamada via endpoint)
function resetReconnectAttempts() {
    reconnectAttempts = 0;
    console.log('🔄 Contador de tentativas de reconexão resetado.');
}

// Export for testing
module.exports = { createApp };

// Initialize app when running directly (not in tests)
if (require.main === module) {
    initializeApp();
}