const express = require('express');
const path = require('path');
const fs = require('fs');
const wppconnect = require('@wppconnect-team/wppconnect');
const axios = require('axios');
const { expressjwt: jwt } = require("express-jwt");
const jsonwebtoken = require('jsonwebtoken');
require('dotenv').config({ path: require('path').resolve(__dirname, '..', '.env') });

let wppStatus = 'DISCONNECTED'; // Status real - sem forçar conexão
let qrCodeData = null;
let wppClientInstance;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 5000; // 5 segundos

// Removidos dados simulados - sistema agora usa apenas dados reais

const PORT = process.env.PORT || 3001;

// Função para sincronizar mensagens de uma conversa específica
async function syncConversationMessages(chatId) {
    if (!wppClientInstance) {
        console.log('❌ Cliente WPP não disponível para sincronização');
        return [];
    }

    try {
        console.log(`🔄 Sincronizando mensagens da conversa: ${chatId}`);

        // Buscar mensagens da conversa usando método disponível
        let messages = [];

        try {
            // Tentar getAllMessagesInChat primeiro
            messages = await wppClientInstance.getAllMessagesInChat(chatId, true, false);
            console.log(`📨 ${messages.length} mensagens encontradas com getAllMessagesInChat`);
        } catch (getAllError) {
            console.log(`ℹ️  getAllMessagesInChat não disponível, tentando alternativa`);

            // Tentar método alternativo
            try {
                const chat = await wppClientInstance.getChatById(chatId);
                if (chat && chat.msgs) {
                    messages = chat.msgs._models || [];
                    console.log(`📨 ${messages.length} mensagens encontradas via getChatById`);
                }
            } catch (getChatError) {
                console.log(`ℹ️  getChatById também não funcionou: ${getChatError.message}`);
                return [];
            }
        }

        // Se conseguimos poucas mensagens, tentar carregar mais
        if (messages.length > 0 && messages.length < 20) {
            console.log(`ℹ️  Poucas mensagens encontradas (${messages.length}), isso é normal para conversas novas`);
        }

        return messages;
    } catch (error) {
        console.error(`❌ Erro ao sincronizar mensagens de ${chatId}:`, error.message);
        return [];
    }
}

// Função para sincronizar todas as conversas
async function syncAllConversations() {
    if (!wppClientInstance) {
        console.log('❌ Cliente WPP não disponível para sincronização completa');
        return;
    }

    try {
        console.log('🔄 Iniciando sincronização completa de conversas...');

        // Buscar todas as conversas
        const chats = await wppClientInstance.listChats({ count: 100 });
        console.log(`📋 ${chats.length} conversas encontradas`);

        // Sincronizar mensagens de cada conversa (limitando para evitar sobrecarga)
        const maxChatsToSync = 10; // Limitar para performance
        const chatsToSync = chats.slice(0, maxChatsToSync);

        for (const chat of chatsToSync) {
            try {
                await syncConversationMessages(chat.id._serialized);
                // Pequena pausa entre sincronizações para não sobrecarregar
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`❌ Erro ao sincronizar conversa ${chat.id._serialized}:`, error);
            }
        }

        console.log('✅ Sincronização completa finalizada');
    } catch (error) {
        console.error('❌ Erro na sincronização completa:', error);
    }
}

// Função para configurar event handlers de mensagens em tempo real
function setupEventHandlers() {
    if (!wppClientInstance) {
        console.log('❌ Cliente WPP não disponível para configurar event handlers');
        return;
    }

    console.log('🔧 Configurando event handlers para sincronização em tempo real...');

    // Event handler para mensagens recebidas
    wppClientInstance.onMessage(async (message) => {
        try {
            console.log(`📨 Nova mensagem recebida de ${message.from}: ${message.body}`);

            // Enviar mensagem para o backend Python para processamento
            await forwardMessageToPython(message);

        } catch (error) {
            console.error('❌ Erro ao processar mensagem recebida:', error);
        }
    });

    // Event handler para mensagens enviadas
    wppClientInstance.onAnyMessage(async (message) => {
        try {
            if (message.isGroupMsg) return; // Ignorar mensagens de grupo por enquanto

            console.log(`📤 Mensagem processada: ${message.from} -> ${message.body?.substring(0, 50)}...`);

        } catch (error) {
            console.error('❌ Erro ao processar mensagem:', error);
        }
    });

    // Iniciar sincronização completa após configurar handlers
    setTimeout(() => {
        syncAllConversations();
    }, 5000); // Aguardar 5 segundos após conexão

    console.log('✅ Event handlers configurados com sucesso');
}

// Função para encaminhar mensagem para o backend Python
async function forwardMessageToPython(message) {
    try {
        const messageData = {
            from: message.from,
            body: message.body,
            timestamp: message.timestamp,
            isGroupMsg: message.isGroupMsg,
            type: message.type,
            chatId: message.chatId
        };

        // Enviar para o backend Python (endpoint a ser criado)
        const response = await axios.post('http://localhost:8000/whatsapp/message-received', messageData, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 5000
        });

        console.log('✅ Mensagem encaminhada para backend Python');
    } catch (error) {
        console.error('❌ Erro ao encaminhar mensagem para Python:', error.message);
    }
}

function createApp(mockClient = null) {
    const app = express();
    app.use(express.json());

    const SECRET_KEY = process.env.SECRET_KEY;
    if (!SECRET_KEY) {
        throw new Error("A variável de ambiente SECRET_KEY deve ser definida.");
    }
    const auth = jwt({ secret: SECRET_KEY, algorithms: ["HS256"] });

    // Use mock client for testing or real client for production
    if (mockClient) {
        wppClientInstance = mockClient;
        wppStatus = mockClient.isConnected ? 'CONNECTED' : 'DISCONNECTED';
    }

    app.get('/', (req, res) => res.send('Servidor de integração WhatsApp está ativo!'));

    app.get('/status', (req, res) => {
        // Retornar status real do WhatsApp
        const currentStatus = wppStatus === 'isLogged' ? 'CONNECTED' : wppStatus;
        console.log(`📊 Status real do WhatsApp: ${currentStatus}`);
        res.json({ status: currentStatus });
    });

    app.get('/qr-code', (req, res) => {
        if (wppStatus === 'QR_CODE_NEEDED' && qrCodeData) {
            res.json({ qrCode: qrCodeData });
        } else {
            res.status(404).json({ message: 'QR Code não disponível no momento.' });
        }
    });

    // Endpoint para forçar reconexão
    app.post('/reconnect', auth, (req, res) => {
        console.log('🔄 Reconexão forçada solicitada via API');
        resetReconnectAttempts();

        if (wppClientInstance) {
            try {
                wppClientInstance.close();
            } catch (error) {
                console.log('Erro ao fechar cliente anterior:', error.message);
            }
        }

        wppStatus = 'RECONNECTING';
        startWppClient();

        res.json({
            message: 'Reconexão iniciada',
            status: wppStatus,
            attempts: reconnectAttempts,
            maxAttempts: MAX_RECONNECT_ATTEMPTS
        });
    });

    app.get('/conversations', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            console.log('📋 WhatsApp não conectado, retornando lista vazia');
            return res.json([]);
        }

        try {
            console.log('📋 Buscando conversas reais do WhatsApp...');
            const chats = await wppClientInstance.listChats({ count: 50 });
            console.log(`✅ ${chats.length} conversas encontradas`);
            res.json(chats);
        } catch (error) {
            console.error('❌ Erro ao buscar conversas:', error);
            // Retornar lista vazia em caso de erro (sem dados simulados)
            console.log('📋 Retornando lista vazia devido ao erro');
            res.json([]);
        }
    });

    // Endpoint para sincronizar conversas com o backend Python
    app.post('/sync-conversations', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'Cliente não está conectado.' });
        }

        try {
            console.log('🔄 Iniciando sincronização de conversas...');
            const chats = await wppClientInstance.listChats({ count: 100 });

            let syncedCount = 0;
            let errorCount = 0;

            for (const chat of chats) {
                try {
                    if (!chat.isGroup && chat.contact) {
                        const contactId = chat.id._serialized.replace('@c.us', '');
                        const contactName = chat.contact.pushname || chat.contact.name || contactId;
                        const lastMessageTime = chat.lastMessage ? new Date(chat.lastMessage.timestamp * 1000).toISOString() : new Date().toISOString();

                        // Sincronizar com backend Python
                        await axios.post('http://localhost:8000/sync-conversation', {
                            contact_id: contactId,
                            contact_name: contactName,
                            last_message_at: lastMessageTime,
                            whatsapp_data: {
                                unreadCount: chat.unreadCount || 0,
                                isArchived: chat.archive || false,
                                isPinned: chat.pin || false
                            }
                        });

                        syncedCount++;
                        console.log(`✅ Conversa sincronizada: ${contactName} (${contactId})`);
                    }
                } catch (error) {
                    errorCount++;
                    console.error(`❌ Erro ao sincronizar conversa ${chat.id._serialized}:`, error.message);
                }
            }

            console.log(`🎯 Sincronização concluída: ${syncedCount} sucessos, ${errorCount} erros`);
            res.json({
                message: 'Sincronização concluída',
                synced: syncedCount,
                errors: errorCount,
                total: chats.length
            });
        } catch (error) {
            console.error('❌ Erro na sincronização:', error);
            res.status(500).json({ message: 'Erro interno na sincronização.' });
        }
    });

    app.post('/send-message', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'Cliente não está conectado.' });
        }
        const { chatId, message } = req.body;
        if (!chatId || !message) {
            return res.status(400).json({ message: 'chatId e message são obrigatórios.' });
        }
        try {
            await wppClientInstance.sendText(chatId, message);
            res.status(200).json({ success: true, message: 'Mensagem enviada com sucesso.' });
        } catch (error) {
            console.error('Erro ao enviar mensagem:', error);
            res.status(500).json({ message: 'Erro interno ao enviar mensagem.' });
        }
    });

    // Endpoint para sincronizar mensagens de uma conversa específica
    app.get('/conversations/:chatId/sync', auth, async (req, res) => {
        const { chatId } = req.params;

        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'WhatsApp não está conectado.' });
        }

        try {
            console.log(`🔄 Sincronizando mensagens para: ${chatId}`);
            const messages = await syncConversationMessages(chatId);

            res.json({
                chatId,
                messageCount: messages.length,
                messages: messages.slice(-20) // Retornar apenas as últimas 20 mensagens
            });
        } catch (error) {
            console.error(`❌ Erro ao sincronizar conversa ${chatId}:`, error);
            res.status(500).json({ message: 'Erro ao sincronizar conversa.' });
        }
    });

    // Endpoint para obter mensagens de uma conversa
    app.get('/conversations/:chatId/messages', auth, async (req, res) => {
        const { chatId } = req.params;
        const { limit = 50 } = req.query;

        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'WhatsApp não está conectado.' });
        }

        try {
            console.log(`📨 Buscando mensagens para: ${chatId}`);
            const messages = await wppClientInstance.getAllMessagesInChat(chatId, true, false);

            // Limitar número de mensagens retornadas
            const limitedMessages = messages.slice(-parseInt(limit));

            res.json({
                chatId,
                messageCount: messages.length,
                messages: limitedMessages
            });
        } catch (error) {
            console.error(`❌ Erro ao buscar mensagens de ${chatId}:`, error);
            res.status(500).json({ message: 'Erro ao buscar mensagens.' });
        }
    });

    // Endpoint para sincronização completa
    app.post('/sync/all', auth, async (req, res) => {
        if (!wppClientInstance || wppStatus !== 'CONNECTED') {
            return res.status(409).json({ message: 'WhatsApp não está conectado.' });
        }

        try {
            console.log('🔄 Iniciando sincronização completa...');
            // Executar sincronização em background
            syncAllConversations();

            res.json({ message: 'Sincronização completa iniciada em background.' });
        } catch (error) {
            console.error('❌ Erro ao iniciar sincronização completa:', error);
            res.status(500).json({ message: 'Erro ao iniciar sincronização.' });
        }
    });

    app.use((err, req, res, next) => {
        if (err.name === 'UnauthorizedError') {
            return res.status(401).send({ message: err.message });
        }
        next(err);
    });

    return app;
}

function initializeApp() {
    const app = createApp();

    app.listen(PORT, () => {
        console.log(`Servidor Express rodando na porta ${PORT}.`);
        console.log('🚀 Iniciando WPPConnect...');
        startWppClient();
    });
}

async function startWppClient() {
    try {
        wppClientInstance = await wppconnect.create({
            session: 'whatsapp-session',
            catchQR: (base64Qr, asciiQR) => {
                console.log(asciiQR);
                qrCodeData = base64Qr;
                wppStatus = 'QR_CODE_NEEDED';
                console.log('📱 QR Code gerado. Escaneie em até 60 segundos.');
            },
            statusFind: (statusSession, session) => {
                console.log('Status da sessão:', statusSession);
                if (statusSession === 'isConnected' || statusSession === 'inChat' || statusSession === 'isLogged') {
                    wppStatus = 'CONNECTED';
                    qrCodeData = null;
                    reconnectAttempts = 0; // Reset contador de tentativas
                    console.log('✅ WhatsApp conectado com sucesso!');

                    // Configurar event handlers para sincronização em tempo real
                    setupEventHandlers();
                } else if (statusSession === 'qrReadError' || statusSession === 'autocloseCalled' || statusSession === 'browserClose') {
                    wppStatus = 'DISCONNECTED';
                    qrCodeData = null;
                    console.log('❌ Falha na conexão. Tentando reconectar...');
                    scheduleReconnect();
                } else if (statusSession === 'notLogged') {
                    wppStatus = 'QR_CODE_NEEDED';
                    console.log('📱 QR Code necessário para autenticação');
                } else {
                    wppStatus = statusSession;
                    console.log(`📊 Status da sessão: ${statusSession}`);
                }
            },
            headless: true,
            devtools: false,
            useChrome: true,
            debug: false,
            logQR: false, // Não mostrar QR no console (já temos no asciiQR)
            logLevel: 'error',
            browserArgs: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ],
            disableWelcome: true,
            autoClose: 90000, // Aumentar para 90 segundos
            createPathFileToken: true,
        });

        wppClientInstance.onMessage(async (message) => {
            console.log('📨 Nova mensagem recebida:', {
                from: message.from,
                body: message.body,
                isGroup: message.isGroupMsg,
                timestamp: new Date().toISOString()
            });

            if (!message.isGroupMsg && message.body) {
                try {
                    // Extrair informações do contato
                    const contactId = message.from.replace('@c.us', '');
                    const contactName = message.notifyName || message.from;

                    console.log('🔄 Enviando para backend Python:', {
                        query: message.body,
                        contact_id: contactId,
                        contact_name: contactName
                    });

                    const response = await axios.post('http://localhost:8000/chat/', {
                        query: message.body,
                        contact_id: contactId,
                        contact_name: contactName
                    });

                    console.log('✅ Resposta do backend:', response.data.response);
                    await wppClientInstance.sendText(message.from, response.data.response);
                    console.log('📤 Mensagem enviada para:', message.from);
                } catch (error) {
                    console.error('❌ Erro ao processar mensagem:', error.message);
                    // Enviar mensagem de erro para o usuário
                    try {
                        await wppClientInstance.sendText(message.from, 'Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente em alguns instantes.');
                    } catch (sendError) {
                        console.error('❌ Erro ao enviar mensagem de erro:', sendError.message);
                    }
                }
            }
        });

        console.log('🚀 Cliente WPPConnect iniciado com sucesso!');

    } catch (error) {
        console.error('❌ Erro ao iniciar o cliente WPPConnect:', error);
        wppStatus = 'ERROR';
        scheduleReconnect();
    }
}

function scheduleReconnect() {
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
        reconnectAttempts++;
        console.log(`⏰ Reagendando reconexão em ${RECONNECT_DELAY/1000} segundos... (Tentativa ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);

        setTimeout(() => {
            console.log('🔄 Tentando reconectar...');
            startWppClient();
        }, RECONNECT_DELAY);
    } else {
        console.log('❌ Máximo de tentativas de reconexão atingido. Parando tentativas automáticas.');
        console.log('💡 Para tentar novamente, reinicie o servidor.');
        wppStatus = 'MAX_ATTEMPTS_REACHED';
    }
}

// Função para resetar tentativas de reconexão (pode ser chamada via endpoint)
function resetReconnectAttempts() {
    reconnectAttempts = 0;
    console.log('🔄 Contador de tentativas de reconexão resetado.');
}

// Export for testing
module.exports = { createApp };

// Initialize app when running directly (not in tests)
if (require.main === module) {
    initializeApp();
}