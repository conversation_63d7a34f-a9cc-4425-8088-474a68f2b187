import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import userEvent from '@testing-library/user-event';
import DocumentList from './DocumentList';
import axiosInstance from '../../api/axiosInstance';

// Mocking axiosInstance
jest.mock('../../api/axiosInstance');
const mockedAxios = axiosInstance;

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false, // Desativa retentativas para testes
    },
  },
});

const wrapper = ({ children }) => (
  <QueryClientProvider client={queryClient}>
    {children}
  </QueryClientProvider>
);

describe('DocumentList', () => {
  afterEach(() => {
    jest.clearAllMocks();
    queryClient.clear();
  });

  it('should display a loading spinner while fetching documents', () => {
    mockedAxios.get.mockImplementation(() => new Promise(() => {})); // Promessa que nunca resolve
    render(<DocumentList />, { wrapper });
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('should display an error message if fetching documents fails', async () => {
    mockedAxios.get.mockRejectedValue(new Error('Network Error'));
    render(<DocumentList />, { wrapper });
    expect(await screen.findByText(/Erro ao buscar documentos/)).toBeInTheDocument();
  });

  it('should display a message when no documents are found', async () => {
    mockedAxios.get.mockResolvedValue({ data: { documents: [] } });
    render(<DocumentList />, { wrapper });
    expect(await screen.findByText('Nenhum documento encontrado.')).toBeInTheDocument();
  });

  it('should render a list of documents successfully', async () => {
    const mockDocuments = [
      { id: '1', filename: 'document1.pdf', status: 'completed', file_type: 'application/pdf' },
      { id: '2', filename: 'document2.txt', status: 'completed', file_type: 'text/plain' },
    ];
    mockedAxios.get.mockResolvedValue({ data: { documents: mockDocuments } });

    render(<DocumentList />, { wrapper });

    expect(await screen.findByText('document1.pdf')).toBeInTheDocument();
    expect(screen.getByText('document2.txt')).toBeInTheDocument();
  });

  it('should call the delete mutation when the delete button is clicked', async () => {
    const mockDocuments = [
      { id: '1', filename: 'document1.pdf', status: 'completed', file_type: 'application/pdf' },
    ];
    mockedAxios.get.mockResolvedValue({ data: { documents: mockDocuments } });
    mockedAxios.delete.mockResolvedValue({ data: {} }); // Mock da chamada de delete

    render(<DocumentList />, { wrapper });

    const deleteButton = await screen.findByRole('button', { name: /delete/i });
    await userEvent.click(deleteButton);

    await waitFor(() => {
      expect(mockedAxios.delete).toHaveBeenCalledWith('/documents/1');
    });
  });
});
