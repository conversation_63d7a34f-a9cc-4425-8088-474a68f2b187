#!/usr/bin/env node

/**
 * Teste para verificar se o erro de hidratação HTML foi corrigido
 * 
 * Este script verifica se não há mais elementos <div> dentro de <p>
 * no componente DocumentList.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verificando correção do erro de hidratação HTML...\n');

// Caminho para o componente corrigido
const documentListPath = path.join(__dirname, 'admin_panel', 'src', 'components', 'documents', 'DocumentList.js');

try {
    // Ler o arquivo
    const content = fs.readFileSync(documentListPath, 'utf8');
    
    console.log('✅ Arquivo DocumentList.js encontrado');
    
    // Verificações específicas
    const checks = [
        {
            name: 'Typography com component="div" no secondary',
            pattern: /secondary=\{[\s\S]*?<Typography[\s\S]*?component="div"/,
            shouldExist: true,
            description: 'Verifica se Typography usa component="div" para evitar <p> com <div>'
        },
        {
            name: 'Typography com component="span" nos filhos',
            pattern: /Typography[\s\S]*?component="span"/,
            shouldExist: true,
            description: 'Verifica se Typography filhos usam component="span"'
        },
        {
            name: 'Box dentro de secondary sem Typography wrapper',
            pattern: /secondary=\{[\s\S]*?<Box[\s\S]*?<Typography[\s\S]*?variant="caption"[\s\S]*?<\/Typography>[\s\S]*?<\/Box>[\s\S]*?\}/,
            shouldExist: false,
            description: 'Verifica se não há Box direto dentro de secondary (que seria <p>)'
        },
        {
            name: 'Typography com display flex direto',
            pattern: /secondary=\{[\s\S]*?<Typography[\s\S]*?component="div"[\s\S]*?display.*flex/,
            shouldExist: true,
            description: 'Verifica se Typography usa display flex diretamente (sem Box aninhado)'
        }
    ];
    
    let allPassed = true;
    
    checks.forEach((check, index) => {
        const found = check.pattern.test(content);
        const passed = found === check.shouldExist;
        
        console.log(`\n${index + 1}. ${check.name}`);
        console.log(`   ${check.description}`);
        
        if (passed) {
            console.log(`   ✅ PASSOU - ${check.shouldExist ? 'Encontrado' : 'Não encontrado'} como esperado`);
        } else {
            console.log(`   ❌ FALHOU - ${check.shouldExist ? 'Não encontrado' : 'Encontrado'} quando ${check.shouldExist ? 'deveria existir' : 'não deveria existir'}`);
            allPassed = false;
        }
    });
    
    // Verificação adicional: procurar por padrões problemáticos
    console.log('\n🔍 Verificações adicionais:');
    
    const problematicPatterns = [
        {
            name: 'Box direto em secondary',
            pattern: /secondary=\{[\s\S]*?<Box/,
            description: 'Box direto em secondary (problemático)'
        },
        {
            name: 'div em Typography body2 sem component',
            pattern: /<Typography[\s\S]*?variant="body2"[\s\S]*?>[^<]*<div/,
            description: 'div dentro de Typography body2 sem component especificado'
        }
    ];
    
    problematicPatterns.forEach((pattern, index) => {
        const found = pattern.pattern.test(content);
        console.log(`   ${index + 1}. ${pattern.name}: ${found ? '❌ ENCONTRADO (problemático)' : '✅ NÃO ENCONTRADO (bom)'}`);
        if (found) {
            allPassed = false;
        }
    });
    
    // Resultado final
    console.log('\n' + '='.repeat(60));
    if (allPassed) {
        console.log('🎉 TODOS OS TESTES PASSARAM!');
        console.log('✅ Erro de hidratação HTML foi corrigido com sucesso');
        console.log('✅ Estrutura HTML válida implementada');
        console.log('✅ Componente DocumentList.js está correto');
    } else {
        console.log('❌ ALGUNS TESTES FALHARAM');
        console.log('⚠️  Ainda podem existir problemas de hidratação');
        console.log('💡 Verifique as correções necessárias acima');
    }
    console.log('='.repeat(60));
    
    // Mostrar trecho relevante do código corrigido
    console.log('\n📝 Trecho do código corrigido:');
    const secondaryMatch = content.match(/secondary=\{[\s\S]*?\}/);
    if (secondaryMatch) {
        console.log('```javascript');
        console.log(secondaryMatch[0].substring(0, 500) + (secondaryMatch[0].length > 500 ? '...' : ''));
        console.log('```');
    }
    
} catch (error) {
    console.error('❌ Erro ao verificar o arquivo:', error.message);
    process.exit(1);
}

console.log('\n✨ Verificação concluída!');
