[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme.js": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\LoginPage.js": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ChatPage.js": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DocumentManagementPage.js": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DashboardPage.js": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\WhatsAppPage.js": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\store\\authStore.js": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\DashboardLayout.js": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Sidebar.js": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Header.js": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\QRCodeDisplay.js": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\api\\axiosInstance.js": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\ConversationList.js": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\SyncingLoader.js": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentList.js": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentUpload.js": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\HistoryPage.js": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationDetailPage.js": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\InboxPage.js": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationReplyPage.js": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\NotificationCenter.js": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\hooks\\useWebSocket.js": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\utils\\websocket.js": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\ConversationDetail.js": "26"}, {"size": 782, "mtime": 1752037874331, "results": "27", "hashOfConfig": "28"}, {"size": 2165, "mtime": 1752048796659, "results": "29", "hashOfConfig": "28"}, {"size": 1029, "mtime": 1752036494128, "results": "30", "hashOfConfig": "28"}, {"size": 2865, "mtime": 1752036890857, "results": "31", "hashOfConfig": "28"}, {"size": 5817, "mtime": 1752060977747, "results": "32", "hashOfConfig": "28"}, {"size": 807, "mtime": 1752037913732, "results": "33", "hashOfConfig": "28"}, {"size": 8652, "mtime": 1752049238549, "results": "34", "hashOfConfig": "28"}, {"size": 2040, "mtime": 1752247944863, "results": "35", "hashOfConfig": "28"}, {"size": 680, "mtime": 1752036857663, "results": "36", "hashOfConfig": "28"}, {"size": 477, "mtime": 1752037757445, "results": "37", "hashOfConfig": "28"}, {"size": 1669, "mtime": 1752048728990, "results": "38", "hashOfConfig": "28"}, {"size": 985, "mtime": 1752244890136, "results": "39", "hashOfConfig": "28"}, {"size": 3454, "mtime": 1752241240911, "results": "40", "hashOfConfig": "28"}, {"size": 1052, "mtime": 1752068909725, "results": "41", "hashOfConfig": "28"}, {"size": 2612, "mtime": 1752039059481, "results": "42", "hashOfConfig": "28"}, {"size": 654, "mtime": 1752038206649, "results": "43", "hashOfConfig": "28"}, {"size": 1856, "mtime": 1752037899869, "results": "44", "hashOfConfig": "28"}, {"size": 2255, "mtime": 1752037889905, "results": "45", "hashOfConfig": "28"}, {"size": 1701, "mtime": 1752049590923, "results": "46", "hashOfConfig": "28"}, {"size": 2204, "mtime": 1752042010144, "results": "47", "hashOfConfig": "28"}, {"size": 2760, "mtime": 1752048743924, "results": "48", "hashOfConfig": "28"}, {"size": 4759, "mtime": 1752049003962, "results": "49", "hashOfConfig": "28"}, {"size": 5156, "mtime": 1752244842569, "results": "50", "hashOfConfig": "28"}, {"size": 2407, "mtime": 1752244804074, "results": "51", "hashOfConfig": "28"}, {"size": 3546, "mtime": 1752244783251, "results": "52", "hashOfConfig": "28"}, {"size": 7695, "mtime": 1752247860653, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1laiz3m", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ChatPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DocumentManagementPage.js", ["132"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DashboardPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\WhatsAppPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\store\\authStore.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\DashboardLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Sidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\QRCodeDisplay.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\api\\axiosInstance.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\ConversationList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\SyncingLoader.js", ["133"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentUpload.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\HistoryPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationDetailPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\InboxPage.js", ["134", "135"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationReplyPage.js", ["136"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\NotificationCenter.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\hooks\\useWebSocket.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\utils\\websocket.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\ConversationDetail.js", ["137", "138"], [], {"ruleId": "139", "severity": 1, "message": "140", "line": 2, "column": 34, "nodeType": "141", "messageId": "142", "endLine": 2, "endColumn": 41}, {"ruleId": "139", "severity": 1, "message": "143", "line": 2, "column": 10, "nodeType": "141", "messageId": "142", "endLine": 2, "endColumn": 13}, {"ruleId": "139", "severity": 1, "message": "143", "line": 6, "column": 59, "nodeType": "141", "messageId": "142", "endLine": 6, "endColumn": 62}, {"ruleId": "139", "severity": 1, "message": "144", "line": 21, "column": 59, "nodeType": "141", "messageId": "142", "endLine": 21, "endColumn": 66}, {"ruleId": "139", "severity": 1, "message": "145", "line": 28, "column": 9, "nodeType": "141", "messageId": "142", "endLine": 28, "endColumn": 17}, {"ruleId": "139", "severity": 1, "message": "146", "line": 18, "column": 3, "nodeType": "141", "messageId": "142", "endLine": 18, "endColumn": 15}, {"ruleId": "147", "severity": 1, "message": "148", "line": 49, "column": 6, "nodeType": "149", "endLine": 49, "endColumn": 26, "suggestions": "150"}, "no-unused-vars", "'Divider' is defined but never used.", "Identifier", "unusedVar", "'Box' is defined but never used.", "'refetch' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'ListItemText' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", "ArrayExpression", ["151"], {"desc": "152", "fix": "153"}, "Update the dependencies array to be: [open, conversation, fetchMessages]", {"range": "154", "text": "155"}, [1119, 1139], "[open, conversation, fetchMessages]"]