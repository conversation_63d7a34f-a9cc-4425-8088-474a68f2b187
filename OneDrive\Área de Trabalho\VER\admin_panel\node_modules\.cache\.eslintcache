[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme.js": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\LoginPage.js": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ChatPage.js": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DocumentManagementPage.js": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DashboardPage.js": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\WhatsAppPage.js": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\store\\authStore.js": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\DashboardLayout.js": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Sidebar.js": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Header.js": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\QRCodeDisplay.js": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\api\\axiosInstance.js": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\ConversationList.js": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\SyncingLoader.js": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentList.js": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentUpload.js": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\InboxPage.js": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationReplyPage.js": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\NotificationCenter.js": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\hooks\\useWebSocket.js": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\utils\\websocket.js": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\ConversationDetail.js": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\DocumentViewer.js": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme\\theme.js": "26", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\MessageList.js": "27"}, {"size": 782, "mtime": 1752037874331, "results": "28", "hashOfConfig": "29"}, {"size": 2017, "mtime": 1752339822007, "results": "30", "hashOfConfig": "29"}, {"size": 1029, "mtime": 1752036494128, "results": "31", "hashOfConfig": "29"}, {"size": 6361, "mtime": 1752249854800, "results": "32", "hashOfConfig": "29"}, {"size": 5817, "mtime": 1752060977747, "results": "33", "hashOfConfig": "29"}, {"size": 1504, "mtime": 1752249416596, "results": "34", "hashOfConfig": "29"}, {"size": 10941, "mtime": 1752344657413, "results": "35", "hashOfConfig": "29"}, {"size": 2040, "mtime": 1752247944863, "results": "36", "hashOfConfig": "29"}, {"size": 680, "mtime": 1752036857663, "results": "37", "hashOfConfig": "29"}, {"size": 686, "mtime": 1752249663587, "results": "38", "hashOfConfig": "29"}, {"size": 3571, "mtime": 1752344265536, "results": "39", "hashOfConfig": "29"}, {"size": 1933, "mtime": 1752249705580, "results": "40", "hashOfConfig": "29"}, {"size": 3454, "mtime": 1752241240911, "results": "41", "hashOfConfig": "29"}, {"size": 1052, "mtime": 1752068909725, "results": "42", "hashOfConfig": "29"}, {"size": 7196, "mtime": 1752441515418, "results": "43", "hashOfConfig": "29"}, {"size": 654, "mtime": 1752038206649, "results": "44", "hashOfConfig": "29"}, {"size": 6286, "mtime": 1752333091745, "results": "45", "hashOfConfig": "29"}, {"size": 2255, "mtime": 1752037889905, "results": "46", "hashOfConfig": "29"}, {"size": 2760, "mtime": 1752048743924, "results": "47", "hashOfConfig": "29"}, {"size": 5786, "mtime": 1752429250053, "results": "48", "hashOfConfig": "29"}, {"size": 5153, "mtime": 1752260385443, "results": "49", "hashOfConfig": "29"}, {"size": 2407, "mtime": 1752244804074, "results": "50", "hashOfConfig": "29"}, {"size": 3546, "mtime": 1752244783251, "results": "51", "hashOfConfig": "29"}, {"size": 3770, "mtime": 1752337609402, "results": "52", "hashOfConfig": "29"}, {"size": 8659, "mtime": 1752339987407, "results": "53", "hashOfConfig": "29"}, {"size": 6748, "mtime": 1752249591198, "results": "54", "hashOfConfig": "29"}, {"size": 12113, "mtime": 1752338870929, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1laiz3m", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ChatPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DocumentManagementPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DashboardPage.js", ["137"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\WhatsAppPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\store\\authStore.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\DashboardLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Sidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Header.js", ["138"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\QRCodeDisplay.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\api\\axiosInstance.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\ConversationList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\SyncingLoader.js", ["139"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentUpload.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\InboxPage.js", ["140", "141"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationReplyPage.js", ["142", "143", "144", "145", "146"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\NotificationCenter.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\hooks\\useWebSocket.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\utils\\websocket.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\ConversationDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\DocumentViewer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme\\theme.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\MessageList.js", ["147"], [], {"ruleId": "148", "severity": 1, "message": "149", "line": 27, "column": 9, "nodeType": "150", "messageId": "151", "endLine": 27, "endColumn": 14}, {"ruleId": "148", "severity": 1, "message": "152", "line": 2, "column": 60, "nodeType": "150", "messageId": "151", "endLine": 2, "endColumn": 70}, {"ruleId": "148", "severity": 1, "message": "153", "line": 2, "column": 10, "nodeType": "150", "messageId": "151", "endLine": 2, "endColumn": 13}, {"ruleId": "148", "severity": 1, "message": "153", "line": 6, "column": 59, "nodeType": "150", "messageId": "151", "endLine": 6, "endColumn": 62}, {"ruleId": "148", "severity": 1, "message": "154", "line": 21, "column": 59, "nodeType": "150", "messageId": "151", "endLine": 21, "endColumn": 66}, {"ruleId": "148", "severity": 1, "message": "155", "line": 20, "column": 7, "nodeType": "150", "messageId": "151", "endLine": 20, "endColumn": 16}, {"ruleId": "148", "severity": 1, "message": "156", "line": 28, "column": 9, "nodeType": "150", "messageId": "151", "endLine": 28, "endColumn": 17}, {"ruleId": "157", "severity": 2, "message": "158", "line": 49, "column": 23, "nodeType": "150", "messageId": "159", "endLine": 49, "endColumn": 35}, {"ruleId": "157", "severity": 2, "message": "158", "line": 50, "column": 25, "nodeType": "150", "messageId": "159", "endLine": 50, "endColumn": 37}, {"ruleId": "157", "severity": 2, "message": "158", "line": 75, "column": 50, "nodeType": "150", "messageId": "159", "endLine": 75, "endColumn": 62}, {"ruleId": "148", "severity": 1, "message": "160", "line": 18, "column": 18, "nodeType": "150", "messageId": "151", "endLine": 18, "endColumn": 26}, "no-unused-vars", "'theme' is assigned a value but never used.", "Identifier", "unusedVar", "'IconButton' is defined but never used.", "'Box' is defined but never used.", "'refetch' is assigned a value but never used.", "'postReply' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "no-undef", "'conversation' is not defined.", "undef", "'SmartToy' is defined but never used."]