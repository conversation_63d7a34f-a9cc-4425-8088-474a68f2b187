[{"C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme.js": "3", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\LoginPage.js": "4", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ChatPage.js": "5", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DocumentManagementPage.js": "6", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DashboardPage.js": "7", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\WhatsAppPage.js": "8", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\store\\authStore.js": "9", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\DashboardLayout.js": "10", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Sidebar.js": "11", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Header.js": "12", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\QRCodeDisplay.js": "13", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\api\\axiosInstance.js": "14", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\ConversationList.js": "15", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\SyncingLoader.js": "16", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentList.js": "17", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentUpload.js": "18", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\HistoryPage.js": "19", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationDetailPage.js": "20", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\InboxPage.js": "21", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationReplyPage.js": "22", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\NotificationCenter.js": "23", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\hooks\\useWebSocket.js": "24", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\utils\\websocket.js": "25", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\ConversationDetail.js": "26", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\DocumentViewer.js": "27", "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme\\theme.js": "28"}, {"size": 782, "mtime": 1752037874331, "results": "29", "hashOfConfig": "30"}, {"size": 2345, "mtime": 1752249640861, "results": "31", "hashOfConfig": "30"}, {"size": 1029, "mtime": 1752036494128, "results": "32", "hashOfConfig": "30"}, {"size": 6361, "mtime": 1752249854800, "results": "33", "hashOfConfig": "30"}, {"size": 5817, "mtime": 1752060977747, "results": "34", "hashOfConfig": "30"}, {"size": 1504, "mtime": 1752249416596, "results": "35", "hashOfConfig": "30"}, {"size": 6094, "mtime": 1752248161529, "results": "36", "hashOfConfig": "30"}, {"size": 2040, "mtime": 1752247944863, "results": "37", "hashOfConfig": "30"}, {"size": 680, "mtime": 1752036857663, "results": "38", "hashOfConfig": "30"}, {"size": 686, "mtime": 1752249663587, "results": "39", "hashOfConfig": "30"}, {"size": 3692, "mtime": 1752249755171, "results": "40", "hashOfConfig": "30"}, {"size": 1933, "mtime": 1752249705580, "results": "41", "hashOfConfig": "30"}, {"size": 3454, "mtime": 1752241240911, "results": "42", "hashOfConfig": "30"}, {"size": 1052, "mtime": 1752068909725, "results": "43", "hashOfConfig": "30"}, {"size": 3068, "mtime": 1752332373609, "results": "44", "hashOfConfig": "30"}, {"size": 654, "mtime": 1752038206649, "results": "45", "hashOfConfig": "30"}, {"size": 5897, "mtime": 1752249482846, "results": "46", "hashOfConfig": "30"}, {"size": 2255, "mtime": 1752037889905, "results": "47", "hashOfConfig": "30"}, {"size": 1701, "mtime": 1752049590923, "results": "48", "hashOfConfig": "30"}, {"size": 2204, "mtime": 1752042010144, "results": "49", "hashOfConfig": "30"}, {"size": 2760, "mtime": 1752048743924, "results": "50", "hashOfConfig": "30"}, {"size": 4759, "mtime": 1752049003962, "results": "51", "hashOfConfig": "30"}, {"size": 5153, "mtime": 1752260385443, "results": "52", "hashOfConfig": "30"}, {"size": 2407, "mtime": 1752244804074, "results": "53", "hashOfConfig": "30"}, {"size": 3546, "mtime": 1752244783251, "results": "54", "hashOfConfig": "30"}, {"size": 7695, "mtime": 1752247860653, "results": "55", "hashOfConfig": "30"}, {"size": 8403, "mtime": 1752249377897, "results": "56", "hashOfConfig": "30"}, {"size": 6748, "mtime": 1752249591198, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1laiz3m", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\LoginPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ChatPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DocumentManagementPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\DashboardPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\WhatsAppPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\store\\authStore.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\DashboardLayout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Sidebar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\layout\\Header.js", ["142"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\QRCodeDisplay.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\api\\axiosInstance.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\ConversationList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\whatsapp\\SyncingLoader.js", ["143"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentList.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\documents\\DocumentUpload.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\HistoryPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationDetailPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\InboxPage.js", ["144", "145"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\pages\\ConversationReplyPage.js", ["146"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\NotificationCenter.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\hooks\\useWebSocket.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\utils\\websocket.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\ConversationDetail.js", ["147", "148"], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\components\\DocumentViewer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Área de Trabalho\\VER\\admin_panel\\src\\theme\\theme.js", [], [], {"ruleId": "149", "severity": 1, "message": "150", "line": 2, "column": 60, "nodeType": "151", "messageId": "152", "endLine": 2, "endColumn": 70}, {"ruleId": "149", "severity": 1, "message": "153", "line": 2, "column": 10, "nodeType": "151", "messageId": "152", "endLine": 2, "endColumn": 13}, {"ruleId": "149", "severity": 1, "message": "153", "line": 6, "column": 59, "nodeType": "151", "messageId": "152", "endLine": 6, "endColumn": 62}, {"ruleId": "149", "severity": 1, "message": "154", "line": 21, "column": 59, "nodeType": "151", "messageId": "152", "endLine": 21, "endColumn": 66}, {"ruleId": "149", "severity": 1, "message": "155", "line": 28, "column": 9, "nodeType": "151", "messageId": "152", "endLine": 28, "endColumn": 17}, {"ruleId": "149", "severity": 1, "message": "156", "line": 18, "column": 3, "nodeType": "151", "messageId": "152", "endLine": 18, "endColumn": 15}, {"ruleId": "157", "severity": 1, "message": "158", "line": 49, "column": 6, "nodeType": "159", "endLine": 49, "endColumn": 26, "suggestions": "160"}, "no-unused-vars", "'IconButton' is defined but never used.", "Identifier", "unusedVar", "'Box' is defined but never used.", "'refetch' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'ListItemText' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", "ArrayExpression", ["161"], {"desc": "162", "fix": "163"}, "Update the dependencies array to be: [open, conversation, fetchMessages]", {"range": "164", "text": "165"}, [1119, 1139], "[open, conversation, fetchMessages]"]