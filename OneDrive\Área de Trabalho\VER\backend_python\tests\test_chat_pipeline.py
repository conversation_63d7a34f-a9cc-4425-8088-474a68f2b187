"""
Testes unitários para o pipeline de chat
"""

import unittest
import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pipelines.chat_pipeline import Chat<PERSON><PERSON>eline, route_intent, generate_general_response
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage


class TestChatPipeline(unittest.TestCase):
    """
    Testes para o pipeline de chat
    """
    
    def setUp(self):
        """Configurar testes"""
        with patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI'):
            self.pipeline = ChatPipeline()
    
    def test_format_conversation_history(self):
        """Testa a formatação do histórico de conversa"""
        messages = [
            {'sender': 'user', 'content': 'Olá!'},
            {'sender': 'ai', 'content': 'Oi! Como posso ajudar?'},
            {'sender': 'human', 'content': 'Preciso de ajuda'},
            {'sender': 'assistant', 'content': 'Claro! Vou te ajudar.'}
        ]
        
        formatted = self.pipeline.format_conversation_history(messages)
        
        self.assertEqual(len(formatted), 4)
        self.assertIsInstance(formatted[0], HumanMessage)
        self.assertIsInstance(formatted[1], AIMessage)
        self.assertIsInstance(formatted[2], HumanMessage)
        self.assertIsInstance(formatted[3], AIMessage)
        
        self.assertEqual(formatted[0].content, 'Olá!')
        self.assertEqual(formatted[1].content, 'Oi! Como posso ajudar?')
    
    def test_format_conversation_history_empty(self):
        """Testa formatação com histórico vazio"""
        formatted = self.pipeline.format_conversation_history([])
        self.assertEqual(len(formatted), 0)
    
    @patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI')
    def test_route_intent_success(self, mock_chat_model):
        """Testa o roteamento de intenção com sucesso"""
        # Mock do modelo
        mock_response = Mock()
        mock_response.content = "search_documents_rag"
        
        mock_model_instance = Mock()
        mock_model_instance.ainvoke = AsyncMock(return_value=mock_response)
        mock_chat_model.return_value = mock_model_instance
        
        # Criar pipeline com mock
        pipeline = ChatPipeline()
        pipeline.intent_router_model = mock_model_instance
        
        async def run_test():
            intent = await pipeline.route_intent("Pergunta sobre documentos", [])
            self.assertEqual(intent, "search_documents_rag")
        
        asyncio.run(run_test())
    
    @patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI')
    def test_route_intent_with_history(self, mock_chat_model):
        """Testa roteamento com histórico"""
        # Mock do modelo
        mock_response = Mock()
        mock_response.content = "general_conversation"
        
        mock_model_instance = Mock()
        mock_model_instance.ainvoke = AsyncMock(return_value=mock_response)
        mock_chat_model.return_value = mock_model_instance
        
        # Criar pipeline com mock
        pipeline = ChatPipeline()
        pipeline.intent_router_model = mock_model_instance
        
        history = [
            {'sender': 'user', 'content': 'Oi'},
            {'sender': 'ai', 'content': 'Olá! Como posso ajudar?'}
        ]
        
        async def run_test():
            intent = await pipeline.route_intent("Como vai?", history)
            self.assertEqual(intent, "general_conversation")
            
            # Verificar se o modelo foi chamado
            mock_model_instance.ainvoke.assert_called_once()
        
        asyncio.run(run_test())
    
    @patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI')
    def test_route_intent_error_fallback(self, mock_chat_model):
        """Testa fallback em caso de erro no roteamento"""
        # Mock que gera erro
        mock_model_instance = Mock()
        mock_model_instance.ainvoke = AsyncMock(side_effect=Exception("Erro de API"))
        mock_chat_model.return_value = mock_model_instance
        
        # Criar pipeline com mock
        pipeline = ChatPipeline()
        pipeline.intent_router_model = mock_model_instance
        
        async def run_test():
            intent = await pipeline.route_intent("Pergunta qualquer", [])
            self.assertEqual(intent, "search_documents_rag")  # Fallback padrão
        
        asyncio.run(run_test())
    
    @patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI')
    def test_generate_rag_response(self, mock_chat_model):
        """Testa geração de resposta RAG"""
        # Mock do modelo
        mock_response = Mock()
        mock_response.content = "Resposta baseada em documentos"
        
        mock_model_instance = Mock()
        mock_model_instance.ainvoke = AsyncMock(return_value=mock_response)
        mock_chat_model.return_value = mock_model_instance
        
        # Criar pipeline com mock
        pipeline = ChatPipeline()
        pipeline.chat_model = mock_model_instance
        
        async def run_test():
            response = await pipeline.generate_rag_response(
                "Pergunta sobre documentos",
                "Contexto dos documentos",
                []
            )
            self.assertEqual(response, "Resposta baseada em documentos")
        
        asyncio.run(run_test())
    
    @patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI')
    def test_generate_general_response(self, mock_chat_model):
        """Testa geração de resposta geral"""
        # Mock do modelo
        mock_response = Mock()
        mock_response.content = "Resposta de conversa geral"
        
        mock_model_instance = Mock()
        mock_model_instance.ainvoke = AsyncMock(return_value=mock_response)
        mock_chat_model.return_value = mock_model_instance
        
        # Criar pipeline com mock
        pipeline = ChatPipeline()
        pipeline.chat_model = mock_model_instance
        
        async def run_test():
            response = await pipeline.generate_general_response("Como vai?", [])
            self.assertEqual(response, "Resposta de conversa geral")
        
        asyncio.run(run_test())
    
    @patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI')
    def test_generate_contextual_response(self, mock_chat_model):
        """Testa geração de resposta contextual"""
        # Mock do modelo
        mock_response = Mock()
        mock_response.content = "Resposta contextual"
        
        mock_model_instance = Mock()
        mock_model_instance.ainvoke = AsyncMock(return_value=mock_response)
        mock_chat_model.return_value = mock_model_instance
        
        # Criar pipeline com mock
        pipeline = ChatPipeline()
        pipeline.chat_model = mock_model_instance
        
        async def run_test():
            response = await pipeline.generate_contextual_response(
                "Pergunta",
                context="Contexto adicional",
                history_messages=[],
                system_prompt="Prompt customizado"
            )
            self.assertEqual(response, "Resposta contextual")
        
        asyncio.run(run_test())
    
    def test_create_conversation_summary(self):
        """Testa criação de resumo da conversa"""
        messages = [
            {'sender': 'user', 'content': 'Primeira mensagem do usuário'},
            {'sender': 'ai', 'content': 'Primeira resposta da IA'},
            {'sender': 'user', 'content': 'Segunda mensagem do usuário'},
            {'sender': 'ai', 'content': 'Segunda resposta da IA'}
        ]
        
        summary = self.pipeline.create_conversation_summary(messages)
        
        self.assertIsInstance(summary, str)
        self.assertIn("Usuário:", summary)
        self.assertIn("Rafaela:", summary)
        self.assertIn("Primeira mensagem", summary)
    
    def test_create_conversation_summary_empty(self):
        """Testa resumo com conversa vazia"""
        summary = self.pipeline.create_conversation_summary([])
        self.assertEqual(summary, "")
    
    def test_create_conversation_summary_max_length(self):
        """Testa limitação de comprimento do resumo"""
        # Criar muitas mensagens longas
        messages = []
        for i in range(20):
            messages.append({
                'sender': 'user',
                'content': f'Esta é uma mensagem muito longa número {i} ' * 10
            })
            messages.append({
                'sender': 'ai', 
                'content': f'Esta é uma resposta muito longa número {i} ' * 10
            })
        
        summary = self.pipeline.create_conversation_summary(messages, max_length=200)
        self.assertLessEqual(len(summary), 200)
    
    @patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI')
    def test_message_structure_in_calls(self, mock_chat_model):
        """Testa se as mensagens são estruturadas corretamente nas chamadas"""
        # Mock do modelo
        mock_response = Mock()
        mock_response.content = "Resposta teste"
        
        mock_model_instance = Mock()
        mock_model_instance.ainvoke = AsyncMock(return_value=mock_response)
        mock_chat_model.return_value = mock_model_instance
        
        # Criar pipeline com mock
        pipeline = ChatPipeline()
        pipeline.chat_model = mock_model_instance
        
        history = [
            {'sender': 'user', 'content': 'Oi'},
            {'sender': 'ai', 'content': 'Olá!'}
        ]
        
        async def run_test():
            await pipeline.generate_general_response("Como vai?", history)
            
            # Verificar se foi chamado
            mock_model_instance.ainvoke.assert_called_once()
            
            # Verificar estrutura das mensagens
            call_args = mock_model_instance.ainvoke.call_args[0][0]
            self.assertIsInstance(call_args, list)
            self.assertIsInstance(call_args[0], SystemMessage)
            self.assertIsInstance(call_args[-1], HumanMessage)
            self.assertEqual(call_args[-1].content, "Como vai?")
        
        asyncio.run(run_test())


if __name__ == "__main__":
    unittest.main()
