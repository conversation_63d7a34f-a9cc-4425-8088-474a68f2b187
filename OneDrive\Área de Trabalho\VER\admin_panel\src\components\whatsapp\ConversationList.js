import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { List, ListItem, ListItemText, ListItemAvatar, Avatar, Typography, CircularProgress, Alert, Badge, Box, Divider } from '@mui/material';
import axiosInstance from '../../api/axiosInstance';

// Função para formatar data e hora
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';

  const date = new Date(timestamp * 1000);
  const now = new Date();
  const diffInHours = (now - date) / (1000 * 60 * 60);
  const diffInDays = Math.floor(diffInHours / 24);

  // Se foi hoje, mostrar apenas a hora
  if (diffInHours < 24 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Se foi ontem
  if (diffInDays === 1) {
    return `Ontem ${date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    })}`;
  }

  // Se foi esta semana (últimos 7 dias)
  if (diffInDays < 7) {
    const weekdays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
    return `${weekdays[date.getDay()]} ${date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    })}`;
  }

  // Para datas mais antigas, mostrar data completa
  return date.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: '2-digit'
  }) + ' ' + date.toLocaleTimeString('pt-BR', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

const fetchConversations = async () => {
  try {
    const { data } = await axiosInstance.get('/whatsapp/conversations');
    // Filtra para garantir que temos conversas válidas
    return data.filter(chat => chat.id && (chat.id._serialized || chat.id));
  } catch (error) {
    console.warn('WhatsApp backend não disponível, usando dados simulados');
    // Dados simulados para demonstração
    return [
      {
        id: { _serialized: '<EMAIL>', user: '558488501582' },
        name: 'Italo Cabral',
        lastMessage: {
          body: 'Bom dia! Preciso de ajuda com cesta básica',
          timestamp: Date.now() / 1000 - 3600 // 1 hora atrás
        },
        timestamp: Date.now() / 1000 - 3600,
        unreadCount: 2,
        contact: {
          formattedName: 'Italo Cabral',
          profilePicThumbObj: { eurl: '' }
        }
      },
      {
        id: { _serialized: '<EMAIL>', user: '5511987654321' },
        name: 'Maria Silva',
        lastMessage: {
          body: 'Obrigada pela ajuda! Consegui resolver o problema.',
          timestamp: Date.now() / 1000 - 7200 // 2 horas atrás
        },
        timestamp: Date.now() / 1000 - 7200,
        unreadCount: 0,
        contact: {
          formattedName: 'Maria Silva',
          profilePicThumbObj: { eurl: '' }
        }
      },
      {
        id: { _serialized: '<EMAIL>', user: '5511123456789' },
        name: 'João Santos',
        lastMessage: {
          body: 'Oi! Gostaria de saber sobre os programas sociais disponíveis.',
          timestamp: Date.now() / 1000 - 86400 // 1 dia atrás
        },
        timestamp: Date.now() / 1000 - 86400,
        unreadCount: 1,
        contact: {
          formattedName: 'João Santos',
          profilePicThumbObj: { eurl: '' }
        }
      },
      {
        id: { _serialized: '<EMAIL>', user: '5511555666777' },
        name: 'Ana Costa',
        lastMessage: {
          body: 'Bom dia! Preciso agendar uma consulta na UBS.',
          timestamp: Date.now() / 1000 - 172800 // 2 dias atrás
        },
        timestamp: Date.now() / 1000 - 172800,
        unreadCount: 0,
        contact: {
          formattedName: 'Ana Costa',
          profilePicThumbObj: { eurl: '' }
        }
      }
    ];
  }
};

const ConversationList = ({ onConversationClick }) => {
  const { data: conversations, isLoading, isError, error } = useQuery({
    queryKey: ['whatsapp-conversations'],
    queryFn: fetchConversations,
  });

  if (isLoading) {
    return <CircularProgress />;
  }

  if (isError) {
    return <Alert severity="error">Erro ao buscar conversas: {error.message}</Alert>;
  }

  return (
    <List sx={{ width: '100%', bgcolor: 'background.paper', p: 0 }}>
      {conversations.length === 0 ? (
        <Typography sx={{ p: 2 }}>Nenhuma conversa encontrada.</Typography>
      ) : (
        conversations.map((chat, index) => (
          <React.Fragment key={chat.id._serialized}>
            <ListItem
              alignItems="flex-start"
              sx={{
                '&:hover': { backgroundColor: 'action.hover' },
                cursor: 'pointer',
                backgroundColor: chat.unreadCount > 0 ? 'rgba(25, 118, 210, 0.04)' : 'transparent',
                borderLeft: chat.unreadCount > 0 ? '3px solid #1976d2' : '3px solid transparent'
              }}
              onClick={() => onConversationClick && onConversationClick({
                id: chat.id._serialized || chat.id,
                contact_id: chat.id._serialized || chat.id,
                contact_name: chat.name || chat.contact?.formattedName || chat.id.user || 'Contato sem nome',
                unreadCount: chat.unreadCount || 0,
                lastMessage: chat.lastMessage?.body || '',
                timestamp: chat.timestamp || chat.t || chat.lastMessage?.timestamp
              })}
            >
              <ListItemAvatar>
                <Avatar alt={chat.name} src={chat.contact?.profilePicThumbObj?.eurl || ''} />
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography noWrap>
                    {chat.name || chat.contact?.formattedName || chat.id.user || 'Contato sem nome'}
                  </Typography>
                }
                secondary={
                  <Typography
                    noWrap
                    color="text.secondary"
                    variant="body2"
                    sx={{
                      fontStyle: chat.lastMessage?.body ? 'normal' : 'italic',
                      opacity: chat.lastMessage?.body ? 1 : 0.7
                    }}
                  >
                    {chat.lastMessage?.body || 'Nenhuma mensagem ainda.'}
                  </Typography>
                }
              />
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', ml: 2 }}>
                {(chat.timestamp || chat.t || chat.lastMessage?.timestamp) && (
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                    <Typography variant="caption" color="text.secondary">
                      {formatDateTime(chat.timestamp || chat.t || chat.lastMessage?.timestamp)}
                    </Typography>
                  </Box>
                )}
                {chat.unreadCount > 0 && (
                  <Badge badgeContent={chat.unreadCount} color="primary" sx={{ mt: 1 }} />
                )}
              </Box>
            </ListItem>
            {index < conversations.length - 1 && <Divider variant="inset" component="li" />}
          </React.Fragment>
        ))
      )}
    </List>
  );
};

export default ConversationList;
