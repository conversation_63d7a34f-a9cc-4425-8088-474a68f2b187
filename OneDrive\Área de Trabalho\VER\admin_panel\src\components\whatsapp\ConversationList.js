import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { List, ListItem, ListItemText, ListItemAvatar, Avatar, Typography, CircularProgress, Alert, Badge, Box, Divider } from '@mui/material';
import axiosInstance from '../../api/axiosInstance';

const fetchConversations = async () => {
  const { data } = await axiosInstance.get('/whatsapp/conversations');
  // Filtra para garantir que temos apenas conversas com nome e id
  return data.filter(chat => chat.id && chat.name);
};

const ConversationList = () => {
  const { data: conversations, isLoading, isError, error } = useQuery({
    queryKey: ['whatsapp-conversations'],
    queryFn: fetchConversations,
  });

  if (isLoading) {
    return <CircularProgress />;
  }

  if (isError) {
    return <Alert severity="error">Erro ao buscar conversas: {error.message}</Alert>;
  }

  return (
    <List sx={{ width: '100%', bgcolor: 'background.paper', p: 0 }}>
      {conversations.length === 0 ? (
        <Typography sx={{ p: 2 }}>Nenhuma conversa encontrada.</Typography>
      ) : (
        conversations.map((chat, index) => (
          <React.Fragment key={chat.id._serialized}>
            <ListItem alignItems="flex-start" sx={{ '&:hover': { backgroundColor: 'action.hover' }, cursor: 'pointer' }}>
              <ListItemAvatar>
                <Avatar alt={chat.name} src={chat.contact?.profilePicThumbObj?.eurl || ''} />
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography noWrap>
                    {chat.name || chat.id.user}
                  </Typography>
                }
                secondary={
                  <Typography noWrap color="text.secondary" variant="body2">
                    {chat.lastMessage?.body || 'Nenhuma mensagem ainda.'}
                  </Typography>
                }
              />
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', ml: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  {chat.timestamp ? new Date(chat.timestamp * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''}
                </Typography>
                {chat.unreadCount > 0 && (
                  <Badge badgeContent={chat.unreadCount} color="primary" sx={{ mt: 1 }} />
                )}
              </Box>
            </ListItem>
            {index < conversations.length - 1 && <Divider variant="inset" component="li" />}
          </React.Fragment>
        ))
      )}
    </List>
  );
};

export default ConversationList;
