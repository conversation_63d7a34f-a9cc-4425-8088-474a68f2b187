import React, { useState } from 'react';
import { Typography, Paper, Box } from '@mui/material';
import DocumentUpload from '../components/documents/DocumentUpload';
import DocumentList from '../components/documents/DocumentList';
import DocumentViewer from '../components/DocumentViewer';

const DocumentManagementPage = () => {
  const [selectedDocument, setSelectedDocument] = useState(null);

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 4, fontWeight: 600, color: 'text.primary' }}>
        Gerenciamento de Documentos
      </Typography>

      <Paper sx={{
        p: 3,
        mb: 4,
        borderRadius: 3,
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: '1px solid',
        borderColor: 'divider'
      }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Enviar Novo Documento
        </Typography>
        <DocumentUpload />
      </Paper>

      <Paper sx={{
        p: 3,
        borderRadius: 3,
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: '1px solid',
        borderColor: 'divider'
      }}>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          Documentos Enviados
        </Typography>
        <DocumentList onDocumentClick={setSelectedDocument} />
      </Paper>

      <DocumentViewer
        open={!!selectedDocument}
        onClose={() => setSelectedDocument(null)}
        document={selectedDocument}
      />
    </Box>
  );
};

export default DocumentManagementPage;

