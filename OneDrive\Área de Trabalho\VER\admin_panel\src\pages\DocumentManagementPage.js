import React from 'react';
import { Typography, Paper, Box, Divider } from '@mui/material';
import DocumentUpload from '../components/documents/DocumentUpload';
import DocumentList from '../components/documents/DocumentList';

const DocumentManagementPage = () => {
  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 4 }}>
        Gerenciamento de Documentos
      </Typography>
      
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Enviar Novo Documento
        </Typography>
        <DocumentUpload />
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Documentos Enviados
        </Typography>
        <DocumentList />
      </Paper>
    </Box>
  );
};

export default DocumentManagementPage;

