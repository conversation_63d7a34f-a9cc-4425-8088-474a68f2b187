#!/usr/bin/env python3
"""
Teste final completo do sistema Rafaela.
"""
import requests
import asyncio
import websockets
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

def print_header(title):
    """Imprime cabeçalho formatado."""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def print_success(message):
    """Imprime mensagem de sucesso."""
    print(f"✅ {message}")

def print_error(message):
    """Imprime mensagem de erro."""
    print(f"❌ {message}")

def print_info(message):
    """Imprime mensagem informativa."""
    print(f"ℹ️  {message}")

def get_auth_token():
    """Obtém token de autenticação."""
    try:
        response = requests.post(
            f"{BASE_URL}/token",
            data={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            return response.json()['access_token']
        else:
            print_error(f"Erro no login: {response.text}")
            return None
    except Exception as e:
        print_error(f"Erro na autenticação: {e}")
        return None

def test_authentication():
    """Testa sistema de autenticação."""
    print_header("TESTE DE AUTENTICAÇÃO")
    
    token = get_auth_token()
    if token:
        print_success("Login realizado com sucesso")
        print_info(f"Token: {token[:50]}...")
        return token
    else:
        print_error("Falha na autenticação")
        return None

def test_api_endpoints(token):
    """Testa endpoints principais da API."""
    print_header("TESTE DE ENDPOINTS DA API")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Teste 1: Status do WhatsApp
    try:
        response = requests.get(f"{BASE_URL}/whatsapp/status", headers=headers)
        if response.status_code == 200:
            status = response.json()
            print_success(f"WhatsApp Status: {status.get('status')}")
        else:
            print_error(f"Erro no status WhatsApp: {response.status_code}")
    except Exception as e:
        print_error(f"Erro ao testar status WhatsApp: {e}")
    
    # Teste 2: Conversas do WhatsApp
    try:
        response = requests.get(f"{BASE_URL}/whatsapp/conversations", headers=headers)
        if response.status_code == 200:
            conversations = response.json()
            print_success(f"Conversas WhatsApp: {len(conversations)} encontradas")
        else:
            print_error(f"Erro nas conversas WhatsApp: {response.status_code}")
    except Exception as e:
        print_error(f"Erro ao testar conversas WhatsApp: {e}")
    
    # Teste 3: Chat com IA
    try:
        response = requests.post(
            f"{BASE_URL}/chat/",
            json={
                "query": "Olá, este é um teste final do sistema",
                "contact_id": f"test_final_{int(time.time())}@c.us",
                "contact_name": "Teste Final"
            },
            headers=headers
        )
        
        if response.status_code == 200:
            chat_response = response.json()
            print_success("Chat com IA funcionando")
            print_info(f"Resposta: {chat_response.get('response', 'N/A')[:100]}...")
        else:
            print_error(f"Erro no chat: {response.status_code}")
    except Exception as e:
        print_error(f"Erro ao testar chat: {e}")

async def test_websocket():
    """Testa conexão WebSocket."""
    print_header("TESTE DE WEBSOCKET")
    
    try:
        async with websockets.connect(WS_URL) as websocket:
            print_success("Conexão WebSocket estabelecida")
            
            # Teste ping/pong
            await websocket.send("ping")
            response = await websocket.recv()
            if "ping" in response:
                print_success("Ping/Pong funcionando")
            
            # Teste mensagem personalizada
            test_message = "Teste final WebSocket"
            await websocket.send(test_message)
            response = await websocket.recv()
            if test_message in response:
                print_success("Echo de mensagem funcionando")
            
            print_success("WebSocket 100% funcional")
            
    except Exception as e:
        print_error(f"Erro no WebSocket: {e}")

def test_database_queries(token):
    """Testa consultas ao banco de dados."""
    print_header("TESTE DE BANCO DE DADOS")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Teste 1: Listar conversas
    try:
        response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
        if response.status_code == 200:
            conversations = response.json()
            print_success(f"Consulta de conversas: {len(conversations)} encontradas")
        else:
            print_error(f"Erro ao consultar conversas: {response.status_code}")
    except Exception as e:
        print_error(f"Erro na consulta de conversas: {e}")

def test_system_health():
    """Testa saúde geral do sistema."""
    print_header("TESTE DE SAÚDE DO SISTEMA")
    
    # Teste 1: Servidor respondendo
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print_success("Servidor FastAPI respondendo")
        else:
            print_error(f"Servidor com problemas: {response.status_code}")
    except Exception as e:
        print_error(f"Servidor inacessível: {e}")
    
    # Teste 2: Verificar portas
    import socket
    
    def check_port(host, port, service):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((host, port))
            sock.close()
            if result == 0:
                print_success(f"{service} (porta {port}) acessível")
            else:
                print_error(f"{service} (porta {port}) inacessível")
        except Exception as e:
            print_error(f"Erro ao verificar {service}: {e}")
    
    check_port("localhost", 8000, "Backend Python")
    check_port("localhost", 3000, "Frontend React")
    check_port("localhost", 3001, "Backend WhatsApp")

async def main():
    """Executa todos os testes."""
    print_header("🚀 TESTE FINAL COMPLETO DO SISTEMA RAFAELA")
    print_info(f"Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Teste de saúde do sistema
    test_system_health()
    
    # 2. Teste de autenticação
    token = test_authentication()
    if not token:
        print_error("Não é possível continuar sem autenticação")
        return
    
    # 3. Teste de endpoints da API
    test_api_endpoints(token)
    
    # 4. Teste de banco de dados
    test_database_queries(token)
    
    # 5. Teste de WebSocket
    await test_websocket()
    
    # Resultado final
    print_header("🎉 RESULTADO FINAL")
    print_success("Todos os testes principais foram executados!")
    print_info("Sistema Rafaela está operacional e pronto para uso!")
    print_info("Principais funcionalidades verificadas:")
    print_info("  ✅ Autenticação e autorização")
    print_info("  ✅ Integração com WhatsApp")
    print_info("  ✅ Chat com IA (Rafaela)")
    print_info("  ✅ WebSocket para notificações")
    print_info("  ✅ Consultas ao banco de dados")
    print_info("  ✅ Saúde geral do sistema")

if __name__ == "__main__":
    asyncio.run(main())
