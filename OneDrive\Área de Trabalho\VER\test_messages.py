#!/usr/bin/env python3
"""
Script para testar o fluxo completo de mensagens.
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"

def get_auth_token():
    """Obtém token de autenticação."""
    try:
        response = requests.post(
            f"{BASE_URL}/token",
            data={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            return response.json()['access_token']
        else:
            print(f"❌ Erro no login: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Erro na autenticação: {e}")
        return None

def test_chat_endpoint(token):
    """Testa o endpoint de chat."""
    headers = {"Authorization": f"Bearer {token}"}
    
    test_messages = [
        "Olá, como você está?",
        "Qual é o horário de funcionamento?",
        "Preciso de ajuda com um produto",
        "Obrigado pela ajuda!"
    ]
    
    print("💬 Testando endpoint de chat...")
    
    for i, message in enumerate(test_messages, 1):
        try:
            response = requests.post(
                f"{BASE_URL}/chat/",
                json={
                    "query": message,
                    "contact_id": f"test_contact_{int(time.time())}",
                    "contact_name": "Usuário Teste"
                },
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Mensagem {i}: {message[:30]}...")
                print(f"   Resposta: {data.get('response', 'N/A')[:50]}...")
            else:
                print(f"❌ Erro na mensagem {i}: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"❌ Erro na mensagem {i}: {e}")
        
        time.sleep(1)  # Pausa entre mensagens

def test_conversations_endpoint(token):
    """Testa o endpoint de conversas."""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📋 Testando endpoint de conversas...")
    
    try:
        response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
        
        if response.status_code == 200:
            conversations = response.json()
            print(f"✅ Encontradas {len(conversations)} conversas")

            # Verificar se conversations é uma lista
            if isinstance(conversations, list) and len(conversations) > 0:
                for i, conv in enumerate(conversations[:3], 1):  # Mostrar apenas 3 primeiras
                    print(f"   {i}. ID: {conv.get('id')} - Contato: {conv.get('contact_name', 'N/A')}")
                    print(f"      Última mensagem: {conv.get('last_message_at', 'N/A')}")
            else:
                print("   Nenhuma conversa encontrada ou formato inesperado")
        else:
            print(f"❌ Erro ao buscar conversas: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Erro no endpoint de conversas: {e}")

def test_whatsapp_endpoints(token):
    """Testa endpoints do WhatsApp."""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📱 Testando endpoints do WhatsApp...")
    
    # Status do WhatsApp
    try:
        response = requests.get(f"{BASE_URL}/whatsapp/status", headers=headers)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Status WhatsApp: {status.get('status')}")
        else:
            print(f"❌ Erro no status: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Erro no status WhatsApp: {e}")
    
    # Conversas do WhatsApp
    try:
        response = requests.get(f"{BASE_URL}/whatsapp/conversations", headers=headers)
        if response.status_code == 200:
            conversations = response.json()
            print(f"✅ Conversas WhatsApp: {len(conversations)} encontradas")
            
            for i, conv in enumerate(conversations[:2], 1):  # Mostrar apenas 2 primeiras
                contact_id = conv.get('id', {}).get('_serialized', 'N/A')
                name = conv.get('name', 'Sem nome')
                print(f"   {i}. {name} ({contact_id})")
        else:
            print(f"❌ Erro nas conversas WhatsApp: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Erro nas conversas WhatsApp: {e}")

def test_dashboard_endpoint(token):
    """Testa o endpoint do dashboard."""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📊 Testando endpoint do dashboard...")
    
    try:
        response = requests.get(f"{BASE_URL}/dashboard/", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Dashboard carregado com sucesso")
            print(f"   Total de conversas: {data.get('total_conversations', 'N/A')}")
            print(f"   Conversas pendentes: {data.get('pending_conversations', 'N/A')}")
            print(f"   Mensagens hoje: {data.get('messages_today', 'N/A')}")
        else:
            print(f"❌ Erro no dashboard: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Erro no endpoint do dashboard: {e}")

def main():
    print("🧪 Testando fluxo completo de mensagens...")
    
    # Obter token
    token = get_auth_token()
    if not token:
        print("❌ Não foi possível obter token de autenticação")
        return
    
    print(f"✅ Token obtido: {token[:50]}...")
    
    # Executar testes
    test_chat_endpoint(token)
    test_conversations_endpoint(token)
    test_whatsapp_endpoints(token)
    test_dashboard_endpoint(token)
    
    print("\n🎉 Testes concluídos!")

if __name__ == "__main__":
    main()
