{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\VER\\\\admin_panel\\\\src\\\\components\\\\whatsapp\\\\ConversationList.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport { List, ListItem, ListItemText, ListItemAvatar, Avatar, Typography, CircularProgress, Alert, Badge, Box, Divider } from '@mui/material';\nimport axiosInstance from '../../api/axiosInstance';\n\n// Função para formatar data e hora\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst formatDateTime = timestamp => {\n  if (!timestamp) return '';\n  const date = new Date(timestamp * 1000);\n  const now = new Date();\n  const diffInHours = (now - date) / (1000 * 60 * 60);\n  const diffInDays = Math.floor(diffInHours / 24);\n\n  // Se foi hoje, mostrar apenas a hora\n  if (diffInHours < 24 && date.getDate() === now.getDate()) {\n    return date.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Se foi ontem\n  if (diffInDays === 1) {\n    return `Ontem ${date.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    })}`;\n  }\n\n  // Se foi esta semana (últimos 7 dias)\n  if (diffInDays < 7) {\n    const weekdays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];\n    return `${weekdays[date.getDay()]} ${date.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    })}`;\n  }\n\n  // Para datas mais antigas, mostrar data completa\n  return date.toLocaleDateString('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: '2-digit'\n  }) + ' ' + date.toLocaleTimeString('pt-BR', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\nconst fetchConversations = async () => {\n  try {\n    const {\n      data\n    } = await axiosInstance.get('/whatsapp/conversations');\n    // Filtra para garantir que temos conversas válidas\n    return data.filter(chat => chat.id && (chat.id._serialized || chat.id));\n  } catch (error) {\n    console.warn('WhatsApp backend não disponível, usando dados simulados');\n    // Dados simulados para demonstração\n    return [{\n      id: {\n        _serialized: '<EMAIL>',\n        user: '558488501582'\n      },\n      name: 'Italo Cabral',\n      lastMessage: {\n        body: 'Bom dia! Preciso de ajuda com cesta básica',\n        timestamp: Date.now() / 1000 - 3600 // 1 hora atrás\n      },\n      timestamp: Date.now() / 1000 - 3600,\n      unreadCount: 2,\n      contact: {\n        formattedName: 'Italo Cabral',\n        profilePicThumbObj: {\n          eurl: ''\n        }\n      }\n    }, {\n      id: {\n        _serialized: '<EMAIL>',\n        user: '5511987654321'\n      },\n      name: 'Maria Silva',\n      lastMessage: {\n        body: 'Obrigada pela ajuda! Consegui resolver o problema.',\n        timestamp: Date.now() / 1000 - 7200 // 2 horas atrás\n      },\n      timestamp: Date.now() / 1000 - 7200,\n      unreadCount: 0,\n      contact: {\n        formattedName: 'Maria Silva',\n        profilePicThumbObj: {\n          eurl: ''\n        }\n      }\n    }, {\n      id: {\n        _serialized: '<EMAIL>',\n        user: '5511123456789'\n      },\n      name: 'João Santos',\n      lastMessage: {\n        body: 'Oi! Gostaria de saber sobre os programas sociais disponíveis.',\n        timestamp: Date.now() / 1000 - 86400 // 1 dia atrás\n      },\n      timestamp: Date.now() / 1000 - 86400,\n      unreadCount: 1,\n      contact: {\n        formattedName: 'João Santos',\n        profilePicThumbObj: {\n          eurl: ''\n        }\n      }\n    }, {\n      id: {\n        _serialized: '<EMAIL>',\n        user: '5511555666777'\n      },\n      name: 'Ana Costa',\n      lastMessage: {\n        body: 'Bom dia! Preciso agendar uma consulta na UBS.',\n        timestamp: Date.now() / 1000 - 172800 // 2 dias atrás\n      },\n      timestamp: Date.now() / 1000 - 172800,\n      unreadCount: 0,\n      contact: {\n        formattedName: 'Ana Costa',\n        profilePicThumbObj: {\n          eurl: ''\n        }\n      }\n    }];\n  }\n};\nconst ConversationList = ({\n  onConversationClick\n}) => {\n  _s();\n  const {\n    data: conversations,\n    isLoading,\n    isError,\n    error\n  } = useQuery({\n    queryKey: ['whatsapp-conversations'],\n    queryFn: fetchConversations\n  });\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 12\n    }, this);\n  }\n  if (isError) {\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      children: [\"Erro ao buscar conversas: \", error.message]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(List, {\n    sx: {\n      width: '100%',\n      bgcolor: 'background.paper',\n      p: 0\n    },\n    children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(Typography, {\n      sx: {\n        p: 2\n      },\n      children: \"Nenhuma conversa encontrada.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 9\n    }, this) : conversations.map((chat, index) => {\n      var _chat$contact2, _chat$contact2$profil, _chat$contact3, _chat$lastMessage2, _chat$lastMessage3, _chat$lastMessage4, _chat$lastMessage5, _chat$lastMessage6;\n      return /*#__PURE__*/_jsxDEV(React.Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(ListItem, {\n          alignItems: \"flex-start\",\n          sx: {\n            '&:hover': {\n              backgroundColor: 'action.hover'\n            },\n            cursor: 'pointer'\n          },\n          onClick: () => {\n            var _chat$contact, _chat$lastMessage;\n            return onConversationClick && onConversationClick({\n              id: chat.id._serialized,\n              contact_id: chat.id._serialized,\n              contact_name: chat.name || ((_chat$contact = chat.contact) === null || _chat$contact === void 0 ? void 0 : _chat$contact.formattedName) || chat.id.user || 'Contato sem nome',\n              unreadCount: chat.unreadCount || 0,\n              lastMessage: ((_chat$lastMessage = chat.lastMessage) === null || _chat$lastMessage === void 0 ? void 0 : _chat$lastMessage.body) || '',\n              timestamp: chat.timestamp || chat.t\n            });\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n            children: /*#__PURE__*/_jsxDEV(Avatar, {\n              alt: chat.name,\n              src: ((_chat$contact2 = chat.contact) === null || _chat$contact2 === void 0 ? void 0 : (_chat$contact2$profil = _chat$contact2.profilePicThumbObj) === null || _chat$contact2$profil === void 0 ? void 0 : _chat$contact2$profil.eurl) || ''\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: /*#__PURE__*/_jsxDEV(Typography, {\n              noWrap: true,\n              children: chat.name || ((_chat$contact3 = chat.contact) === null || _chat$contact3 === void 0 ? void 0 : _chat$contact3.formattedName) || chat.id.user || 'Contato sem nome'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 19\n            }, this),\n            secondary: /*#__PURE__*/_jsxDEV(Typography, {\n              noWrap: true,\n              color: \"text.secondary\",\n              variant: \"body2\",\n              sx: {\n                fontStyle: (_chat$lastMessage2 = chat.lastMessage) !== null && _chat$lastMessage2 !== void 0 && _chat$lastMessage2.body ? 'normal' : 'italic',\n                opacity: (_chat$lastMessage3 = chat.lastMessage) !== null && _chat$lastMessage3 !== void 0 && _chat$lastMessage3.body ? 1 : 0.7\n              },\n              children: ((_chat$lastMessage4 = chat.lastMessage) === null || _chat$lastMessage4 === void 0 ? void 0 : _chat$lastMessage4.body) || 'Nenhuma mensagem ainda.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'flex-end',\n              ml: 2\n            },\n            children: [(chat.timestamp || chat.t || ((_chat$lastMessage5 = chat.lastMessage) === null || _chat$lastMessage5 === void 0 ? void 0 : _chat$lastMessage5.timestamp)) && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'flex-end'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: formatDateTime(chat.timestamp || chat.t || ((_chat$lastMessage6 = chat.lastMessage) === null || _chat$lastMessage6 === void 0 ? void 0 : _chat$lastMessage6.timestamp))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 19\n            }, this), chat.unreadCount > 0 && /*#__PURE__*/_jsxDEV(Badge, {\n              badgeContent: chat.unreadCount,\n              color: \"primary\",\n              sx: {\n                mt: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this), index < conversations.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {\n          variant: \"inset\",\n          component: \"li\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 50\n        }, this)]\n      }, chat.id._serialized, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 11\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(ConversationList, \"vBwe6GVd2rt9S+0YW1HbqGhW75k=\", false, function () {\n  return [useQuery];\n});\n_c = ConversationList;\nexport default ConversationList;\nvar _c;\n$RefreshReg$(_c, \"ConversationList\");", "map": {"version": 3, "names": ["React", "useQuery", "List", "ListItem", "ListItemText", "ListItemAvatar", "Avatar", "Typography", "CircularProgress", "<PERSON><PERSON>", "Badge", "Box", "Divider", "axiosInstance", "jsxDEV", "_jsxDEV", "formatDateTime", "timestamp", "date", "Date", "now", "diffInHours", "diffInDays", "Math", "floor", "getDate", "toLocaleTimeString", "hour", "minute", "weekdays", "getDay", "toLocaleDateString", "day", "month", "year", "fetchConversations", "data", "get", "filter", "chat", "id", "_serialized", "error", "console", "warn", "user", "name", "lastMessage", "body", "unreadCount", "contact", "formattedName", "profilePicThumbObj", "eurl", "ConversationList", "onConversationClick", "_s", "conversations", "isLoading", "isError", "query<PERSON><PERSON>", "queryFn", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "children", "message", "sx", "width", "bgcolor", "p", "length", "map", "index", "_chat$contact2", "_chat$contact2$profil", "_chat$contact3", "_chat$lastMessage2", "_chat$lastMessage3", "_chat$lastMessage4", "_chat$lastMessage5", "_chat$lastMessage6", "Fragment", "alignItems", "backgroundColor", "cursor", "onClick", "_chat$contact", "_chat$lastMessage", "contact_id", "contact_name", "t", "alt", "src", "primary", "noWrap", "secondary", "color", "variant", "fontStyle", "opacity", "display", "flexDirection", "ml", "badgeContent", "mt", "component", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/VER/admin_panel/src/components/whatsapp/ConversationList.js"], "sourcesContent": ["import React from 'react';\nimport { useQuery } from '@tanstack/react-query';\nimport { List, ListItem, ListItemText, ListItemAvatar, Avatar, Typography, CircularProgress, Alert, Badge, Box, Divider } from '@mui/material';\nimport axiosInstance from '../../api/axiosInstance';\n\n// Função para formatar data e hora\nconst formatDateTime = (timestamp) => {\n  if (!timestamp) return '';\n\n  const date = new Date(timestamp * 1000);\n  const now = new Date();\n  const diffInHours = (now - date) / (1000 * 60 * 60);\n  const diffInDays = Math.floor(diffInHours / 24);\n\n  // Se foi hoje, mostrar apenas a hora\n  if (diffInHours < 24 && date.getDate() === now.getDate()) {\n    return date.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  // Se foi ontem\n  if (diffInDays === 1) {\n    return `Ontem ${date.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    })}`;\n  }\n\n  // Se foi esta semana (últimos 7 dias)\n  if (diffInDays < 7) {\n    const weekdays = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];\n    return `${weekdays[date.getDay()]} ${date.toLocaleTimeString('pt-BR', {\n      hour: '2-digit',\n      minute: '2-digit'\n    })}`;\n  }\n\n  // Para datas mais antigas, mostrar data completa\n  return date.toLocaleDateString('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: '2-digit'\n  }) + ' ' + date.toLocaleTimeString('pt-BR', {\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\nconst fetchConversations = async () => {\n  try {\n    const { data } = await axiosInstance.get('/whatsapp/conversations');\n    // Filtra para garantir que temos conversas válidas\n    return data.filter(chat => chat.id && (chat.id._serialized || chat.id));\n  } catch (error) {\n    console.warn('WhatsApp backend não disponível, usando dados simulados');\n    // Dados simulados para demonstração\n    return [\n      {\n        id: { _serialized: '<EMAIL>', user: '558488501582' },\n        name: 'Italo Cabral',\n        lastMessage: {\n          body: 'Bom dia! Preciso de ajuda com cesta básica',\n          timestamp: Date.now() / 1000 - 3600 // 1 hora atrás\n        },\n        timestamp: Date.now() / 1000 - 3600,\n        unreadCount: 2,\n        contact: {\n          formattedName: 'Italo Cabral',\n          profilePicThumbObj: { eurl: '' }\n        }\n      },\n      {\n        id: { _serialized: '<EMAIL>', user: '5511987654321' },\n        name: 'Maria Silva',\n        lastMessage: {\n          body: 'Obrigada pela ajuda! Consegui resolver o problema.',\n          timestamp: Date.now() / 1000 - 7200 // 2 horas atrás\n        },\n        timestamp: Date.now() / 1000 - 7200,\n        unreadCount: 0,\n        contact: {\n          formattedName: 'Maria Silva',\n          profilePicThumbObj: { eurl: '' }\n        }\n      },\n      {\n        id: { _serialized: '<EMAIL>', user: '5511123456789' },\n        name: 'João Santos',\n        lastMessage: {\n          body: 'Oi! Gostaria de saber sobre os programas sociais disponíveis.',\n          timestamp: Date.now() / 1000 - 86400 // 1 dia atrás\n        },\n        timestamp: Date.now() / 1000 - 86400,\n        unreadCount: 1,\n        contact: {\n          formattedName: 'João Santos',\n          profilePicThumbObj: { eurl: '' }\n        }\n      },\n      {\n        id: { _serialized: '<EMAIL>', user: '5511555666777' },\n        name: 'Ana Costa',\n        lastMessage: {\n          body: 'Bom dia! Preciso agendar uma consulta na UBS.',\n          timestamp: Date.now() / 1000 - 172800 // 2 dias atrás\n        },\n        timestamp: Date.now() / 1000 - 172800,\n        unreadCount: 0,\n        contact: {\n          formattedName: 'Ana Costa',\n          profilePicThumbObj: { eurl: '' }\n        }\n      }\n    ];\n  }\n};\n\nconst ConversationList = ({ onConversationClick }) => {\n  const { data: conversations, isLoading, isError, error } = useQuery({\n    queryKey: ['whatsapp-conversations'],\n    queryFn: fetchConversations,\n  });\n\n  if (isLoading) {\n    return <CircularProgress />;\n  }\n\n  if (isError) {\n    return <Alert severity=\"error\">Erro ao buscar conversas: {error.message}</Alert>;\n  }\n\n  return (\n    <List sx={{ width: '100%', bgcolor: 'background.paper', p: 0 }}>\n      {conversations.length === 0 ? (\n        <Typography sx={{ p: 2 }}>Nenhuma conversa encontrada.</Typography>\n      ) : (\n        conversations.map((chat, index) => (\n          <React.Fragment key={chat.id._serialized}>\n            <ListItem\n              alignItems=\"flex-start\"\n              sx={{ '&:hover': { backgroundColor: 'action.hover' }, cursor: 'pointer' }}\n              onClick={() => onConversationClick && onConversationClick({\n                id: chat.id._serialized,\n                contact_id: chat.id._serialized,\n                contact_name: chat.name || chat.contact?.formattedName || chat.id.user || 'Contato sem nome',\n                unreadCount: chat.unreadCount || 0,\n                lastMessage: chat.lastMessage?.body || '',\n                timestamp: chat.timestamp || chat.t\n              })}\n            >\n              <ListItemAvatar>\n                <Avatar alt={chat.name} src={chat.contact?.profilePicThumbObj?.eurl || ''} />\n              </ListItemAvatar>\n              <ListItemText\n                primary={\n                  <Typography noWrap>\n                    {chat.name || chat.contact?.formattedName || chat.id.user || 'Contato sem nome'}\n                  </Typography>\n                }\n                secondary={\n                  <Typography\n                    noWrap\n                    color=\"text.secondary\"\n                    variant=\"body2\"\n                    sx={{\n                      fontStyle: chat.lastMessage?.body ? 'normal' : 'italic',\n                      opacity: chat.lastMessage?.body ? 1 : 0.7\n                    }}\n                  >\n                    {chat.lastMessage?.body || 'Nenhuma mensagem ainda.'}\n                  </Typography>\n                }\n              />\n              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', ml: 2 }}>\n                {(chat.timestamp || chat.t || chat.lastMessage?.timestamp) && (\n                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      {formatDateTime(chat.timestamp || chat.t || chat.lastMessage?.timestamp)}\n                    </Typography>\n                  </Box>\n                )}\n                {chat.unreadCount > 0 && (\n                  <Badge badgeContent={chat.unreadCount} color=\"primary\" sx={{ mt: 1 }} />\n                )}\n              </Box>\n            </ListItem>\n            {index < conversations.length - 1 && <Divider variant=\"inset\" component=\"li\" />}\n          </React.Fragment>\n        ))\n      )}\n    </List>\n  );\n};\n\nexport default ConversationList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,QAAQ,eAAe;AAC9I,OAAOC,aAAa,MAAM,yBAAyB;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAIC,SAAS,IAAK;EACpC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;EAEzB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,GAAG,IAAI,CAAC;EACvC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;EACtB,MAAME,WAAW,GAAG,CAACD,GAAG,GAAGF,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;EACnD,MAAMI,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACH,WAAW,GAAG,EAAE,CAAC;;EAE/C;EACA,IAAIA,WAAW,GAAG,EAAE,IAAIH,IAAI,CAACO,OAAO,CAAC,CAAC,KAAKL,GAAG,CAACK,OAAO,CAAC,CAAC,EAAE;IACxD,OAAOP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIN,UAAU,KAAK,CAAC,EAAE;IACpB,OAAO,SAASJ,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MAC/CC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC,EAAE;EACN;;EAEA;EACA,IAAIN,UAAU,GAAG,CAAC,EAAE;IAClB,MAAMO,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAClE,OAAO,GAAGA,QAAQ,CAACX,IAAI,CAACY,MAAM,CAAC,CAAC,CAAC,IAAIZ,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;MACpEC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC,EAAE;EACN;;EAEA;EACA,OAAOV,IAAI,CAACa,kBAAkB,CAAC,OAAO,EAAE;IACtCC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE;EACR,CAAC,CAAC,GAAG,GAAG,GAAGhB,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;IAC1CC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ,CAAC;AAED,MAAMO,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EACrC,IAAI;IACF,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMvB,aAAa,CAACwB,GAAG,CAAC,yBAAyB,CAAC;IACnE;IACA,OAAOD,IAAI,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKD,IAAI,CAACC,EAAE,CAACC,WAAW,IAAIF,IAAI,CAACC,EAAE,CAAC,CAAC;EACzE,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,yDAAyD,CAAC;IACvE;IACA,OAAO,CACL;MACEJ,EAAE,EAAE;QAAEC,WAAW,EAAE,mBAAmB;QAAEI,IAAI,EAAE;MAAe,CAAC;MAC9DC,IAAI,EAAE,cAAc;MACpBC,WAAW,EAAE;QACXC,IAAI,EAAE,4CAA4C;QAClD/B,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;MACtC,CAAC;MACDH,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;MACnC6B,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE;QACPC,aAAa,EAAE,cAAc;QAC7BC,kBAAkB,EAAE;UAAEC,IAAI,EAAE;QAAG;MACjC;IACF,CAAC,EACD;MACEb,EAAE,EAAE;QAAEC,WAAW,EAAE,oBAAoB;QAAEI,IAAI,EAAE;MAAgB,CAAC;MAChEC,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE;QACXC,IAAI,EAAE,oDAAoD;QAC1D/B,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;MACtC,CAAC;MACDH,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;MACnC6B,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE;QACPC,aAAa,EAAE,aAAa;QAC5BC,kBAAkB,EAAE;UAAEC,IAAI,EAAE;QAAG;MACjC;IACF,CAAC,EACD;MACEb,EAAE,EAAE;QAAEC,WAAW,EAAE,oBAAoB;QAAEI,IAAI,EAAE;MAAgB,CAAC;MAChEC,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE;QACXC,IAAI,EAAE,+DAA+D;QACrE/B,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;MACvC,CAAC;MACDH,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK;MACpC6B,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE;QACPC,aAAa,EAAE,aAAa;QAC5BC,kBAAkB,EAAE;UAAEC,IAAI,EAAE;QAAG;MACjC;IACF,CAAC,EACD;MACEb,EAAE,EAAE;QAAEC,WAAW,EAAE,oBAAoB;QAAEI,IAAI,EAAE;MAAgB,CAAC;MAChEC,IAAI,EAAE,WAAW;MACjBC,WAAW,EAAE;QACXC,IAAI,EAAE,+CAA+C;QACrD/B,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC;MACxC,CAAC;MACDH,SAAS,EAAEE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,MAAM;MACrC6B,WAAW,EAAE,CAAC;MACdC,OAAO,EAAE;QACPC,aAAa,EAAE,WAAW;QAC1BC,kBAAkB,EAAE;UAAEC,IAAI,EAAE;QAAG;MACjC;IACF,CAAC,CACF;EACH;AACF,CAAC;AAED,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACpD,MAAM;IAAEpB,IAAI,EAAEqB,aAAa;IAAEC,SAAS;IAAEC,OAAO;IAAEjB;EAAM,CAAC,GAAGzC,QAAQ,CAAC;IAClE2D,QAAQ,EAAE,CAAC,wBAAwB,CAAC;IACpCC,OAAO,EAAE1B;EACX,CAAC,CAAC;EAEF,IAAIuB,SAAS,EAAE;IACb,oBAAO3C,OAAA,CAACP,gBAAgB;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7B;EAEA,IAAIN,OAAO,EAAE;IACX,oBAAO5C,OAAA,CAACN,KAAK;MAACyD,QAAQ,EAAC,OAAO;MAAAC,QAAA,GAAC,4BAA0B,EAACzB,KAAK,CAAC0B,OAAO;IAAA;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAClF;EAEA,oBACElD,OAAA,CAACb,IAAI;IAACmE,EAAE,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,OAAO,EAAE,kBAAkB;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAL,QAAA,EAC5DV,aAAa,CAACgB,MAAM,KAAK,CAAC,gBACzB1D,OAAA,CAACR,UAAU;MAAC8D,EAAE,EAAE;QAAEG,CAAC,EAAE;MAAE,CAAE;MAAAL,QAAA,EAAC;IAA4B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,GAEnER,aAAa,CAACiB,GAAG,CAAC,CAACnC,IAAI,EAAEoC,KAAK;MAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,kBAAA;MAAA,oBAC5BpE,OAAA,CAACf,KAAK,CAACoF,QAAQ;QAAAjB,QAAA,gBACbpD,OAAA,CAACZ,QAAQ;UACPkF,UAAU,EAAC,YAAY;UACvBhB,EAAE,EAAE;YAAE,SAAS,EAAE;cAAEiB,eAAe,EAAE;YAAe,CAAC;YAAEC,MAAM,EAAE;UAAU,CAAE;UAC1EC,OAAO,EAAEA,CAAA;YAAA,IAAAC,aAAA,EAAAC,iBAAA;YAAA,OAAMnC,mBAAmB,IAAIA,mBAAmB,CAAC;cACxDf,EAAE,EAAED,IAAI,CAACC,EAAE,CAACC,WAAW;cACvBkD,UAAU,EAAEpD,IAAI,CAACC,EAAE,CAACC,WAAW;cAC/BmD,YAAY,EAAErD,IAAI,CAACO,IAAI,MAAA2C,aAAA,GAAIlD,IAAI,CAACW,OAAO,cAAAuC,aAAA,uBAAZA,aAAA,CAActC,aAAa,KAAIZ,IAAI,CAACC,EAAE,CAACK,IAAI,IAAI,kBAAkB;cAC5FI,WAAW,EAAEV,IAAI,CAACU,WAAW,IAAI,CAAC;cAClCF,WAAW,EAAE,EAAA2C,iBAAA,GAAAnD,IAAI,CAACQ,WAAW,cAAA2C,iBAAA,uBAAhBA,iBAAA,CAAkB1C,IAAI,KAAI,EAAE;cACzC/B,SAAS,EAAEsB,IAAI,CAACtB,SAAS,IAAIsB,IAAI,CAACsD;YACpC,CAAC,CAAC;UAAA,CAAC;UAAA1B,QAAA,gBAEHpD,OAAA,CAACV,cAAc;YAAA8D,QAAA,eACbpD,OAAA,CAACT,MAAM;cAACwF,GAAG,EAAEvD,IAAI,CAACO,IAAK;cAACiD,GAAG,EAAE,EAAAnB,cAAA,GAAArC,IAAI,CAACW,OAAO,cAAA0B,cAAA,wBAAAC,qBAAA,GAAZD,cAAA,CAAcxB,kBAAkB,cAAAyB,qBAAA,uBAAhCA,qBAAA,CAAkCxB,IAAI,KAAI;YAAG;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,eACjBlD,OAAA,CAACX,YAAY;YACX4F,OAAO,eACLjF,OAAA,CAACR,UAAU;cAAC0F,MAAM;cAAA9B,QAAA,EACf5B,IAAI,CAACO,IAAI,MAAAgC,cAAA,GAAIvC,IAAI,CAACW,OAAO,cAAA4B,cAAA,uBAAZA,cAAA,CAAc3B,aAAa,KAAIZ,IAAI,CAACC,EAAE,CAACK,IAAI,IAAI;YAAkB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CACb;YACDiC,SAAS,eACPnF,OAAA,CAACR,UAAU;cACT0F,MAAM;cACNE,KAAK,EAAC,gBAAgB;cACtBC,OAAO,EAAC,OAAO;cACf/B,EAAE,EAAE;gBACFgC,SAAS,EAAE,CAAAtB,kBAAA,GAAAxC,IAAI,CAACQ,WAAW,cAAAgC,kBAAA,eAAhBA,kBAAA,CAAkB/B,IAAI,GAAG,QAAQ,GAAG,QAAQ;gBACvDsD,OAAO,EAAE,CAAAtB,kBAAA,GAAAzC,IAAI,CAACQ,WAAW,cAAAiC,kBAAA,eAAhBA,kBAAA,CAAkBhC,IAAI,GAAG,CAAC,GAAG;cACxC,CAAE;cAAAmB,QAAA,EAED,EAAAc,kBAAA,GAAA1C,IAAI,CAACQ,WAAW,cAAAkC,kBAAA,uBAAhBA,kBAAA,CAAkBjC,IAAI,KAAI;YAAyB;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFlD,OAAA,CAACJ,GAAG;YAAC0D,EAAE,EAAE;cAAEkC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE,QAAQ;cAAEnB,UAAU,EAAE,UAAU;cAAEoB,EAAE,EAAE;YAAE,CAAE;YAAAtC,QAAA,GAClF,CAAC5B,IAAI,CAACtB,SAAS,IAAIsB,IAAI,CAACsD,CAAC,MAAAX,kBAAA,GAAI3C,IAAI,CAACQ,WAAW,cAAAmC,kBAAA,uBAAhBA,kBAAA,CAAkBjE,SAAS,mBACvDF,OAAA,CAACJ,GAAG;cAAC0D,EAAE,EAAE;gBAAEkC,OAAO,EAAE,MAAM;gBAAEC,aAAa,EAAE,QAAQ;gBAAEnB,UAAU,EAAE;cAAW,CAAE;cAAAlB,QAAA,eAC5EpD,OAAA,CAACR,UAAU;gBAAC6F,OAAO,EAAC,SAAS;gBAACD,KAAK,EAAC,gBAAgB;gBAAAhC,QAAA,EACjDnD,cAAc,CAACuB,IAAI,CAACtB,SAAS,IAAIsB,IAAI,CAACsD,CAAC,MAAAV,kBAAA,GAAI5C,IAAI,CAACQ,WAAW,cAAAoC,kBAAA,uBAAhBA,kBAAA,CAAkBlE,SAAS;cAAC;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN,EACA1B,IAAI,CAACU,WAAW,GAAG,CAAC,iBACnBlC,OAAA,CAACL,KAAK;cAACgG,YAAY,EAAEnE,IAAI,CAACU,WAAY;cAACkD,KAAK,EAAC,SAAS;cAAC9B,EAAE,EAAE;gBAAEsC,EAAE,EAAE;cAAE;YAAE;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACxE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACVU,KAAK,GAAGlB,aAAa,CAACgB,MAAM,GAAG,CAAC,iBAAI1D,OAAA,CAACH,OAAO;UAACwF,OAAO,EAAC,OAAO;UAACQ,SAAS,EAAC;QAAI;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,GAjD5D1B,IAAI,CAACC,EAAE,CAACC,WAAW;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkDxB,CAAC;IAAA,CAClB;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAACT,EAAA,CA3EIF,gBAAgB;EAAA,QACuCrD,QAAQ;AAAA;AAAA4G,EAAA,GAD/DvD,gBAAgB;AA6EtB,eAAeA,gBAAgB;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}