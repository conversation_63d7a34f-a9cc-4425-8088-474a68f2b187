import { createTheme } from '@mui/material/styles';

// Create a theme instance.
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2', // Um azul profissional
    },
    secondary: {
      main: '#dc004e', // Um rosa/vermelho para contraste
    },
    background: {
      default: '#f4f6f8', // Um cinza muito claro para o fundo
      paper: '#ffffff',   // Branco para os cards e papéis
    },
    text: {
      primary: '#172b4d',
      secondary: '#5e6c84',
    },
  },
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    h5: {
      fontWeight: 600,
    },
  },
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.08)',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
        },
      },
    },
  },
});

export default theme;
