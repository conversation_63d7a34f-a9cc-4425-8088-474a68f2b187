"""
Testes de integração para o sistema completo de chat da persona
"""

import unittest
import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.persona import RESPONSE_TEMPLATES, EMOJIS, HASHTAGS
from pipelines.post_processing import process_response
from handlers.intent_handlers import create_intent_handler


class TestChatPersonaIntegration(unittest.TestCase):
    """
    Testes de integração para o sistema completo de chat da persona
    """
    
    def setUp(self):
        """Configurar testes de integração"""
        self.mock_supabase = Mock()
        self.handler = create_intent_handler(self.mock_supabase)
    
    def test_complete_template_flow(self):
        """Testa o fluxo completo de template"""
        # 1. Handler detecta template
        raw_response = self.handler.handle_template_query("Bom dia!")
        self.assertIsNotNone(raw_response)
        
        # 2. Resposta já vem processada (com emoji/hashtag)
        self.assertIsInstance(raw_response, str)
        
        # 3. Verificar se contém elementos da persona
        self.assertGreater(len(raw_response), 10)
    
    def test_template_to_postprocessing_integration(self):
        """Testa integração entre templates e pós-processamento"""
        # Obter resposta de template sem processamento
        for template_key, template_data in RESPONSE_TEMPLATES.items():
            raw_response = template_data["response"]
            
            # Processar com pipeline
            processed_response = process_response(raw_response)
            
            # Verificar se foi processado
            self.assertIsInstance(processed_response, str)
            self.assertGreaterEqual(len(processed_response), len(raw_response))
    
    @patch('handlers.intent_handlers.generate_rag_response')
    def test_rag_to_postprocessing_integration(self, mock_generate_rag):
        """Testa integração RAG com pós-processamento"""
        # Mock da busca de documentos
        self.handler._search_documents = AsyncMock(return_value="Contexto relevante")
        
        # Mock da geração RAG
        mock_generate_rag.return_value = "Resposta baseada em documentos sobre Parnamirim"
        
        async def run_test():
            # 1. Handler RAG
            rag_response = await self.handler.handle_rag_query(
                "Quais são os programas sociais?", 
                []
            )
            
            # 2. Verificar se foi processado
            self.assertIsNotNone(rag_response)
            self.assertIsInstance(rag_response, str)
            
            return rag_response
        
        response = asyncio.run(run_test())
        self.assertIsNotNone(response)
    
    def test_persona_consistency_across_handlers(self):
        """Testa consistência da persona entre diferentes handlers"""
        # Coletar respostas de diferentes handlers
        responses = []
        
        # Template responses
        template_response = self.handler.handle_template_query("Oi")
        if template_response:
            responses.append(template_response)
        
        # Fallback response
        fallback_response = self.handler.get_fallback_response()
        responses.append(fallback_response)
        
        # Verificar consistência de tom
        for response in responses:
            # Verificar se mantém tom da persona
            self.assertIsInstance(response, str)
            self.assertGreater(len(response), 10)
            
            # Verificar elementos da persona (tom acolhedor)
            persona_indicators = ["você", "nossa", "juntos", "equipe", "ajudar"]
            has_persona = any(indicator in response.lower() for indicator in persona_indicators)
            self.assertTrue(has_persona, f"Resposta sem elementos da persona: {response}")
    
    def test_emoji_hashtag_contextual_integration(self):
        """Testa integração contextual de emojis e hashtags"""
        test_cases = [
            ("Vou verificar na UBS", ["🏥", "💪", "💖"]),  # Saúde
            ("Vamos trabalhar juntos", ["💪", "🤝", "🙌"]),  # Trabalho
            ("Que Deus nos abençoe", ["🙏", "✨"]),  # Fé
            ("Transparência é fundamental", ["📊", "✨", "🙌"])  # Transparência
        ]
        
        for text, expected_emojis in test_cases:
            processed = process_response(text, add_emoji=True, add_hashtag=True)
            
            # Verificar se algum emoji esperado foi adicionado
            has_expected_emoji = any(emoji in processed for emoji in expected_emojis)
            self.assertTrue(has_expected_emoji, f"Emoji contextual não encontrado em: {processed}")
            
            # Verificar se hashtag foi adicionada
            self.assertIn("#", processed)
    
    def test_response_length_limits(self):
        """Testa limites de comprimento em todo o sistema"""
        # Texto muito longo
        long_text = "Esta é uma resposta muito longa. " * 50
        
        # Processar
        processed = process_response(long_text)
        
        # Verificar limite
        from config.persona import PERSONA_CONFIG
        max_length = PERSONA_CONFIG["max_response_length"]
        self.assertLessEqual(len(processed), max_length)
    
    def test_error_handling_integration(self):
        """Testa tratamento de erros em todo o sistema"""
        # Teste com entrada vazia
        empty_response = process_response("")
        self.assertIsInstance(empty_response, str)
        
        # Teste com entrada None (simulando erro)
        try:
            none_response = process_response(None)
            # Se não der erro, deve retornar string
            self.assertIsInstance(none_response, str)
        except:
            # Se der erro, é aceitável
            pass
        
        # Teste template com query vazia
        empty_template = self.handler.handle_template_query("")
        self.assertIsNone(empty_template)
    
    def test_multilingual_keywords(self):
        """Testa palavras-chave com variações linguísticas"""
        variations = [
            ("oi", "saudacao_geral"),
            ("olá", "saudacao_geral"),
            ("ola", "saudacao_geral"),
            ("bom dia", "saudacao_geral"),
            ("boa tarde", "saudacao_geral"),
            ("remedio", "medicamentos"),
            ("remédio", "medicamentos"),
            ("medicamento", "medicamentos")
        ]
        
        for query, expected_template in variations:
            response = self.handler.handle_template_query(query)
            self.assertIsNotNone(response, f"Variação '{query}' não funcionou para {expected_template}")
    
    def test_conversation_flow_simulation(self):
        """Simula um fluxo completo de conversa"""
        conversation_steps = [
            ("Bom dia!", "template"),
            ("Preciso de cesta básica", "template"),
            ("Como faço o cadastro no CRAS?", "general"),
            ("Obrigado pela ajuda", "template")
        ]
        
        conversation_history = []
        
        for query, expected_type in conversation_steps:
            # Tentar template primeiro
            response = self.handler.handle_template_query(query)
            
            if response is None and expected_type == "general":
                # Simular resposta geral
                response = "Vou te ajudar com essa informação. Nossa equipe está sempre disponível!"
                response = process_response(response)
            
            # Verificar se obteve resposta
            self.assertIsNotNone(response, f"Sem resposta para: {query}")
            
            # Adicionar ao histórico
            conversation_history.append({
                'sender': 'user',
                'content': query
            })
            conversation_history.append({
                'sender': 'ai', 
                'content': response
            })
        
        # Verificar se o histórico foi construído
        self.assertEqual(len(conversation_history), 8)  # 4 perguntas + 4 respostas
    
    def test_persona_elements_preservation(self):
        """Testa se elementos da persona são preservados"""
        # Elementos essenciais da persona
        essential_elements = {
            "nome": "Rafaela",
            "cargo": "Vereadora", 
            "cidade": "Parnamirim",
            "tom": ["você", "nossa", "juntos"],
            "valores": ["inclusão", "transparência", "acessibilidade"]
        }
        
        # Testar em diferentes tipos de resposta
        responses_to_test = []
        
        # Template responses
        for template_data in list(RESPONSE_TEMPLATES.values())[:3]:
            responses_to_test.append(template_data["response"])
        
        # Fallback response
        responses_to_test.append(self.handler.get_fallback_response())
        
        # Verificar presença de elementos da persona
        for response in responses_to_test:
            # Pelo menos alguns elementos devem estar presentes
            has_persona_elements = (
                any(tom in response.lower() for tom in essential_elements["tom"]) or
                "equipe" in response.lower() or
                "ajudar" in response.lower()
            )
            self.assertTrue(has_persona_elements, f"Resposta sem elementos da persona: {response}")


if __name__ == "__main__":
    unittest.main()
