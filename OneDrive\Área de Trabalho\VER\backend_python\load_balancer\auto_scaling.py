"""
Sistema de Load Balancing Automático e Auto Scaling
Gerencia carga e escala recursos automaticamente
"""

import asyncio
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import deque, defaultdict
import psutil
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue


@dataclass
class ServerMetrics:
    """Métricas de um servidor/worker"""
    server_id: str
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    active_requests: int = 0
    total_requests: int = 0
    avg_response_time: float = 0.0
    error_rate: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)
    is_healthy: bool = True
    
    @property
    def load_score(self) -> float:
        """Calcula score de carga (0-100, menor é melhor)"""
        cpu_weight = 0.4
        memory_weight = 0.3
        requests_weight = 0.2
        response_time_weight = 0.1
        
        # Normalizar métricas
        cpu_score = min(self.cpu_usage, 100)
        memory_score = min(self.memory_usage, 100)
        requests_score = min(self.active_requests * 10, 100)  # 10 requests = 100%
        response_time_score = min(self.avg_response_time * 10, 100)  # 10s = 100%
        
        return (
            cpu_score * cpu_weight +
            memory_score * memory_weight +
            requests_score * requests_weight +
            response_time_score * response_time_weight
        )


@dataclass
class LoadBalancerConfig:
    """Configuração do load balancer"""
    max_workers: int = 10
    min_workers: int = 2
    scale_up_threshold: float = 70.0  # % de carga
    scale_down_threshold: float = 30.0  # % de carga
    health_check_interval: int = 30  # segundos
    metrics_window: int = 300  # 5 minutos
    request_timeout: int = 30  # segundos
    max_retries: int = 3


@dataclass
class RequestMetrics:
    """Métricas de uma requisição"""
    request_id: str
    start_time: float
    end_time: Optional[float] = None
    server_id: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None
    
    @property
    def duration(self) -> float:
        """Duração da requisição em segundos"""
        if self.end_time:
            return self.end_time - self.start_time
        return time.time() - self.start_time


class WorkerPool:
    """Pool de workers para processamento"""
    
    def __init__(self, worker_function: Callable, initial_size: int = 2):
        self.worker_function = worker_function
        self.workers: Dict[str, ThreadPoolExecutor] = {}
        self.worker_metrics: Dict[str, ServerMetrics] = {}
        self.request_queue = queue.Queue()
        self.active_requests: Dict[str, RequestMetrics] = {}
        
        # Inicializar workers
        for i in range(initial_size):
            self._add_worker(f"worker_{i}")
    
    def _add_worker(self, worker_id: str):
        """Adiciona um novo worker"""
        executor = ThreadPoolExecutor(max_workers=1, thread_name_prefix=worker_id)
        self.workers[worker_id] = executor
        self.worker_metrics[worker_id] = ServerMetrics(server_id=worker_id)
        print(f"🔧 Worker {worker_id} adicionado")
    
    def _remove_worker(self, worker_id: str):
        """Remove um worker"""
        if worker_id in self.workers:
            self.workers[worker_id].shutdown(wait=True)
            del self.workers[worker_id]
            del self.worker_metrics[worker_id]
            print(f"🗑️ Worker {worker_id} removido")
    
    def get_best_worker(self) -> Optional[str]:
        """Seleciona o melhor worker baseado na carga"""
        if not self.worker_metrics:
            return None
        
        # Filtrar workers saudáveis
        healthy_workers = {
            worker_id: metrics for worker_id, metrics in self.worker_metrics.items()
            if metrics.is_healthy
        }
        
        if not healthy_workers:
            return None
        
        # Selecionar worker com menor carga
        best_worker = min(
            healthy_workers.items(),
            key=lambda x: x[1].load_score
        )
        
        return best_worker[0]
    
    def update_worker_metrics(self, worker_id: str, **metrics):
        """Atualiza métricas de um worker"""
        if worker_id in self.worker_metrics:
            worker_metrics = self.worker_metrics[worker_id]
            
            for key, value in metrics.items():
                if hasattr(worker_metrics, key):
                    setattr(worker_metrics, key, value)
            
            worker_metrics.last_updated = datetime.now()


class AutoLoadBalancer:
    """
    Load Balancer automático com auto scaling
    """
    
    def __init__(self, worker_function: Callable, config: LoadBalancerConfig = None):
        self.config = config or LoadBalancerConfig()
        self.worker_pool = WorkerPool(worker_function, self.config.min_workers)
        
        # Métricas e monitoramento
        self.request_history: deque = deque(maxlen=1000)
        self.system_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.active_requests: Dict[str, RequestMetrics] = {}
        
        # Controle de scaling
        self.last_scale_action = datetime.now()
        self.scale_cooldown = timedelta(minutes=2)
        
        # Threads de monitoramento
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        # Lock para thread safety
        self.lock = threading.RLock()
    
    async def process_request(self, request_data: Any, request_id: str = None) -> Any:
        """Processa uma requisição com load balancing"""
        if request_id is None:
            request_id = f"req_{int(time.time() * 1000)}"
        
        request_metrics = RequestMetrics(
            request_id=request_id,
            start_time=time.time()
        )
        
        with self.lock:
            self.active_requests[request_id] = request_metrics
        
        try:
            # Selecionar worker
            worker_id = self.worker_pool.get_best_worker()
            if not worker_id:
                raise Exception("Nenhum worker disponível")
            
            request_metrics.server_id = worker_id
            
            # Atualizar métricas do worker
            self.worker_pool.update_worker_metrics(
                worker_id,
                active_requests=self.worker_pool.worker_metrics[worker_id].active_requests + 1
            )
            
            # Processar requisição
            executor = self.worker_pool.workers[worker_id]
            future = executor.submit(self.worker_pool.worker_function, request_data)
            
            # Aguardar resultado com timeout
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: future.result(timeout=self.config.request_timeout)
            )
            
            request_metrics.success = True
            return result
            
        except Exception as e:
            request_metrics.success = False
            request_metrics.error_message = str(e)
            raise
        
        finally:
            # Finalizar métricas
            request_metrics.end_time = time.time()
            
            with self.lock:
                if request_id in self.active_requests:
                    del self.active_requests[request_id]
                
                self.request_history.append(request_metrics)
                
                # Atualizar métricas do worker
                if request_metrics.server_id:
                    worker_metrics = self.worker_pool.worker_metrics[request_metrics.server_id]
                    worker_metrics.active_requests = max(0, worker_metrics.active_requests - 1)
                    worker_metrics.total_requests += 1
                    
                    # Atualizar tempo médio de resposta
                    if worker_metrics.avg_response_time == 0:
                        worker_metrics.avg_response_time = request_metrics.duration
                    else:
                        worker_metrics.avg_response_time = (
                            worker_metrics.avg_response_time * 0.9 + 
                            request_metrics.duration * 0.1
                        )
    
    def _monitoring_loop(self):
        """Loop de monitoramento e auto scaling"""
        while self.monitoring_active:
            try:
                self._collect_system_metrics()
                self._update_worker_health()
                self._check_scaling_conditions()
                
                time.sleep(self.config.health_check_interval)
                
            except Exception as e:
                print(f"Erro no monitoramento: {e}")
                time.sleep(5)
    
    def _collect_system_metrics(self):
        """Coleta métricas do sistema"""
        now = datetime.now()
        
        # Métricas do sistema
        cpu_percent = psutil.cpu_percent(interval=1)
        memory_percent = psutil.virtual_memory().percent
        
        self.system_metrics['cpu'].append((now, cpu_percent))
        self.system_metrics['memory'].append((now, memory_percent))
        
        # Métricas de requisições
        recent_requests = [
            req for req in self.request_history
            if req.end_time and req.end_time > time.time() - 60  # Último minuto
        ]
        
        if recent_requests:
            avg_response_time = statistics.mean([req.duration for req in recent_requests])
            error_rate = sum(1 for req in recent_requests if not req.success) / len(recent_requests)
            
            self.system_metrics['response_time'].append((now, avg_response_time))
            self.system_metrics['error_rate'].append((now, error_rate))
    
    def _update_worker_health(self):
        """Atualiza status de saúde dos workers"""
        for worker_id, metrics in self.worker_pool.worker_metrics.items():
            # Verificar se worker está responsivo
            time_since_update = (datetime.now() - metrics.last_updated).total_seconds()
            
            if time_since_update > self.config.health_check_interval * 2:
                metrics.is_healthy = False
                print(f"⚠️ Worker {worker_id} marcado como não saudável")
            else:
                metrics.is_healthy = True
            
            # Atualizar métricas do sistema
            metrics.cpu_usage = psutil.cpu_percent()
            metrics.memory_usage = psutil.virtual_memory().percent
    
    def _check_scaling_conditions(self):
        """Verifica se precisa escalar workers"""
        now = datetime.now()
        
        # Verificar cooldown
        if now - self.last_scale_action < self.scale_cooldown:
            return
        
        # Calcular carga média
        healthy_workers = [
            metrics for metrics in self.worker_pool.worker_metrics.values()
            if metrics.is_healthy
        ]
        
        if not healthy_workers:
            return
        
        avg_load = statistics.mean([worker.load_score for worker in healthy_workers])
        active_workers = len(healthy_workers)
        
        # Decisão de scaling
        if avg_load > self.config.scale_up_threshold and active_workers < self.config.max_workers:
            self._scale_up()
        elif avg_load < self.config.scale_down_threshold and active_workers > self.config.min_workers:
            self._scale_down()
    
    def _scale_up(self):
        """Adiciona mais workers"""
        new_worker_id = f"worker_{len(self.worker_pool.workers)}"
        self.worker_pool._add_worker(new_worker_id)
        self.last_scale_action = datetime.now()
        print(f"📈 Scale up: {len(self.worker_pool.workers)} workers ativos")
    
    def _scale_down(self):
        """Remove workers ociosos"""
        # Encontrar worker com menor carga
        worker_loads = [
            (worker_id, metrics.load_score)
            for worker_id, metrics in self.worker_pool.worker_metrics.items()
            if metrics.is_healthy and metrics.active_requests == 0
        ]
        
        if worker_loads:
            worker_to_remove = min(worker_loads, key=lambda x: x[1])[0]
            self.worker_pool._remove_worker(worker_to_remove)
            self.last_scale_action = datetime.now()
            print(f"📉 Scale down: {len(self.worker_pool.workers)} workers ativos")
    
    def get_load_balancer_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas do load balancer"""
        with self.lock:
            recent_requests = [
                req for req in self.request_history
                if req.end_time and req.end_time > time.time() - 300  # Últimos 5 minutos
            ]
            
            stats = {
                'active_workers': len([w for w in self.worker_pool.worker_metrics.values() if w.is_healthy]),
                'total_workers': len(self.worker_pool.workers),
                'active_requests': len(self.active_requests),
                'total_requests_processed': len(self.request_history),
                'recent_requests_5min': len(recent_requests),
                'avg_response_time_5min': 0.0,
                'error_rate_5min': 0.0,
                'system_cpu': psutil.cpu_percent(),
                'system_memory': psutil.virtual_memory().percent,
                'worker_details': []
            }
            
            if recent_requests:
                stats['avg_response_time_5min'] = statistics.mean([req.duration for req in recent_requests])
                stats['error_rate_5min'] = sum(1 for req in recent_requests if not req.success) / len(recent_requests)
            
            # Detalhes dos workers
            for worker_id, metrics in self.worker_pool.worker_metrics.items():
                stats['worker_details'].append({
                    'worker_id': worker_id,
                    'load_score': round(metrics.load_score, 2),
                    'active_requests': metrics.active_requests,
                    'total_requests': metrics.total_requests,
                    'avg_response_time': round(metrics.avg_response_time, 3),
                    'is_healthy': metrics.is_healthy
                })
            
            return stats
    
    def shutdown(self):
        """Encerra o load balancer"""
        self.monitoring_active = False
        
        # Aguardar thread de monitoramento
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        # Encerrar todos os workers
        for worker_id in list(self.worker_pool.workers.keys()):
            self.worker_pool._remove_worker(worker_id)
        
        print("🛑 Load balancer encerrado")


class LoadBalancerManager:
    """
    Gerenciador de múltiplos load balancers especializados
    """
    
    def __init__(self):
        self.load_balancers: Dict[str, AutoLoadBalancer] = {}
        self.global_stats = {
            'total_requests': 0,
            'total_errors': 0,
            'avg_response_time': 0.0
        }
    
    def create_load_balancer(self, name: str, worker_function: Callable, config: LoadBalancerConfig = None) -> AutoLoadBalancer:
        """Cria um novo load balancer"""
        lb = AutoLoadBalancer(worker_function, config)
        self.load_balancers[name] = lb
        print(f"🔧 Load balancer '{name}' criado")
        return lb
    
    def get_load_balancer(self, name: str) -> Optional[AutoLoadBalancer]:
        """Obtém load balancer por nome"""
        return self.load_balancers.get(name)
    
    def get_global_stats(self) -> Dict[str, Any]:
        """Obtém estatísticas globais de todos os load balancers"""
        global_stats = {
            'load_balancers': {},
            'total_active_workers': 0,
            'total_active_requests': 0,
            'system_metrics': {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent
            }
        }
        
        for name, lb in self.load_balancers.items():
            lb_stats = lb.get_load_balancer_stats()
            global_stats['load_balancers'][name] = lb_stats
            global_stats['total_active_workers'] += lb_stats['active_workers']
            global_stats['total_active_requests'] += lb_stats['active_requests']
        
        return global_stats
    
    def shutdown_all(self):
        """Encerra todos os load balancers"""
        for name, lb in self.load_balancers.items():
            lb.shutdown()
            print(f"🛑 Load balancer '{name}' encerrado")
        
        self.load_balancers.clear()


# Instância global do gerenciador
load_balancer_manager = LoadBalancerManager()
