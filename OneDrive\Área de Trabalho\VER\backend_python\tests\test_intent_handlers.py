"""
Testes unitários para os handlers de intenção
"""

import unittest
import asyncio
import sys
import os
from unittest.mock import Mock, patch, AsyncMock

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from handlers.intent_handlers import IntentHandlers, create_intent_handler
from config.persona import RESPONSE_TEMPLATES


class TestIntentHandlers(unittest.TestCase):
    """
    Testes para os handlers de intenção
    """
    
    def setUp(self):
        """Configurar testes"""
        # Mock do cliente Supabase
        self.mock_supabase = Mock()
        self.handler = IntentHandlers(self.mock_supabase)
    
    def test_create_intent_handler(self):
        """Testa a criação do handler"""
        handler = create_intent_handler(self.mock_supabase)
        self.assertIsInstance(handler, IntentHandlers)
        self.assertEqual(handler.supabase, self.mock_supabase)
    
    def test_handle_template_query_success(self):
        """Testa o handler de template com sucesso"""
        # Teste com saudação
        response = self.handler.handle_template_query("Bom dia!")
        self.assertIsNotNone(response)
        self.assertIsInstance(response, str)
        self.assertGreater(len(response), 10)
        
        # Teste com assistência social
        response2 = self.handler.handle_template_query("Preciso de cesta básica")
        self.assertIsNotNone(response2)
        self.assertIn("CRAS", response2)
        
        # Teste com medicamento
        response3 = self.handler.handle_template_query("Preciso de remédio")
        self.assertIsNotNone(response3)
        self.assertIn("UBS", response3)
    
    def test_handle_template_query_no_match(self):
        """Testa o handler de template sem correspondência"""
        response = self.handler.handle_template_query("Pergunta muito específica sem template")
        self.assertIsNone(response)
    
    def test_handle_template_query_case_insensitive(self):
        """Testa se o handler é case insensitive"""
        # Teste com diferentes casos
        response1 = self.handler.handle_template_query("BOM DIA")
        response2 = self.handler.handle_template_query("bom dia")
        response3 = self.handler.handle_template_query("Bom Dia")
        
        # Todos devem retornar uma resposta
        self.assertIsNotNone(response1)
        self.assertIsNotNone(response2)
        self.assertIsNotNone(response3)
    
    @patch('handlers.intent_handlers.generate_rag_response')
    def test_handle_rag_query_success(self, mock_generate_rag):
        """Testa o handler RAG com sucesso"""
        # Mock da busca de documentos
        self.handler._search_documents = AsyncMock(return_value="Contexto dos documentos")
        
        # Mock da geração de resposta
        mock_generate_rag.return_value = "Resposta baseada em documentos"
        
        # Executar teste
        async def run_test():
            response = await self.handler.handle_rag_query("Pergunta sobre documentos", [])
            self.assertIsNotNone(response)
            self.assertIsInstance(response, str)
            return response
        
        response = asyncio.run(run_test())
        self.assertIsNotNone(response)
    
    @patch('handlers.intent_handlers.generate_rag_response')
    def test_handle_rag_query_no_documents(self, mock_generate_rag):
        """Testa o handler RAG sem documentos"""
        # Mock sem documentos encontrados
        self.handler._search_documents = AsyncMock(return_value="")
        
        async def run_test():
            response = await self.handler.handle_rag_query("Pergunta sem documentos", [])
            self.assertIsNone(response)
        
        asyncio.run(run_test())
    
    @patch('handlers.intent_handlers.generate_general_response')
    def test_handle_general_conversation(self, mock_generate_general):
        """Testa o handler de conversa geral"""
        # Mock da resposta
        mock_generate_general.return_value = "Resposta de conversa geral"
        
        async def run_test():
            response = await self.handler.handle_general_conversation("Como vai?", [])
            self.assertIsNotNone(response)
            self.assertIsInstance(response, str)
        
        asyncio.run(run_test())
    
    def test_handle_human_escalation_with_conversation_id(self):
        """Testa escalação para humano com ID da conversa"""
        # Mock do Supabase
        mock_table = Mock()
        mock_update = Mock()
        mock_eq = Mock()
        mock_execute = Mock()
        
        mock_table.update.return_value = mock_update
        mock_update.eq.return_value = mock_eq
        mock_eq.execute.return_value = mock_execute
        
        self.mock_supabase.table.return_value = mock_table
        
        async def run_test():
            response = await self.handler.handle_human_escalation("conv_123")
            self.assertIsNotNone(response)
            self.assertIsInstance(response, str)
            self.assertIn("equipe", response.lower())
            
            # Verificar se foi chamado corretamente
            self.mock_supabase.table.assert_called_with('conversations')
            mock_table.update.assert_called_with({'requires_human_attention': True})
        
        asyncio.run(run_test())
    
    def test_handle_human_escalation_without_conversation_id(self):
        """Testa escalação para humano sem ID da conversa"""
        async def run_test():
            response = await self.handler.handle_human_escalation(None)
            self.assertIsNotNone(response)
            self.assertIsInstance(response, str)
        
        asyncio.run(run_test())
    
    def test_handle_emergency(self):
        """Testa o handler de emergência"""
        # Mock do Supabase
        mock_table = Mock()
        mock_update = Mock()
        mock_eq = Mock()
        mock_execute = Mock()
        
        mock_table.update.return_value = mock_update
        mock_update.eq.return_value = mock_eq
        mock_eq.execute.return_value = mock_execute
        
        self.mock_supabase.table.return_value = mock_table
        
        async def run_test():
            response = await self.handler.handle_emergency("Emergência médica!", "conv_123")
            self.assertIsNotNone(response)
            self.assertIsInstance(response, str)
            self.assertIn("SAMU", response)
            
            # Verificar se foi marcado como emergência
            expected_update = {
                'requires_human_attention': True,
                'priority': 'high',
                'tags': ['emergencia']
            }
            mock_table.update.assert_called_with(expected_update)
        
        asyncio.run(run_test())
    
    def test_get_fallback_response(self):
        """Testa a resposta de fallback"""
        response = self.handler.get_fallback_response()
        self.assertIsNotNone(response)
        self.assertIsInstance(response, str)
        self.assertGreater(len(response), 20)
    
    @patch('handlers.intent_handlers.GoogleGenerativeAIEmbeddings')
    def test_search_documents_success(self, mock_embeddings_class):
        """Testa a busca de documentos com sucesso"""
        # Mock do modelo de embeddings
        mock_embeddings = Mock()
        mock_embeddings.embed_query.return_value = [0.1, 0.2, 0.3]
        mock_embeddings_class.return_value = mock_embeddings
        
        # Mock do Supabase RPC
        mock_rpc = Mock()
        mock_execute = Mock()
        mock_execute.data = [
            {'content': 'Conteúdo do documento 1', 'similarity': 0.9},
            {'content': 'Conteúdo do documento 2', 'similarity': 0.8}
        ]
        mock_rpc.execute.return_value = mock_execute
        self.mock_supabase.rpc.return_value = mock_rpc
        
        async def run_test():
            context = await self.handler._search_documents("query de teste")
            self.assertIsInstance(context, str)
            self.assertIn("Conteúdo do documento 1", context)
            self.assertIn("Conteúdo do documento 2", context)
        
        asyncio.run(run_test())
    
    @patch('handlers.intent_handlers.GoogleGenerativeAIEmbeddings')
    def test_search_documents_no_results(self, mock_embeddings_class):
        """Testa a busca de documentos sem resultados"""
        # Mock do modelo de embeddings
        mock_embeddings = Mock()
        mock_embeddings.embed_query.return_value = [0.1, 0.2, 0.3]
        mock_embeddings_class.return_value = mock_embeddings
        
        # Mock do Supabase RPC sem resultados
        mock_rpc = Mock()
        mock_execute = Mock()
        mock_execute.data = []
        mock_rpc.execute.return_value = mock_execute
        self.mock_supabase.rpc.return_value = mock_rpc
        
        async def run_test():
            context = await self.handler._search_documents("query sem resultados")
            self.assertEqual(context, "")
        
        asyncio.run(run_test())
    
    def test_template_coverage(self):
        """Testa se todos os templates estão sendo cobertos"""
        # Verificar se o handler tem acesso a todos os templates
        self.assertEqual(self.handler.templates, RESPONSE_TEMPLATES)
        
        # Testar alguns templates específicos
        test_cases = [
            ("oi", "saudacao_geral"),
            ("cesta básica", "assistencia_social"),
            ("remédio", "medicamentos"),
            ("emprego", "pedido_emprego"),
            ("cirurgia", "cirurgias_exames"),
            ("dentista", "tratamentos_dentarios"),
            ("amém", "mensagem_fe"),
            ("agenda", "encontro_presencial")
        ]
        
        for query, expected_template in test_cases:
            response = self.handler.handle_template_query(query)
            self.assertIsNotNone(response, f"Template {expected_template} não funcionou para '{query}'")


if __name__ == "__main__":
    unittest.main()
