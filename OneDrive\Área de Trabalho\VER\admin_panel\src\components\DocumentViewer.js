import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  <PERSON>alogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  Chip,
  Divider,
  Paper,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Close as CloseIcon,
  Description as DocumentIcon,
  PictureAsPdf as PdfIcon,
  TableChart as CsvIcon,
  TextFields as TextIcon,
  Download as DownloadIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import axiosInstance from '../api/axiosInstance';

const DocumentViewer = ({ open, onClose, document }) => {
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [contentLoaded, setContentLoaded] = useState(false);

  const getFileIcon = (fileType) => {
    switch (fileType?.toLowerCase()) {
      case 'pdf':
        return <PdfIcon sx={{ color: '#d32f2f' }} />;
      case 'csv':
        return <CsvIcon sx={{ color: '#2e7d32' }} />;
      case 'txt':
        return <TextIcon sx={{ color: '#1976d2' }} />;
      default:
        return <DocumentIcon sx={{ color: '#757575' }} />;
    }
  };

  const getFileTypeColor = (fileType) => {
    switch (fileType?.toLowerCase()) {
      case 'pdf':
        return 'error';
      case 'csv':
        return 'success';
      case 'txt':
        return 'primary';
      default:
        return 'default';
    }
  };

  const formatFileSize = (bytes) => {
    if (!bytes) return 'N/A';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm', { locale: ptBR });
    } catch {
      return 'Data inválida';
    }
  };

  const loadDocumentContent = async () => {
    if (!document?.id || contentLoaded) return;

    setLoading(true);
    setError('');
    
    try {
      const response = await axiosInstance.get(`/documents/${document.id}/content`);
      setContent(response.data.content || 'Conteúdo não disponível');
      setContentLoaded(true);
    } catch (err) {
      setError('Erro ao carregar conteúdo do documento');
      console.error('Erro ao carregar documento:', err);
    } finally {
      setLoading(false);
    }
  };

  const downloadDocument = async () => {
    if (!document?.id) return;

    try {
      const response = await axiosInstance.get(`/documents/${document.id}/download`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', document.filename || 'documento');
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Erro ao baixar documento:', err);
    }
  };

  const handleViewContent = () => {
    if (!contentLoaded) {
      loadDocumentContent();
    }
  };

  if (!document) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { 
          borderRadius: 3,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {getFileIcon(document.file_type)}
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {document.filename || 'Documento'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                <Chip
                  label={document.file_type?.toUpperCase() || 'UNKNOWN'}
                  color={getFileTypeColor(document.file_type)}
                  size="small"
                  variant="outlined"
                />
                <Typography variant="caption" color="text.secondary">
                  {formatFileSize(document.file_size)}
                </Typography>
              </Box>
            </Box>
          </Box>
          <IconButton onClick={onClose} sx={{ color: 'text.secondary' }}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ p: 3 }}>
        {/* Informações do Documento */}
        <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50', borderRadius: 2 }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Informações do Documento
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: 2 }}>
            <Box>
              <Typography variant="caption" color="text.secondary">
                Data de Upload
              </Typography>
              <Typography variant="body2">
                {formatDate(document.created_at)}
              </Typography>
            </Box>
            <Box>
              <Typography variant="caption" color="text.secondary">
                Tamanho do Arquivo
              </Typography>
              <Typography variant="body2">
                {formatFileSize(document.file_size)}
              </Typography>
            </Box>
            <Box>
              <Typography variant="caption" color="text.secondary">
                Tipo de Arquivo
              </Typography>
              <Typography variant="body2">
                {document.file_type?.toUpperCase() || 'N/A'}
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Conteúdo do Documento */}
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="subtitle2" color="text.secondary">
              Conteúdo do Documento
            </Typography>
            {!contentLoaded && (
              <Button
                startIcon={<ViewIcon />}
                onClick={handleViewContent}
                variant="outlined"
                size="small"
              >
                Visualizar Conteúdo
              </Button>
            )}
          </Box>

          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {contentLoaded && content && (
            <Paper
              sx={{
                p: 3,
                maxHeight: 500,
                overflow: 'auto',
                bgcolor: 'grey.50',
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'divider',
                boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.06)'
              }}
            >
              <Typography
                variant="body2"
                component="pre"
                sx={{
                  whiteSpace: 'pre-wrap',
                  fontFamily: '"Roboto Mono", "Courier New", monospace',
                  fontSize: '0.9rem',
                  lineHeight: 1.7,
                  color: 'text.primary',
                  '& strong': {
                    fontWeight: 600,
                    color: 'primary.main'
                  }
                }}
              >
                {content}
              </Typography>
            </Paper>
          )}

          {!contentLoaded && !loading && (
            <Box sx={{ textAlign: 'center', py: 4, color: 'text.secondary' }}>
              <DocumentIcon sx={{ fontSize: 48, mb: 1, opacity: 0.5 }} />
              <Typography variant="body2">
                Clique em "Visualizar Conteúdo" para carregar o documento
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button
          startIcon={<DownloadIcon />}
          onClick={downloadDocument}
          variant="outlined"
        >
          Download
        </Button>
        <Button onClick={onClose} variant="contained">
          Fechar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DocumentViewer;
