<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo: Sistema de Notificações e Mensagens</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .demo-section {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        
        .demo-section:last-child {
            border-bottom: none;
        }
        
        .demo-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-active { background-color: #4CAF50; }
        .status-partial { background-color: #FF9800; }
        .status-inactive { background-color: #F44336; }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #4CAF50;
        }
        
        .demo-card h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .demo-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .log-area {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .notification-demo {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            padding: 15px;
            max-width: 300px;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        
        .notification-demo.show {
            transform: translateX(0);
        }
        
        .notification-demo .title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .notification-demo .message {
            color: #666;
            font-size: 0.9em;
        }
        
        .websocket-status {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 Sistema de Notificações e Mensagens</h1>
            <p>Demonstração das Funcionalidades Implementadas</p>
            <div class="websocket-status">
                <span class="status-indicator" id="wsStatus"></span>
                <span id="wsStatusText">Conectando...</span>
            </div>
        </div>
        
        <!-- Seção 1: Notificações WebSocket -->
        <div class="demo-section">
            <div class="demo-title">
                <span class="status-indicator status-active"></span>
                🔔 Notificações Instantâneas via WebSocket
            </div>
            
            <div class="demo-grid">
                <div class="demo-card">
                    <h4>✅ Funcionalidades Ativas</h4>
                    <p>• Conexão WebSocket em tempo real<br>
                       • Notificações de novas mensagens<br>
                       • Notificações de respostas enviadas<br>
                       • Alertas do sistema<br>
                       • Broadcast para todos os clientes</p>
                </div>
                
                <div class="demo-card">
                    <h4>🧪 Testes Disponíveis</h4>
                    <button class="test-button" onclick="testNotification()">Enviar Notificação</button>
                    <button class="test-button" onclick="testBroadcast()">Testar Broadcast</button>
                    <button class="test-button" onclick="getWebSocketStats()">Ver Estatísticas</button>
                </div>
            </div>
            
            <div class="log-area" id="notificationLog">
                🔔 Log de notificações aparecerá aqui...
            </div>
        </div>
        
        <!-- Seção 2: Envio de Mensagens -->
        <div class="demo-section">
            <div class="demo-title">
                <span class="status-indicator status-partial"></span>
                📱 Sistema de Envio de Mensagens
            </div>
            
            <div class="demo-grid">
                <div class="demo-card">
                    <h4>✅ Implementado</h4>
                    <p>• Endpoint de envio funcionando<br>
                       • Sistema de filas assíncronas<br>
                       • Frontend integrado<br>
                       • Tratamento de erros<br>
                       • Notificações automáticas</p>
                </div>
                
                <div class="demo-card">
                    <h4>⚠️ Dependência Externa</h4>
                    <p>• WhatsApp (WPPConnect) não configurado<br>
                       • Sistema funciona em modo simulação<br>
                       • Todas as funcionalidades testáveis<br>
                       • Pronto para produção</p>
                </div>
            </div>
            
            <div class="demo-card">
                <h4>🧪 Testar Envio</h4>
                <input type="text" id="messageInput" placeholder="Digite uma mensagem de teste..." style="width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">
                <button class="test-button" onclick="testSendMessage()">Enviar Mensagem</button>
                <button class="test-button" onclick="getMessageStats()">Ver Estatísticas</button>
            </div>
            
            <div class="log-area" id="messageLog">
                📱 Log de mensagens aparecerá aqui...
            </div>
        </div>
        
        <!-- Seção 3: Integração Frontend -->
        <div class="demo-section">
            <div class="demo-title">
                <span class="status-indicator status-active"></span>
                🌐 Integração com Frontend
            </div>
            
            <div class="demo-grid">
                <div class="demo-card">
                    <h4>✅ Admin Panel Atualizado</h4>
                    <p>• WebSocket client automático<br>
                       • Centro de notificações<br>
                       • Envio pelas janelas individuais<br>
                       • Feedback visual em tempo real</p>
                </div>
                
                <div class="demo-card">
                    <h4>🚀 Como Usar</h4>
                    <p>1. Acesse: <a href="http://localhost:3000" target="_blank">http://localhost:3000</a><br>
                       2. Vá para uma conversa individual<br>
                       3. Digite e envie mensagens<br>
                       4. Veja notificações em tempo real</p>
                </div>
            </div>
        </div>
        
        <!-- Seção 4: Status do Sistema -->
        <div class="demo-section">
            <div class="demo-title">
                📊 Status do Sistema
            </div>
            
            <div class="demo-grid">
                <div class="demo-card">
                    <h4>🔔 WebSocket</h4>
                    <p id="wsStatsDisplay">Carregando...</p>
                    <button class="test-button" onclick="refreshStats()">Atualizar</button>
                </div>
                
                <div class="demo-card">
                    <h4>📱 WhatsApp</h4>
                    <p id="whatsappStatusDisplay">Carregando...</p>
                    <button class="test-button" onclick="checkWhatsAppStatus()">Verificar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Notificação Demo -->
    <div class="notification-demo" id="demoNotification">
        <div class="title" id="notificationTitle">Título da Notificação</div>
        <div class="message" id="notificationMessage">Mensagem da notificação</div>
    </div>
    
    <script>
        let ws = null;
        let wsConnected = false;
        
        // Conectar WebSocket
        function connectWebSocket() {
            try {
                ws = new WebSocket('ws://localhost:8000/ws');
                
                ws.onopen = function() {
                    wsConnected = true;
                    updateWSStatus('Conectado', 'status-active');
                    logMessage('notificationLog', '✅ WebSocket conectado com sucesso!');
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    logMessage('notificationLog', `📨 Recebido: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.type === 'notification') {
                        showNotification(data.notification.title, data.notification.message);
                    }
                };
                
                ws.onclose = function() {
                    wsConnected = false;
                    updateWSStatus('Desconectado', 'status-inactive');
                    logMessage('notificationLog', '❌ WebSocket desconectado');
                };
                
                ws.onerror = function(error) {
                    updateWSStatus('Erro', 'status-inactive');
                    logMessage('notificationLog', `❌ Erro WebSocket: ${error}`);
                };
            } catch (error) {
                updateWSStatus('Erro', 'status-inactive');
                logMessage('notificationLog', `❌ Erro ao conectar: ${error}`);
            }
        }
        
        function updateWSStatus(text, statusClass) {
            document.getElementById('wsStatusText').textContent = text;
            const indicator = document.getElementById('wsStatus');
            indicator.className = 'status-indicator ' + statusClass;
        }
        
        function logMessage(logId, message) {
            const log = document.getElementById(logId);
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function showNotification(title, message) {
            const notification = document.getElementById('demoNotification');
            document.getElementById('notificationTitle').textContent = title;
            document.getElementById('notificationMessage').textContent = message;
            
            notification.classList.add('show');
            setTimeout(() => {
                notification.classList.remove('show');
            }, 4000);
        }
        
        // Funções de teste
        async function testNotification() {
            try {
                const response = await fetch('http://localhost:8000/ws/notify', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        title: 'Teste de Notificação',
                        message: 'Esta é uma notificação de teste do sistema!',
                        priority: 'normal'
                    })
                });
                
                const result = await response.json();
                logMessage('notificationLog', `✅ Notificação enviada: ${JSON.stringify(result)}`);
            } catch (error) {
                logMessage('notificationLog', `❌ Erro: ${error}`);
            }
        }
        
        async function testBroadcast() {
            try {
                const response = await fetch('http://localhost:8000/ws/broadcast', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        type: 'demo_broadcast',
                        title: 'Broadcast de Teste',
                        message: 'Esta é uma mensagem de broadcast!',
                        timestamp: new Date().toISOString()
                    })
                });
                
                const result = await response.json();
                logMessage('notificationLog', `📢 Broadcast enviado: ${JSON.stringify(result)}`);
            } catch (error) {
                logMessage('notificationLog', `❌ Erro: ${error}`);
            }
        }
        
        async function getWebSocketStats() {
            try {
                const response = await fetch('http://localhost:8000/ws/stats');
                const stats = await response.json();
                logMessage('notificationLog', `📊 Estatísticas: ${JSON.stringify(stats, null, 2)}`);
                document.getElementById('wsStatsDisplay').innerHTML = 
                    `Conexões ativas: ${stats.active_connections}<br>
                     Usuários conectados: ${stats.connected_users}<br>
                     Total de conexões: ${stats.total_connections}<br>
                     Mensagens enviadas: ${stats.total_messages_sent}<br>
                     Notificações enviadas: ${stats.total_notifications_sent}`;
            } catch (error) {
                logMessage('notificationLog', `❌ Erro: ${error}`);
            }
        }
        
        async function testSendMessage() {
            const message = document.getElementById('messageInput').value || 'Mensagem de teste do sistema!';
            
            try {
                const response = await fetch('http://localhost:8000/send-message', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        contact_id: '5511999999999',
                        contact_name: 'Usuário Demo',
                        message: message,
                        conversation_id: 'demo_conv_123',
                        channel: 'whatsapp'
                    })
                });
                
                const result = await response.json();
                logMessage('messageLog', `📤 Mensagem enviada: ${JSON.stringify(result)}`);
            } catch (error) {
                logMessage('messageLog', `❌ Erro: ${error}`);
            }
        }
        
        async function getMessageStats() {
            try {
                const response = await fetch('http://localhost:8000/messages/stats');
                const stats = await response.json();
                logMessage('messageLog', `📊 Estatísticas: ${JSON.stringify(stats, null, 2)}`);
            } catch (error) {
                logMessage('messageLog', `❌ Erro: ${error}`);
            }
        }
        
        async function checkWhatsAppStatus() {
            try {
                const response = await fetch('http://localhost:8000/whatsapp/status');
                const status = await response.json();
                document.getElementById('whatsappStatusDisplay').innerHTML = 
                    `Status: ${status.status}<br>
                     Conectado: ${status.connected ? 'Sim' : 'Não'}<br>
                     Timestamp: ${new Date(status.timestamp).toLocaleString()}`;
            } catch (error) {
                document.getElementById('whatsappStatusDisplay').innerHTML = `Erro: ${error}`;
            }
        }
        
        function refreshStats() {
            getWebSocketStats();
            checkWhatsAppStatus();
        }
        
        // Inicializar
        window.onload = function() {
            connectWebSocket();
            refreshStats();
            
            // Atualizar estatísticas a cada 10 segundos
            setInterval(refreshStats, 10000);
        };
    </script>
</body>
</html>
