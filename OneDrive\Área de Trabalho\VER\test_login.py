#!/usr/bin/env python3
"""
Script para testar login e criar usu<PERSON>rio admin se necessário.
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_login(username, password):
    """Testa login com usuário e senha."""
    try:
        response = requests.post(
            f"{BASE_URL}/token",
            data={"username": username, "password": password},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Login bem-sucedido!")
            print(f"   Token: {data['access_token'][:50]}...")
            return data['access_token']
        else:
            print(f"❌ Erro no login: {response.status_code}")
            print(f"   Resposta: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Erro na requisição: {e}")
        return None

def test_endpoints_with_token(token):
    """Testa endpoints protegidos com token."""
    headers = {"Authorization": f"Bearer {token}"}
    
    # Testar status do WhatsApp
    try:
        response = requests.get(f"{BASE_URL}/whatsapp/status", headers=headers)
        print(f"📊 Status WhatsApp: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Erro ao testar status: {e}")
    
    # Testar conversas do WhatsApp
    try:
        response = requests.get(f"{BASE_URL}/whatsapp/conversations", headers=headers)
        print(f"💬 Conversas WhatsApp: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Encontradas {len(data)} conversas")
        else:
            print(f"   Erro: {response.text}")
    except Exception as e:
        print(f"❌ Erro ao testar conversas: {e}")

def main():
    print("🔐 Testando autenticação...")
    
    # Tentar login com admin
    token = test_login("admin", "admin123")
    
    if token:
        print("\n🧪 Testando endpoints protegidos...")
        test_endpoints_with_token(token)
    else:
        print("\n❌ Não foi possível fazer login")
        print("   Verifique se o usuário admin existe e a senha está correta")

if __name__ == "__main__":
    main()
