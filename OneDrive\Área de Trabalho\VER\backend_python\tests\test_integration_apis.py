"""
Testes de integração com APIs externas (usando mocks)
"""

import unittest
import asyncio
import sys
import os
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from handlers.intent_handlers import IntentHandlers
from pipelines.chat_pipeline import ChatPipeline


class TestAPIIntegration(unittest.TestCase):
    """
    Testes de integração com APIs externas
    """
    
    def setUp(self):
        """Configurar testes de API"""
        self.mock_supabase = Mock()
        self.handler = IntentHandlers(self.mock_supabase)
        self.api_results = {}
    
    @patch('handlers.intent_handlers.GoogleGenerativeAIEmbeddings')
    def test_supabase_integration(self, mock_embeddings):
        """Testa integração com Supabase"""
        print("   🔗 Testando integração com Supabase...")
        
        # Mock do modelo de embeddings
        mock_embeddings_instance = Mock()
        mock_embeddings_instance.embed_query.return_value = [0.1, 0.2, 0.3, 0.4, 0.5]
        mock_embeddings.return_value = mock_embeddings_instance
        
        # Cenários de teste
        test_scenarios = [
            {
                "name": "busca_com_resultados",
                "query": "programas sociais",
                "mock_response": [
                    {"content": "Programa Auxílio Família", "similarity": 0.9},
                    {"content": "Programa Cesta Básica", "similarity": 0.8}
                ],
                "expected_context": True
            },
            {
                "name": "busca_sem_resultados", 
                "query": "assunto inexistente",
                "mock_response": [],
                "expected_context": False
            },
            {
                "name": "busca_com_baixa_similaridade",
                "query": "tópico irrelevante",
                "mock_response": [
                    {"content": "Conteúdo pouco relevante", "similarity": 0.3}
                ],
                "expected_context": False  # Abaixo do threshold
            }
        ]
        
        successful_integrations = 0
        
        for scenario in test_scenarios:
            try:
                # Configurar mock do Supabase
                mock_rpc = Mock()
                mock_execute = Mock()
                mock_execute.data = scenario["mock_response"]
                mock_rpc.execute.return_value = mock_execute
                self.mock_supabase.rpc.return_value = mock_rpc
                
                # Executar busca
                async def run_search():
                    context = await self.handler._search_documents(scenario["query"])
                    return context
                
                context = asyncio.run(run_search())
                
                # Validar resultado
                if scenario["expected_context"]:
                    self.assertIsInstance(context, str)
                    self.assertGreater(len(context), 0)
                    # Verificar se contém conteúdo dos documentos
                    for doc in scenario["mock_response"]:
                        if doc["similarity"] >= 0.7:  # Threshold
                            self.assertIn(doc["content"], context)
                else:
                    self.assertEqual(context, "")
                
                # Verificar chamadas corretas
                self.mock_supabase.rpc.assert_called_with(
                    'match_chunks',
                    {
                        'query_embedding': [0.1, 0.2, 0.3, 0.4, 0.5],
                        'match_threshold': 0.7,
                        'match_count': 5
                    }
                )
                
                successful_integrations += 1
                print(f"      ✅ {scenario['name']}: integração bem-sucedida")
                
            except Exception as e:
                print(f"      ❌ {scenario['name']}: falha - {e}")
        
        integration_rate = successful_integrations / len(test_scenarios)
        
        self.api_results['supabase_integration'] = {
            'total_scenarios': len(test_scenarios),
            'successful_integrations': successful_integrations,
            'integration_rate': integration_rate
        }
        
        self.assertGreater(integration_rate, 0.8, "Integração com Supabase falhando")
        
        print(f"   🎉 Supabase: {integration_rate:.1%} ({successful_integrations}/{len(test_scenarios)})")
    
    def test_conversation_database_integration(self):
        """Testa integração com banco de conversas"""
        print("   🔗 Testando integração com banco de conversas...")
        
        # Cenários de operações no banco
        database_operations = [
            {
                "operation": "create_conversation",
                "data": {
                    "contact_id": "user123",
                    "contact_name": "João Silva",
                    "created_at": "2024-01-01T10:00:00Z"
                }
            },
            {
                "operation": "save_message",
                "data": {
                    "conversation_id": "conv123",
                    "sender": "user",
                    "content": "Bom dia!",
                    "created_at": "2024-01-01T10:01:00Z"
                }
            },
            {
                "operation": "update_conversation",
                "data": {
                    "conversation_id": "conv123",
                    "last_message_at": "2024-01-01T10:01:00Z"
                }
            },
            {
                "operation": "mark_human_attention",
                "data": {
                    "conversation_id": "conv123",
                    "requires_human_attention": True
                }
            }
        ]
        
        successful_operations = 0
        
        for operation in database_operations:
            try:
                op_name = operation["operation"]
                op_data = operation["data"]
                
                # Configurar mocks baseado na operação
                if op_name == "create_conversation":
                    mock_result = Mock()
                    mock_result.data = [{"id": "conv123"}]
                    
                    mock_insert = Mock()
                    mock_insert.execute.return_value = mock_result
                    
                    mock_table = Mock()
                    mock_table.insert.return_value = mock_insert
                    
                    self.mock_supabase.table.return_value = mock_table
                    
                    # Simular criação
                    result = self.mock_supabase.table('conversations').insert(op_data).execute()
                    
                    # Validar
                    self.assertIsNotNone(result.data)
                    self.assertEqual(result.data[0]["id"], "conv123")
                
                elif op_name == "save_message":
                    mock_insert = Mock()
                    mock_insert.execute.return_value = Mock()
                    
                    mock_table = Mock()
                    mock_table.insert.return_value = mock_insert
                    
                    self.mock_supabase.table.return_value = mock_table
                    
                    # Simular salvamento
                    self.mock_supabase.table('messages').insert(op_data).execute()
                    
                    # Verificar chamada
                    mock_table.insert.assert_called_with(op_data)
                
                elif op_name == "update_conversation":
                    mock_eq = Mock()
                    mock_eq.execute.return_value = Mock()
                    
                    mock_update = Mock()
                    mock_update.eq.return_value = mock_eq
                    
                    mock_table = Mock()
                    mock_table.update.return_value = mock_update
                    
                    self.mock_supabase.table.return_value = mock_table
                    
                    # Simular atualização
                    conv_id = op_data.pop("conversation_id")
                    self.mock_supabase.table('conversations').update(op_data).eq('id', conv_id).execute()
                    
                    # Verificar chamadas
                    mock_table.update.assert_called_with(op_data)
                    mock_update.eq.assert_called_with('id', conv_id)
                
                elif op_name == "mark_human_attention":
                    # Testar escalação para humano
                    async def run_escalation():
                        return await self.handler.handle_human_escalation(op_data["conversation_id"])
                    
                    response = asyncio.run(run_escalation())
                    
                    # Validar resposta
                    self.assertIsInstance(response, str)
                    self.assertIn("equipe", response.lower())
                
                successful_operations += 1
                print(f"      ✅ {op_name}: operação bem-sucedida")
                
            except Exception as e:
                print(f"      ❌ {op_name}: falha - {e}")
        
        operation_success_rate = successful_operations / len(database_operations)
        
        self.api_results['database_operations'] = {
            'total_operations': len(database_operations),
            'successful_operations': successful_operations,
            'success_rate': operation_success_rate
        }
        
        self.assertGreater(operation_success_rate, 0.8, "Operações de banco falhando")
        
        print(f"   🎉 Banco de dados: {operation_success_rate:.1%} ({successful_operations}/{len(database_operations)})")
    
    @patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI')
    def test_ai_model_integration(self, mock_chat_model):
        """Testa integração com modelos de IA"""
        print("   🔗 Testando integração com modelos de IA...")
        
        # Cenários de teste com IA
        ai_scenarios = [
            {
                "name": "intent_routing",
                "input": "Preciso de ajuda com cesta básica",
                "mock_response": "answer_with_template",
                "expected_intent": "answer_with_template"
            },
            {
                "name": "rag_response",
                "input": "Quais são os programas sociais disponíveis?",
                "mock_response": "Com base nos documentos, temos os seguintes programas: Auxílio Família, Cesta Básica e Bolsa Estudantil.",
                "expected_type": "informative"
            },
            {
                "name": "general_conversation",
                "input": "Como você está hoje?",
                "mock_response": "Estou bem, obrigada! Nossa equipe está sempre trabalhando para ajudar você.",
                "expected_type": "conversational"
            }
        ]
        
        successful_ai_calls = 0
        
        for scenario in ai_scenarios:
            try:
                # Configurar mock do modelo
                mock_response = Mock()
                mock_response.content = scenario["mock_response"]
                
                mock_model_instance = Mock()
                mock_model_instance.ainvoke = AsyncMock(return_value=mock_response)
                mock_chat_model.return_value = mock_model_instance
                
                # Criar pipeline com mock
                with patch('pipelines.chat_pipeline.ChatGoogleGenerativeAI', return_value=mock_model_instance):
                    pipeline = ChatPipeline()
                    
                    if scenario["name"] == "intent_routing":
                        # Testar roteamento de intenção
                        async def run_routing():
                            return await pipeline.route_intent(scenario["input"], [])
                        
                        result = asyncio.run(run_routing())
                        self.assertEqual(result, scenario["expected_intent"])
                    
                    elif scenario["name"] == "rag_response":
                        # Testar resposta RAG
                        async def run_rag():
                            return await pipeline.generate_rag_response(
                                scenario["input"],
                                "Contexto dos documentos",
                                []
                            )
                        
                        result = asyncio.run(run_rag())
                        self.assertIsInstance(result, str)
                        self.assertIn("programas", result.lower())
                    
                    elif scenario["name"] == "general_conversation":
                        # Testar conversa geral
                        async def run_general():
                            return await pipeline.generate_general_response(scenario["input"], [])
                        
                        result = asyncio.run(run_general())
                        self.assertIsInstance(result, str)
                        self.assertIn("equipe", result.lower())
                
                # Verificar se o modelo foi chamado
                mock_model_instance.ainvoke.assert_called()
                
                successful_ai_calls += 1
                print(f"      ✅ {scenario['name']}: IA respondeu corretamente")
                
            except Exception as e:
                print(f"      ❌ {scenario['name']}: falha - {e}")
        
        ai_success_rate = successful_ai_calls / len(ai_scenarios)
        
        self.api_results['ai_integration'] = {
            'total_scenarios': len(ai_scenarios),
            'successful_calls': successful_ai_calls,
            'success_rate': ai_success_rate
        }
        
        self.assertGreater(ai_success_rate, 0.8, "Integração com IA falhando")
        
        print(f"   🎉 Modelos de IA: {ai_success_rate:.1%} ({successful_ai_calls}/{len(ai_scenarios)})")
    
    def test_websocket_integration(self):
        """Testa integração com WebSocket (mock)"""
        print("   🔗 Testando integração com WebSocket...")
        
        # Simular notificações WebSocket
        websocket_events = [
            {
                "event": "new_message",
                "data": {
                    "contact_name": "João Silva",
                    "message": "Bom dia!",
                    "timestamp": "2024-01-01T10:00:00Z"
                }
            },
            {
                "event": "ai_response",
                "data": {
                    "contact_name": "João Silva", 
                    "response": "Oi João! Como posso ajudar você hoje?",
                    "timestamp": "2024-01-01T10:00:30Z"
                }
            },
            {
                "event": "human_escalation",
                "data": {
                    "conversation_id": "conv123",
                    "reason": "Solicitação complexa",
                    "timestamp": "2024-01-01T10:05:00Z"
                }
            }
        ]
        
        successful_notifications = 0
        
        # Mock da função de notificação WebSocket
        with patch('app.notify_new_message') as mock_notify:
            mock_notify.return_value = AsyncMock()
            
            for event in websocket_events:
                try:
                    event_type = event["event"]
                    event_data = event["data"]
                    
                    if event_type == "new_message":
                        # Simular notificação de nova mensagem
                        async def run_notification():
                            await mock_notify(event_data["contact_name"], event_data["message"])
                        
                        asyncio.run(run_notification())
                        
                        # Verificar se foi chamado corretamente
                        mock_notify.assert_called_with(event_data["contact_name"], event_data["message"])
                    
                    elif event_type == "ai_response":
                        # Simular notificação de resposta da IA
                        async def run_ai_notification():
                            await mock_notify(event_data["contact_name"], event_data["response"])
                        
                        asyncio.run(run_ai_notification())
                    
                    elif event_type == "human_escalation":
                        # Simular notificação de escalação
                        # (Normalmente seria uma notificação diferente)
                        self.assertIn("conversation_id", event_data)
                        self.assertIn("reason", event_data)
                    
                    successful_notifications += 1
                    print(f"      ✅ {event_type}: notificação enviada")
                    
                except Exception as e:
                    print(f"      ❌ {event_type}: falha - {e}")
        
        notification_success_rate = successful_notifications / len(websocket_events)
        
        self.api_results['websocket_integration'] = {
            'total_events': len(websocket_events),
            'successful_notifications': successful_notifications,
            'success_rate': notification_success_rate
        }
        
        self.assertGreater(notification_success_rate, 0.8, "Notificações WebSocket falhando")
        
        print(f"   🎉 WebSocket: {notification_success_rate:.1%} ({successful_notifications}/{len(websocket_events)})")
    
    def test_error_recovery_integration(self):
        """Testa recuperação de erros em integrações"""
        print("   🔗 Testando recuperação de erros...")
        
        # Cenários de erro
        error_scenarios = [
            {
                "name": "supabase_timeout",
                "error_type": "TimeoutError",
                "component": "database"
            },
            {
                "name": "ai_api_limit",
                "error_type": "RateLimitError", 
                "component": "ai_model"
            },
            {
                "name": "network_error",
                "error_type": "ConnectionError",
                "component": "network"
            }
        ]
        
        recovered_errors = 0
        
        for scenario in error_scenarios:
            try:
                error_name = scenario["name"]
                component = scenario["component"]
                
                if component == "database":
                    # Simular erro de banco e recuperação
                    self.mock_supabase.rpc.side_effect = Exception("Database timeout")
                    
                    # Testar se o sistema se recupera graciosamente
                    async def run_with_db_error():
                        try:
                            await self.handler._search_documents("test query")
                            return "fallback_used"
                        except:
                            return "error_handled"
                    
                    result = asyncio.run(run_with_db_error())
                    self.assertIn(result, ["fallback_used", "error_handled"])
                
                elif component == "ai_model":
                    # Simular erro de IA e fallback
                    fallback_response = self.handler.get_fallback_response()
                    self.assertIsInstance(fallback_response, str)
                    self.assertGreater(len(fallback_response), 10)
                
                elif component == "network":
                    # Simular erro de rede
                    # Sistema deve continuar funcionando com templates
                    template_response = self.handler.handle_template_query("Bom dia")
                    self.assertIsNotNone(template_response)
                
                recovered_errors += 1
                print(f"      ✅ {error_name}: recuperação bem-sucedida")
                
            except Exception as e:
                print(f"      ❌ {error_name}: falha na recuperação - {e}")
            
            # Resetar mocks
            self.mock_supabase.reset_mock()
        
        recovery_rate = recovered_errors / len(error_scenarios)
        
        self.api_results['error_recovery'] = {
            'total_scenarios': len(error_scenarios),
            'recovered_errors': recovered_errors,
            'recovery_rate': recovery_rate
        }
        
        self.assertGreater(recovery_rate, 0.6, "Recuperação de erros insuficiente")
        
        print(f"   🎉 Recuperação de erros: {recovery_rate:.1%} ({recovered_errors}/{len(error_scenarios)})")
    
    def get_api_results(self):
        """Retorna resultados dos testes de API"""
        return self.api_results


if __name__ == "__main__":
    unittest.main()
