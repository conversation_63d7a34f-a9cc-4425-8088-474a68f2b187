import React from 'react';
import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import App from './App';
import useAuthStore from './store/authStore';

// Mock all the page components
jest.mock('./pages/LoginPage', () => {
  return function MockLoginPage() {
    return <div data-testid="login-page">Login Page</div>;
  };
});

jest.mock('./pages/DashboardPage', () => {
  return function MockDashboardPage() {
    return <div data-testid="dashboard-page">Dashboard Page</div>;
  };
});

jest.mock('./pages/DocumentManagementPage', () => {
  return function MockDocumentManagementPage() {
    return <div data-testid="documents-page">Documents Page</div>;
  };
});

jest.mock('./pages/ChatPage', () => {
  return function MockChatPage() {
    return <div data-testid="chat-page">Chat Page</div>;
  };
});

jest.mock('./pages/WhatsAppPage', () => {
  return function MockWhatsAppPage() {
    return <div data-testid="whatsapp-page">WhatsApp Page</div>;
  };
});

jest.mock('./pages/HistoryPage', () => {
  return function MockHistoryPage() {
    return <div data-testid="history-page">History Page</div>;
  };
});

jest.mock('./pages/InboxPage', () => {
  return function MockInboxPage() {
    return <div data-testid="inbox-page">Inbox Page</div>;
  };
});

jest.mock('./pages/ConversationDetailPage', () => {
  return function MockConversationDetailPage() {
    return <div data-testid="conversation-detail-page">Conversation Detail Page</div>;
  };
});

jest.mock('./pages/ConversationReplyPage', () => {
  return function MockConversationReplyPage() {
    return <div data-testid="conversation-reply-page">Conversation Reply Page</div>;
  };
});

jest.mock('./components/layout/DashboardLayout', () => {
  return function MockDashboardLayout({ children }) {
    return <div data-testid="dashboard-layout">{children}</div>;
  };
});

// Mock the auth store
jest.mock('./store/authStore');

describe('App', () => {
  const renderApp = (initialEntries = ['/']) => {
    return render(
      <MemoryRouter initialEntries={initialEntries}>
        <App />
      </MemoryRouter>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when user is not authenticated', () => {
    beforeEach(() => {
      useAuthStore.mockReturnValue({
        isAuthenticated: false,
      });
    });

    it('should redirect to login page when accessing root path', () => {
      renderApp(['/']);
      expect(screen.getByTestId('login-page')).toBeInTheDocument();
    });

    it('should redirect to login page when accessing protected routes', () => {
      renderApp(['/documents']);
      expect(screen.getByTestId('login-page')).toBeInTheDocument();
    });

    it('should show login page when accessing /login', () => {
      renderApp(['/login']);
      expect(screen.getByTestId('login-page')).toBeInTheDocument();
    });

    it('should redirect to login for unknown routes', () => {
      renderApp(['/unknown-route']);
      expect(screen.getByTestId('login-page')).toBeInTheDocument();
    });
  });

  describe('when user is authenticated', () => {
    beforeEach(() => {
      useAuthStore.mockReturnValue({
        isAuthenticated: true,
      });
    });

    it('should show dashboard page at root path', () => {
      renderApp(['/']);
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
      expect(screen.getByTestId('dashboard-page')).toBeInTheDocument();
    });

    it('should show documents page at /documents', () => {
      renderApp(['/documents']);
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
      expect(screen.getByTestId('documents-page')).toBeInTheDocument();
    });

    it('should show chat page at /chat', () => {
      renderApp(['/chat']);
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
      expect(screen.getByTestId('chat-page')).toBeInTheDocument();
    });

    it('should show whatsapp page at /whatsapp', () => {
      renderApp(['/whatsapp']);
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
      expect(screen.getByTestId('whatsapp-page')).toBeInTheDocument();
    });

    it('should show inbox page at /inbox', () => {
      renderApp(['/inbox']);
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
      expect(screen.getByTestId('inbox-page')).toBeInTheDocument();
    });

    it('should show history page at /history', () => {
      renderApp(['/history']);
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
      expect(screen.getByTestId('history-page')).toBeInTheDocument();
    });

    it('should show conversation reply page at /inbox/:conversationId', () => {
      renderApp(['/inbox/123']);
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
      expect(screen.getByTestId('conversation-reply-page')).toBeInTheDocument();
    });

    it('should show conversation detail page at /history/:conversationId', () => {
      renderApp(['/history/123']);
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
      expect(screen.getByTestId('conversation-detail-page')).toBeInTheDocument();
    });

    it('should redirect unknown routes to dashboard', () => {
      renderApp(['/unknown-route']);
      expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument();
      expect(screen.getByTestId('dashboard-page')).toBeInTheDocument();
    });
  });
});
