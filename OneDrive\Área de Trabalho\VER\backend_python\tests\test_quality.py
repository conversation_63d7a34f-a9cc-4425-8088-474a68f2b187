"""
Testes de qualidade e conformidade para o chat da persona
"""

import unittest
import sys
import os
import re
import ast
from typing import Dict, List, Any

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.persona import (
    SYSTEM_PROMPT, RESPONSE_TEMPLATES, EMOJIS, HASHTAGS, PERSONA_CONFIG,
    RAG_SYSTEM_PROMPT, GENERAL_CONVERSATION_PROMPT, INTENT_ROUTING_PROMPT
)
from pipelines.post_processing import PostProcessor
from handlers.intent_handlers import IntentHandlers


class TestQuality(unittest.TestCase):
    """
    Testes de qualidade e conformidade
    """
    
    def setUp(self):
        """Configurar testes de qualidade"""
        self.processor = PostProcessor()
        self.quality_results = {}
    
    def test_response_quality_metrics(self):
        """Testa métricas de qualidade das respostas"""
        quality_metrics = {
            'length_appropriate': 0,
            'has_persona_elements': 0,
            'grammatically_correct': 0,
            'contextually_relevant': 0,
            'emotionally_appropriate': 0
        }
        
        total_responses = len(RESPONSE_TEMPLATES)
        
        for template_key, template_data in RESPONSE_TEMPLATES.items():
            response = template_data["response"]
            keywords = template_data["keywords"]
            
            # Métrica 1: Comprimento apropriado (20-200 caracteres)
            if 20 <= len(response) <= 200:
                quality_metrics['length_appropriate'] += 1
            
            # Métrica 2: Elementos da persona
            persona_elements = ["você", "nossa", "juntos", "equipe", "ajudar", "conte comigo"]
            if any(element in response.lower() for element in persona_elements):
                quality_metrics['has_persona_elements'] += 1
            
            # Métrica 3: Correção gramatical básica
            if self._check_basic_grammar(response):
                quality_metrics['grammatically_correct'] += 1
            
            # Métrica 4: Relevância contextual
            if self._check_contextual_relevance(response, keywords):
                quality_metrics['contextually_relevant'] += 1
            
            # Métrica 5: Adequação emocional
            if self._check_emotional_appropriateness(response):
                quality_metrics['emotionally_appropriate'] += 1
        
        # Calcular percentuais
        quality_percentages = {
            metric: (count / total_responses) * 100
            for metric, count in quality_metrics.items()
        }
        
        self.quality_results['response_quality'] = {
            'metrics': quality_metrics,
            'percentages': quality_percentages,
            'total_responses': total_responses
        }
        
        # Verificar qualidade mínima
        for metric, percentage in quality_percentages.items():
            self.assertGreater(percentage, 80, f"Qualidade baixa em {metric}: {percentage:.1f}%")
        
        print(f"   📊 Qualidade das respostas:")
        for metric, percentage in quality_percentages.items():
            print(f"      {metric}: {percentage:.1f}%")
    
    def test_persona_consistency_metrics(self):
        """Testa métricas de consistência da persona"""
        consistency_metrics = {
            'tone_consistency': 0,
            'identity_presence': 0,
            'value_alignment': 0,
            'language_style': 0
        }
        
        all_texts = [SYSTEM_PROMPT, RAG_SYSTEM_PROMPT, GENERAL_CONVERSATION_PROMPT]
        all_texts.extend([data["response"] for data in RESPONSE_TEMPLATES.values()])
        
        for text in all_texts:
            # Métrica 1: Consistência de tom
            if self._check_tone_consistency(text):
                consistency_metrics['tone_consistency'] += 1
            
            # Métrica 2: Presença de identidade
            if self._check_identity_presence(text):
                consistency_metrics['identity_presence'] += 1
            
            # Métrica 3: Alinhamento com valores
            if self._check_value_alignment(text):
                consistency_metrics['value_alignment'] += 1
            
            # Métrica 4: Estilo de linguagem
            if self._check_language_style(text):
                consistency_metrics['language_style'] += 1
        
        total_texts = len(all_texts)
        consistency_percentages = {
            metric: (count / total_texts) * 100
            for metric, count in consistency_metrics.items()
        }
        
        self.quality_results['persona_consistency'] = {
            'metrics': consistency_metrics,
            'percentages': consistency_percentages,
            'total_texts': total_texts
        }
        
        print(f"   📊 Consistência da persona:")
        for metric, percentage in consistency_percentages.items():
            print(f"      {metric}: {percentage:.1f}%")
    
    def test_accessibility_compliance(self):
        """Testa conformidade com acessibilidade"""
        accessibility_metrics = {
            'simple_language': 0,
            'clear_instructions': 0,
            'inclusive_language': 0,
            'readable_length': 0
        }
        
        total_responses = len(RESPONSE_TEMPLATES)
        
        for template_data in RESPONSE_TEMPLATES.values():
            response = template_data["response"]
            
            # Métrica 1: Linguagem simples
            if self._check_simple_language(response):
                accessibility_metrics['simple_language'] += 1
            
            # Métrica 2: Instruções claras
            if self._check_clear_instructions(response):
                accessibility_metrics['clear_instructions'] += 1
            
            # Métrica 3: Linguagem inclusiva
            if self._check_inclusive_language(response):
                accessibility_metrics['inclusive_language'] += 1
            
            # Métrica 4: Comprimento legível
            if len(response) <= 150:  # Fácil de ler
                accessibility_metrics['readable_length'] += 1
        
        accessibility_percentages = {
            metric: (count / total_responses) * 100
            for metric, count in accessibility_metrics.items()
        }
        
        self.quality_results['accessibility'] = {
            'metrics': accessibility_metrics,
            'percentages': accessibility_percentages,
            'total_responses': total_responses
        }
        
        print(f"   📊 Acessibilidade:")
        for metric, percentage in accessibility_percentages.items():
            print(f"      {metric}: {percentage:.1f}%")
    
    def test_technical_compliance(self):
        """Testa conformidade técnica"""
        compliance_issues = []
        
        # Verificar estrutura dos templates
        for template_key, template_data in RESPONSE_TEMPLATES.items():
            if not isinstance(template_data, dict):
                compliance_issues.append(f"Template {template_key} não é dict")
            
            if "keywords" not in template_data:
                compliance_issues.append(f"Template {template_key} sem keywords")
            
            if "response" not in template_data:
                compliance_issues.append(f"Template {template_key} sem response")
            
            if not isinstance(template_data.get("keywords"), list):
                compliance_issues.append(f"Keywords de {template_key} não é lista")
            
            if not isinstance(template_data.get("response"), str):
                compliance_issues.append(f"Response de {template_key} não é string")
        
        # Verificar configurações
        required_configs = [
            "max_response_length", "emoji_probability", "hashtag_probability",
            "temperature", "max_history_messages"
        ]
        
        for config in required_configs:
            if config not in PERSONA_CONFIG:
                compliance_issues.append(f"Config {config} ausente")
        
        # Verificar emojis
        for i, emoji in enumerate(EMOJIS):
            if not isinstance(emoji, str):
                compliance_issues.append(f"Emoji {i} não é string")
            if len(emoji) == 0:
                compliance_issues.append(f"Emoji {i} vazio")
        
        # Verificar hashtags
        for i, hashtag in enumerate(HASHTAGS):
            if not isinstance(hashtag, str):
                compliance_issues.append(f"Hashtag {i} não é string")
            if not hashtag.startswith("#"):
                compliance_issues.append(f"Hashtag {i} não começa com #")
        
        compliance_score = max(0, 100 - len(compliance_issues) * 5)
        
        self.quality_results['technical_compliance'] = {
            'issues': compliance_issues,
            'score': compliance_score,
            'total_checks': 50  # Estimativa de verificações
        }
        
        # Verificar se não há problemas críticos
        self.assertLess(len(compliance_issues), 5, f"Muitos problemas de conformidade: {compliance_issues}")
        
        print(f"   📊 Conformidade técnica: {compliance_score:.0f}% ({len(compliance_issues)} issues)")
    
    def _check_basic_grammar(self, text: str) -> bool:
        """Verifica gramática básica"""
        # Verificações simples
        if not text.strip():
            return False
        
        # Deve começar com maiúscula
        if not text[0].isupper():
            return False
        
        # Deve terminar com pontuação
        if not text.rstrip().endswith(('.', '!', '?')):
            return False
        
        # Não deve ter espaços duplos
        if "  " in text:
            return False
        
        return True
    
    def _check_contextual_relevance(self, response: str, keywords: List[str]) -> bool:
        """Verifica relevância contextual"""
        response_lower = response.lower()
        
        # Pelo menos uma palavra-chave relacionada deve aparecer na resposta
        # ou a resposta deve ser contextualmente apropriada
        context_indicators = [
            "cras", "ubs", "equipe", "verificar", "ajudar", 
            "transparente", "agenda", "deus", "juntos"
        ]
        
        return any(indicator in response_lower for indicator in context_indicators)
    
    def _check_emotional_appropriateness(self, text: str) -> bool:
        """Verifica adequação emocional"""
        text_lower = text.lower()
        
        # Deve ter tom positivo/acolhedor
        positive_indicators = [
            "ajudar", "juntos", "equipe", "conte comigo", "vamos", 
            "nossa", "você", "sempre", "melhor"
        ]
        
        # Não deve ter tom negativo
        negative_indicators = [
            "não posso", "impossível", "nunca", "jamais", "infelizmente"
        ]
        
        has_positive = any(indicator in text_lower for indicator in positive_indicators)
        has_negative = any(indicator in text_lower for indicator in negative_indicators)
        
        return has_positive and not has_negative
    
    def _check_tone_consistency(self, text: str) -> bool:
        """Verifica consistência de tom"""
        text_lower = text.lower()
        
        # Tom da Vereadora Rafaela: acolhedor, próximo, esperançoso
        tone_indicators = [
            "você", "nossa", "juntos", "equipe", "ajudar", "sempre",
            "conte comigo", "vamos", "melhor", "força"
        ]
        
        return any(indicator in text_lower for indicator in tone_indicators)
    
    def _check_identity_presence(self, text: str) -> bool:
        """Verifica presença de elementos de identidade"""
        identity_elements = ["rafaela", "vereadora", "parnamirim"]
        text_lower = text.lower()
        
        return any(element in text_lower for element in identity_elements)
    
    def _check_value_alignment(self, text: str) -> bool:
        """Verifica alinhamento com valores"""
        values = [
            "inclusão", "transparência", "acessibilidade", "saúde",
            "assistência", "trabalho", "oportunidade", "união"
        ]
        text_lower = text.lower()
        
        return any(value in text_lower for value in values)
    
    def _check_language_style(self, text: str) -> bool:
        """Verifica estilo de linguagem"""
        # Deve usar "você" ao invés de "senhor/senhora"
        if "senhor" in text.lower() or "senhora" in text.lower():
            return False
        
        # Deve ter linguagem acessível
        complex_words = ["outrossim", "destarte", "conquanto", "porquanto"]
        text_lower = text.lower()
        
        return not any(word in text_lower for word in complex_words)
    
    def _check_simple_language(self, text: str) -> bool:
        """Verifica se usa linguagem simples"""
        # Palavras muito complexas que devem ser evitadas
        complex_words = [
            "subsequente", "concomitante", "outrossim", "destarte",
            "conquanto", "porquanto", "dessarte", "mormente"
        ]
        
        text_lower = text.lower()
        return not any(word in text_lower for word in complex_words)
    
    def _check_clear_instructions(self, text: str) -> bool:
        """Verifica se as instruções são claras"""
        # Deve ter verbos de ação claros
        action_verbs = [
            "verificar", "consultar", "procurar", "entrar", "dar entrada",
            "marcar", "agendar", "buscar", "solicitar"
        ]
        
        text_lower = text.lower()
        return any(verb in text_lower for verb in action_verbs)
    
    def _check_inclusive_language(self, text: str) -> bool:
        """Verifica linguagem inclusiva"""
        # Não deve ter linguagem excludente
        exclusive_terms = [
            "deficiente", "aleijado", "inválido", "anormal",
            "portador", "sofredor"
        ]
        
        text_lower = text.lower()
        return not any(term in text_lower for term in exclusive_terms)
    
    def get_quality_report(self):
        """Retorna relatório de qualidade"""
        return self.quality_results


if __name__ == "__main__":
    unittest.main()
