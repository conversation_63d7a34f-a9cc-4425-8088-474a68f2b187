"""
Testes de integração end-to-end para o sistema completo
"""

import unittest
import asyncio
import sys
import os
import time
from unittest.mock import Mock, patch, AsyncMock

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.persona import RESPONSE_TEMPLATES, EMOJIS, HASHTAGS
from pipelines.post_processing import PostProcessor, process_response
from pipelines.chat_pipeline import ChatPipeline
from handlers.intent_handlers import IntentHandlers, create_intent_handler


class TestEndToEndIntegration(unittest.TestCase):
    """
    Testes de integração end-to-end
    """
    
    def setUp(self):
        """Configurar testes de integração"""
        self.mock_supabase = Mock()
        self.processor = PostProcessor()
        self.handler = create_intent_handler(self.mock_supabase)
        self.integration_results = {}
    
    def test_complete_conversation_flow(self):
        """Testa fluxo completo de conversa"""
        print("   🔗 Testando fluxo completo de conversa...")
        
        # Simular uma conversa completa
        conversation_steps = [
            {
                "user_input": "Bom dia!",
                "expected_type": "template",
                "expected_elements": ["bom dia", "nome", "bairro"]
            },
            {
                "user_input": "Meu nome é João e moro no Centro",
                "expected_type": "general",
                "expected_elements": ["joão", "centro"]
            },
            {
                "user_input": "Preciso de cesta básica",
                "expected_type": "template", 
                "expected_elements": ["cras", "cadastro"]
            },
            {
                "user_input": "Como faço o cadastro no CRAS?",
                "expected_type": "general",
                "expected_elements": ["cadastro", "cras"]
            },
            {
                "user_input": "Obrigado pela ajuda",
                "expected_type": "template",
                "expected_elements": ["obrigado", "ajuda"]
            }
        ]
        
        conversation_history = []
        successful_steps = 0
        
        for i, step in enumerate(conversation_steps):
            try:
                user_input = step["user_input"]
                
                # 1. Tentar template primeiro
                template_response = self.handler.handle_template_query(user_input)
                
                if template_response:
                    response = template_response
                    response_type = "template"
                else:
                    # 2. Simular resposta geral
                    response = f"Entendo sua mensagem sobre '{user_input}'. Nossa equipe está aqui para ajudar você!"
                    response = process_response(response)
                    response_type = "general"
                
                # 3. Validar resposta
                self.assertIsNotNone(response)
                self.assertIsInstance(response, str)
                self.assertGreater(len(response), 10)
                
                # 4. Verificar elementos esperados (flexível)
                response_lower = response.lower()
                has_expected_elements = any(
                    element in response_lower or element in user_input.lower()
                    for element in step["expected_elements"]
                )
                
                # 5. Adicionar ao histórico
                conversation_history.append({
                    'sender': 'user',
                    'content': user_input,
                    'step': i + 1
                })
                conversation_history.append({
                    'sender': 'ai',
                    'content': response,
                    'type': response_type,
                    'step': i + 1
                })
                
                successful_steps += 1
                print(f"      ✅ Passo {i+1}: {user_input[:30]}... -> {response_type}")
                
            except Exception as e:
                print(f"      ❌ Passo {i+1} falhou: {e}")
        
        # Validar conversa completa
        self.assertGreaterEqual(successful_steps, 4, "Muitos passos da conversa falharam")
        self.assertEqual(len(conversation_history), successful_steps * 2, "Histórico incompleto")
        
        self.integration_results['conversation_flow'] = {
            'total_steps': len(conversation_steps),
            'successful_steps': successful_steps,
            'success_rate': successful_steps / len(conversation_steps),
            'conversation_length': len(conversation_history)
        }
        
        print(f"   🎉 Conversa completa: {successful_steps}/{len(conversation_steps)} passos")
    
    def test_template_to_processing_integration(self):
        """Testa integração template -> pós-processamento"""
        print("   🔗 Testando integração template + pós-processamento...")
        
        template_tests = [
            "Bom dia!",
            "Preciso de cesta básica", 
            "Onde encontro remédio?",
            "Quero informações sobre emprego",
            "Preciso marcar consulta"
        ]
        
        successful_integrations = 0
        processing_times = []
        
        for query in template_tests:
            try:
                start_time = time.time()
                
                # 1. Template matching
                template_response = self.handler.handle_template_query(query)
                
                if template_response:
                    # 2. Verificar se já foi processado (handler já processa)
                    self.assertIsInstance(template_response, str)
                    self.assertGreater(len(template_response), 10)
                    
                    # 3. Verificar elementos da persona
                    has_emoji = any(emoji in template_response for emoji in EMOJIS)
                    has_hashtag = any(hashtag in template_response for hashtag in HASHTAGS)
                    
                    # Pelo menos um elemento deve estar presente
                    self.assertTrue(has_emoji or has_hashtag, "Resposta sem elementos da persona")
                    
                    successful_integrations += 1
                
                end_time = time.time()
                processing_times.append(end_time - start_time)
                
            except Exception as e:
                print(f"      ❌ Falha na integração para '{query}': {e}")
        
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        self.integration_results['template_processing'] = {
            'total_tests': len(template_tests),
            'successful_integrations': successful_integrations,
            'success_rate': successful_integrations / len(template_tests),
            'avg_processing_time': avg_processing_time
        }
        
        self.assertGreater(successful_integrations, len(template_tests) * 0.8, "Muitas integrações falharam")
        
        print(f"   🎉 Integração template+processamento: {successful_integrations}/{len(template_tests)}")
    
    def test_persona_consistency_across_components(self):
        """Testa consistência da persona entre componentes"""
        print("   🔗 Testando consistência da persona...")
        
        # Coletar respostas de diferentes componentes
        responses = []
        
        # 1. Respostas de templates
        template_queries = ["Bom dia", "cesta básica", "remédio", "emprego"]
        for query in template_queries:
            response = self.handler.handle_template_query(query)
            if response:
                responses.append(('template', response))
        
        # 2. Resposta de fallback
        fallback_response = self.handler.get_fallback_response()
        responses.append(('fallback', fallback_response))
        
        # 3. Respostas processadas
        raw_texts = [
            "Nossa equipe está trabalhando para você",
            "Vamos juntos construir uma cidade melhor",
            "Transparência é fundamental na gestão"
        ]
        for text in raw_texts:
            processed = process_response(text)
            responses.append(('processed', processed))
        
        # Analisar consistência
        persona_elements = ["você", "nossa", "juntos", "equipe", "ajudar", "sempre"]
        consistent_responses = 0
        
        for response_type, response in responses:
            response_lower = response.lower()
            
            # Verificar elementos da persona
            has_persona_elements = any(element in response_lower for element in persona_elements)
            
            # Verificar tom positivo
            positive_indicators = ["ajudar", "juntos", "sempre", "melhor", "força", "conte comigo"]
            has_positive_tone = any(indicator in response_lower for indicator in positive_indicators)
            
            # Verificar ausência de tom negativo
            negative_indicators = ["não posso", "impossível", "nunca", "jamais"]
            has_negative_tone = any(indicator in response_lower for indicator in negative_indicators)
            
            if (has_persona_elements or has_positive_tone) and not has_negative_tone:
                consistent_responses += 1
        
        consistency_rate = consistent_responses / len(responses)
        
        self.integration_results['persona_consistency'] = {
            'total_responses': len(responses),
            'consistent_responses': consistent_responses,
            'consistency_rate': consistency_rate,
            'response_types': [r[0] for r in responses]
        }
        
        self.assertGreater(consistency_rate, 0.8, f"Baixa consistência da persona: {consistency_rate:.2%}")
        
        print(f"   🎉 Consistência da persona: {consistency_rate:.1%} ({consistent_responses}/{len(responses)})")
    
    def test_error_handling_integration(self):
        """Testa tratamento de erros em integração"""
        print("   🔗 Testando tratamento de erros...")
        
        error_scenarios = [
            ("", "entrada vazia"),
            ("   \n\n   ", "apenas espaços"),
            ("xyz123!@#$%", "caracteres especiais"),
            ("a" * 1000, "texto muito longo"),
            (None, "entrada nula")
        ]
        
        handled_errors = 0
        
        for test_input, scenario_name in error_scenarios:
            try:
                # Testar template handler
                if test_input is not None:
                    template_result = self.handler.handle_template_query(test_input)
                    # Deve retornar None ou string válida
                    self.assertTrue(template_result is None or isinstance(template_result, str))
                
                # Testar pós-processamento
                if test_input is not None:
                    processed_result = process_response(test_input)
                    # Deve sempre retornar string
                    self.assertIsInstance(processed_result, str)
                
                handled_errors += 1
                print(f"      ✅ {scenario_name}: tratado corretamente")
                
            except Exception as e:
                print(f"      ❌ {scenario_name}: erro não tratado - {e}")
        
        error_handling_rate = handled_errors / len(error_scenarios)
        
        self.integration_results['error_handling'] = {
            'total_scenarios': len(error_scenarios),
            'handled_errors': handled_errors,
            'handling_rate': error_handling_rate
        }
        
        self.assertGreater(error_handling_rate, 0.8, "Tratamento de erros insuficiente")
        
        print(f"   🎉 Tratamento de erros: {error_handling_rate:.1%}")
    
    def test_performance_under_load_integration(self):
        """Testa performance sob carga em integração"""
        print("   🔗 Testando performance sob carga...")
        
        # Simular carga de trabalho realística
        workload = [
            ("Bom dia!", "template"),
            ("Preciso de ajuda", "general"),
            ("cesta básica", "template"),
            ("Como está o tempo?", "general"),
            ("remédio na farmácia", "template"),
            ("Obrigado", "template")
        ] * 50  # 300 requests total
        
        start_time = time.time()
        successful_requests = 0
        response_times = []
        
        for query, expected_type in workload:
            request_start = time.time()
            
            try:
                # Processar request completo
                template_response = self.handler.handle_template_query(query)
                
                if template_response:
                    final_response = template_response
                else:
                    # Simular resposta geral processada
                    raw_response = f"Entendo sua mensagem. Nossa equipe vai ajudar você!"
                    final_response = process_response(raw_response)
                
                # Validar resposta
                self.assertIsInstance(final_response, str)
                self.assertGreater(len(final_response), 5)
                
                successful_requests += 1
                
            except Exception as e:
                print(f"      ❌ Request falhou: {e}")
            
            request_end = time.time()
            response_times.append(request_end - request_start)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Calcular métricas
        throughput = len(workload) / total_time
        avg_response_time = sum(response_times) / len(response_times)
        success_rate = successful_requests / len(workload)
        
        self.integration_results['load_performance'] = {
            'total_requests': len(workload),
            'successful_requests': successful_requests,
            'success_rate': success_rate,
            'total_time': total_time,
            'throughput': throughput,
            'avg_response_time': avg_response_time
        }
        
        # Validações de performance
        self.assertGreater(throughput, 50, f"Throughput muito baixo: {throughput:.1f} req/s")
        self.assertLess(avg_response_time, 0.1, f"Tempo de resposta muito alto: {avg_response_time:.3f}s")
        self.assertGreater(success_rate, 0.95, f"Taxa de sucesso muito baixa: {success_rate:.2%}")
        
        print(f"   🎉 Performance: {throughput:.1f} req/s, {avg_response_time*1000:.1f}ms avg")
    
    def test_data_flow_integration(self):
        """Testa fluxo de dados entre componentes"""
        print("   🔗 Testando fluxo de dados...")
        
        # Testar fluxo: Input -> Template -> Processing -> Output
        test_cases = [
            {
                "input": "Bom dia, como vai?",
                "expected_flow": ["template_match", "persona_elements", "final_output"]
            },
            {
                "input": "Preciso de cesta básica urgente",
                "expected_flow": ["template_match", "cras_mention", "final_output"]
            }
        ]
        
        successful_flows = 0
        
        for case in test_cases:
            try:
                input_text = case["input"]
                flow_steps = []
                
                # Passo 1: Template matching
                template_response = self.handler.handle_template_query(input_text)
                if template_response:
                    flow_steps.append("template_match")
                    
                    # Passo 2: Verificar elementos específicos
                    if "cras" in template_response.lower():
                        flow_steps.append("cras_mention")
                    
                    # Passo 3: Verificar elementos da persona
                    has_persona = any(
                        element in template_response.lower() 
                        for element in ["você", "nossa", "equipe", "ajudar"]
                    )
                    if has_persona:
                        flow_steps.append("persona_elements")
                    
                    # Passo 4: Output final válido
                    if len(template_response) > 10:
                        flow_steps.append("final_output")
                
                # Verificar se o fluxo esperado foi seguido
                expected_steps = case["expected_flow"]
                flow_complete = all(step in flow_steps for step in expected_steps)
                
                if flow_complete:
                    successful_flows += 1
                    print(f"      ✅ Fluxo completo: {input_text[:30]}...")
                else:
                    print(f"      ⚠️  Fluxo incompleto: {input_text[:30]}... (faltam: {set(expected_steps) - set(flow_steps)})")
                
            except Exception as e:
                print(f"      ❌ Erro no fluxo: {e}")
        
        flow_success_rate = successful_flows / len(test_cases)
        
        self.integration_results['data_flow'] = {
            'total_cases': len(test_cases),
            'successful_flows': successful_flows,
            'flow_success_rate': flow_success_rate
        }
        
        self.assertGreater(flow_success_rate, 0.5, "Muitos fluxos de dados falharam")
        
        print(f"   🎉 Fluxo de dados: {flow_success_rate:.1%} ({successful_flows}/{len(test_cases)})")
    
    def get_integration_results(self):
        """Retorna resultados dos testes de integração"""
        return self.integration_results


if __name__ == "__main__":
    unittest.main()
