import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { 
  Container, Typography, List, ListItem, ListItemText, 
  CircularProgress, Alert, Paper, ListItemAvatar, Avatar, Box 
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import axiosInstance from '../api/axiosInstance';

const fetchConversationsForAttention = async () => {
  // Usamos o novo filtro da API
  const { data } = await axiosInstance.get('/conversations/', {
    params: { requires_attention: true, limit: 100 } // Pega até 100 conversas
  });
  return data.conversations;
};

const InboxPage = () => {
  const navigate = useNavigate();
  const { data: conversations, isLoading, isError, error, refetch } = useQuery({
    queryKey: ['conversationsForAttention'],
    queryFn: fetchConversationsForAttention,
  });

  const handleConversationClick = (conversationId) => {
    navigate(`/inbox/${conversationId}`);
  };

  const formatLastMessageTime = (isoString) => {
    if (!isoString) return '';
    const date = new Date(isoString);
    return date.toLocaleDateString('pt-BR', { day: 'numeric', month: 'long' }) + ' às ' + date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
  }

  return (
    <Container maxWidth="lg">
      <Typography variant="h4" sx={{ mb: 4 }}>
        Caixa de Entrada
      </Typography>
      <Typography sx={{ mb: 2 }} color="text.secondary">
        Conversas que precisam de sua atenção.
      </Typography>
      <Paper>
        {isLoading && <CircularProgress />}
        {isError && <Alert severity="error">Erro ao carregar conversas: {error.message}</Alert>}
        
        {conversations && (
          <List>
            {conversations.length === 0 ? (
              <ListItem>
                <ListItemText primary="Nenhuma conversa precisa de atenção no momento." />
              </ListItem>
            ) : (
              conversations.map((convo) => (
                <ListItem 
                  button 
                  key={convo.id} 
                  onClick={() => handleConversationClick(convo.id)}
                  divider
                >
                  <ListItemAvatar>
                    <Avatar>
                      <PersonIcon />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={convo.contact_name || 'Nome não disponível'}
                    secondary={`Aguardando resposta desde: ${formatLastMessageTime(convo.last_message_at)}`}
                  />
                </ListItem>
              ))
            )}
          </List>
        )}
      </Paper>
    </Container>
  );
};

export default InboxPage;
