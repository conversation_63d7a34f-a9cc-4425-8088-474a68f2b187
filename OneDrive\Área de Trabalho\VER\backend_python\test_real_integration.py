#!/usr/bin/env python3

"""
Teste de integração real com o backend em execução
"""

import requests
import time
import json
import sys
from datetime import datetime


class RealIntegrationTester:
    """
    Testador de integração real com APIs
    """
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.results = {}
    
    def test_backend_availability(self):
        """Testa se o backend está disponível"""
        print("🔗 Testando disponibilidade do backend...")
        
        try:
            response = self.session.get(f"{self.base_url}/", timeout=5)
            if response.status_code == 200:
                print("   ✅ Backend disponível")
                return True
            else:
                print(f"   ❌ Backend retornou status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Backend não disponível: {e}")
            return False
    
    def test_authentication_flow(self):
        """Testa fluxo de autenticação"""
        print("🔗 Testando autenticação...")
        
        try:
            # Teste de login
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            
            response = self.session.post(
                f"{self.base_url}/token",
                data=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                token_data = response.json()
                access_token = token_data.get("access_token")
                
                if access_token:
                    # Configurar token para próximas requests
                    self.session.headers.update({
                        "Authorization": f"Bearer {access_token}"
                    })
                    print("   ✅ Autenticação bem-sucedida")
                    return True
                else:
                    print("   ❌ Token não recebido")
                    return False
            else:
                print(f"   ❌ Login falhou: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Erro na autenticação: {e}")
            return False
    
    def test_chat_endpoints(self):
        """Testa endpoints de chat"""
        print("🔗 Testando endpoints de chat...")
        
        chat_tests = [
            {
                "endpoint": "/chat/",
                "payload": {
                    "query": "Bom dia!",
                    "contact_id": "test_user_123",
                    "contact_name": "Usuário Teste"
                },
                "expected_keywords": ["bom dia", "nome", "bairro"]
            },
            {
                "endpoint": "/chat/",
                "payload": {
                    "query": "Preciso de cesta básica",
                    "contact_id": "test_user_456", 
                    "contact_name": "Maria Teste"
                },
                "expected_keywords": ["cras", "cadastro"]
            },
            {
                "endpoint": "/query-rag/",
                "payload": {
                    "query": "Quais são os programas sociais disponíveis?",
                    "contact_id": "test_user_789"
                },
                "expected_keywords": ["programas", "social"]
            }
        ]
        
        successful_tests = 0
        response_times = []
        
        for test in chat_tests:
            try:
                start_time = time.time()
                
                response = self.session.post(
                    f"{self.base_url}{test['endpoint']}",
                    json=test["payload"],
                    timeout=30
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                response_times.append(response_time)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if "response" in data:
                        response_text = data["response"].lower()
                        
                        # Verificar palavras-chave esperadas (flexível)
                        has_expected_content = any(
                            keyword in response_text 
                            for keyword in test["expected_keywords"]
                        ) or len(data["response"]) > 20  # Resposta substancial
                        
                        if has_expected_content:
                            successful_tests += 1
                            print(f"   ✅ {test['endpoint']}: resposta válida ({response_time:.2f}s)")
                        else:
                            print(f"   ⚠️  {test['endpoint']}: resposta sem conteúdo esperado")
                    else:
                        print(f"   ❌ {test['endpoint']}: resposta sem campo 'response'")
                else:
                    print(f"   ❌ {test['endpoint']}: status {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {test['endpoint']}: erro - {e}")
        
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        success_rate = successful_tests / len(chat_tests)
        
        self.results['chat_endpoints'] = {
            'total_tests': len(chat_tests),
            'successful_tests': successful_tests,
            'success_rate': success_rate,
            'avg_response_time': avg_response_time
        }
        
        print(f"   📊 Chat endpoints: {success_rate:.1%} sucesso, {avg_response_time:.2f}s média")
        return success_rate > 0.5
    
    def test_dashboard_endpoints(self):
        """Testa endpoints do dashboard"""
        print("🔗 Testando endpoints do dashboard...")
        
        dashboard_tests = [
            {
                "endpoint": "/dashboard/stats",
                "method": "GET",
                "expected_fields": ["total_conversations", "total_documents"]
            },
            {
                "endpoint": "/conversations/",
                "method": "GET", 
                "expected_fields": ["conversations"]
            },
            {
                "endpoint": "/documents/",
                "method": "GET",
                "expected_fields": ["documents"]
            }
        ]
        
        successful_tests = 0
        
        for test in dashboard_tests:
            try:
                if test["method"] == "GET":
                    response = self.session.get(
                        f"{self.base_url}{test['endpoint']}",
                        timeout=15
                    )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Verificar campos esperados
                    has_expected_fields = any(
                        field in data for field in test["expected_fields"]
                    )
                    
                    if has_expected_fields:
                        successful_tests += 1
                        print(f"   ✅ {test['endpoint']}: dados válidos")
                    else:
                        print(f"   ⚠️  {test['endpoint']}: campos esperados ausentes")
                else:
                    print(f"   ❌ {test['endpoint']}: status {response.status_code}")
                    
            except Exception as e:
                print(f"   ❌ {test['endpoint']}: erro - {e}")
        
        dashboard_success_rate = successful_tests / len(dashboard_tests)
        
        self.results['dashboard_endpoints'] = {
            'total_tests': len(dashboard_tests),
            'successful_tests': successful_tests,
            'success_rate': dashboard_success_rate
        }
        
        print(f"   📊 Dashboard: {dashboard_success_rate:.1%} sucesso")
        return dashboard_success_rate > 0.5
    
    def test_conversation_flow(self):
        """Testa fluxo completo de conversa"""
        print("🔗 Testando fluxo completo de conversa...")
        
        conversation_steps = [
            "Bom dia!",
            "Meu nome é João e moro no Centro",
            "Preciso de cesta básica",
            "Como faço o cadastro no CRAS?",
            "Obrigado pela ajuda"
        ]
        
        contact_id = f"integration_test_{int(time.time())}"
        successful_steps = 0
        
        for i, message in enumerate(conversation_steps):
            try:
                payload = {
                    "query": message,
                    "contact_id": contact_id,
                    "contact_name": "João Teste Integração"
                }
                
                response = self.session.post(
                    f"{self.base_url}/chat/",
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if "response" in data and len(data["response"]) > 10:
                        successful_steps += 1
                        print(f"   ✅ Passo {i+1}: {message[:30]}... -> resposta recebida")
                    else:
                        print(f"   ❌ Passo {i+1}: resposta inválida")
                else:
                    print(f"   ❌ Passo {i+1}: status {response.status_code}")
                
                # Pequena pausa entre mensagens
                time.sleep(0.5)
                
            except Exception as e:
                print(f"   ❌ Passo {i+1}: erro - {e}")
        
        flow_success_rate = successful_steps / len(conversation_steps)
        
        self.results['conversation_flow'] = {
            'total_steps': len(conversation_steps),
            'successful_steps': successful_steps,
            'success_rate': flow_success_rate,
            'contact_id': contact_id
        }
        
        print(f"   📊 Fluxo de conversa: {flow_success_rate:.1%} sucesso")
        return flow_success_rate > 0.7
    
    def run_all_tests(self):
        """Executa todos os testes de integração real"""
        print("🧪 TESTES DE INTEGRAÇÃO REAL COM BACKEND")
        print("=" * 60)
        
        start_time = time.time()
        
        # 1. Verificar disponibilidade
        if not self.test_backend_availability():
            print("\n❌ Backend não disponível. Certifique-se de que está rodando na porta 8000.")
            return False
        
        # 2. Autenticação
        if not self.test_authentication_flow():
            print("\n⚠️  Continuando sem autenticação...")
        
        # 3. Endpoints de chat
        chat_success = self.test_chat_endpoints()
        
        # 4. Endpoints do dashboard
        dashboard_success = self.test_dashboard_endpoints()
        
        # 5. Fluxo de conversa
        flow_success = self.test_conversation_flow()
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Relatório final
        print("\n" + "=" * 60)
        print("📊 RELATÓRIO DE INTEGRAÇÃO REAL")
        print("=" * 60)
        
        total_categories = 3  # chat, dashboard, flow
        successful_categories = sum([chat_success, dashboard_success, flow_success])
        overall_success_rate = successful_categories / total_categories
        
        print(f"\n⏱️  Duração: {duration:.2f} segundos")
        print(f"📊 Categorias testadas: {total_categories}")
        print(f"✅ Categorias aprovadas: {successful_categories}")
        print(f"📈 Taxa de sucesso geral: {overall_success_rate:.1%}")
        
        # Detalhes por categoria
        print(f"\n📋 DETALHES:")
        for category, result in self.results.items():
            success_rate = result.get('success_rate', 0)
            total = result.get('total_tests', 0)
            successful = result.get('successful_tests', 0)
            print(f"   {category}: {success_rate:.1%} ({successful}/{total})")
        
        # Status final
        if overall_success_rate >= 0.8:
            print(f"\n🎉 INTEGRAÇÃO REAL APROVADA! ({overall_success_rate:.1%})")
        elif overall_success_rate >= 0.6:
            print(f"\n✅ INTEGRAÇÃO REAL BOA! ({overall_success_rate:.1%})")
        else:
            print(f"\n⚠️  INTEGRAÇÃO REAL PRECISA DE ATENÇÃO! ({overall_success_rate:.1%})")
        
        print("=" * 60)
        
        return overall_success_rate > 0.6


def main():
    """Função principal"""
    print("🚀 Iniciando testes de integração real...")
    print("📋 Certifique-se de que o backend está rodando em http://localhost:8000")
    print()
    
    tester = RealIntegrationTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 Testes de integração real concluídos com sucesso!")
    else:
        print("\n⚠️  Alguns testes de integração falharam. Verifique o backend.")
    
    return success


if __name__ == "__main__":
    main()
