import { renderHook, act } from '@testing-library/react';
import useAuthStore from './authStore';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

describe('useAuthStore', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Reset the store state
    useAuthStore.getState().logout();
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useAuthStore());
    
    expect(result.current.authToken).toBeNull();
    expect(result.current.user).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('should set token and update authentication state', () => {
    const { result } = renderHook(() => useAuthStore());
    const testToken = 'test-token-123';

    act(() => {
      result.current.setToken(testToken);
    });

    expect(result.current.authToken).toBe(testToken);
    expect(result.current.isAuthenticated).toBe(true);
  });

  it('should handle null token correctly', () => {
    const { result } = renderHook(() => useAuthStore());

    // First set a token
    act(() => {
      result.current.setToken('test-token');
    });

    // Then set null token
    act(() => {
      result.current.setToken(null);
    });

    expect(result.current.authToken).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('should set user data', () => {
    const { result } = renderHook(() => useAuthStore());
    const testUser = { id: 1, username: 'testuser', email: '<EMAIL>' };

    act(() => {
      result.current.setUser(testUser);
    });

    expect(result.current.user).toEqual(testUser);
  });

  it('should logout and clear all data', () => {
    const { result } = renderHook(() => useAuthStore());
    const testToken = 'test-token-123';
    const testUser = { id: 1, username: 'testuser' };

    // Set initial data
    act(() => {
      result.current.setToken(testToken);
      result.current.setUser(testUser);
    });

    // Verify data is set
    expect(result.current.authToken).toBe(testToken);
    expect(result.current.user).toEqual(testUser);
    expect(result.current.isAuthenticated).toBe(true);

    // Logout
    act(() => {
      result.current.logout();
    });

    // Verify data is cleared
    expect(result.current.authToken).toBeNull();
    expect(result.current.user).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('should handle empty string token as falsy', () => {
    const { result } = renderHook(() => useAuthStore());

    act(() => {
      result.current.setToken('');
    });

    expect(result.current.authToken).toBe('');
    expect(result.current.isAuthenticated).toBe(false);
  });
});
