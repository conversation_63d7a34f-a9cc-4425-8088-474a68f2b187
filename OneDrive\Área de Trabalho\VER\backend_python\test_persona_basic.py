#!/usr/bin/env python3

"""
Testes básicos para o chat da persona (sem dependências externas)
"""

import sys
import os
import time

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_persona_config():
    """Testa a configuração da persona"""
    print("🧪 Testando configuração da persona...")
    
    try:
        from config.persona import (
            SYSTEM_PROMPT, RESPONSE_TEMPLATES, EMOJIS, HASHTAGS, PERSONA_CONFIG,
            RAG_SYSTEM_PROMPT, GENERAL_CONVERSATION_PROMPT, INTENT_ROUTING_PROMPT
        )
        
        tests_passed = 0
        total_tests = 0
        
        # Teste 1: System prompt
        total_tests += 1
        assert isinstance(SYSTEM_PROMPT, str), "System prompt deve ser string"
        assert len(SYSTEM_PROMPT) > 100, "System prompt muito curto"
        assert "Vereadora Rafaela de Nilda" in SYSTEM_PROMPT, "Nome não encontrado"
        assert "Parnamirim" in SYSTEM_PROMPT, "Cidade não encontrada"
        tests_passed += 1
        print("   ✅ System prompt válido")
        
        # Teste 2: Templates estrutura
        total_tests += 1
        assert isinstance(RESPONSE_TEMPLATES, dict), "Templates devem ser dict"
        assert len(RESPONSE_TEMPLATES) >= 8, f"Poucos templates: {len(RESPONSE_TEMPLATES)}"
        tests_passed += 1
        print("   ✅ Estrutura de templates válida")
        
        # Teste 3: Templates essenciais
        total_tests += 1
        essential_templates = [
            "saudacao_geral", "assistencia_social", "medicamentos", 
            "pedido_emprego", "cirurgias_exames", "mensagem_fe"
        ]
        for template in essential_templates:
            assert template in RESPONSE_TEMPLATES, f"Template {template} não encontrado"
            template_data = RESPONSE_TEMPLATES[template]
            assert "keywords" in template_data, f"Keywords ausentes em {template}"
            assert "response" in template_data, f"Response ausente em {template}"
            assert isinstance(template_data["keywords"], list), f"Keywords deve ser lista em {template}"
            assert len(template_data["keywords"]) > 0, f"Keywords vazia em {template}"
            assert isinstance(template_data["response"], str), f"Response deve ser string em {template}"
            assert len(template_data["response"]) > 10, f"Response muito curta em {template}"
        tests_passed += 1
        print("   ✅ Templates essenciais válidos")
        
        # Teste 4: Emojis
        total_tests += 1
        assert isinstance(EMOJIS, list), "Emojis devem ser lista"
        assert len(EMOJIS) >= 8, f"Poucos emojis: {len(EMOJIS)}"
        expected_emojis = ["✨", "🙏", "💖", "💪", "🤝"]
        for emoji in expected_emojis:
            assert emoji in EMOJIS, f"Emoji {emoji} não encontrado"
        tests_passed += 1
        print("   ✅ Emojis válidos")
        
        # Teste 5: Hashtags
        total_tests += 1
        assert isinstance(HASHTAGS, list), "Hashtags devem ser lista"
        assert len(HASHTAGS) >= 4, f"Poucas hashtags: {len(HASHTAGS)}"
        assert all(h.startswith("#") for h in HASHTAGS), "Hashtags devem começar com #"
        expected_hashtags = ["#RafaelaDeNilda", "#ParnamirimRN", "#Inclusão"]
        for hashtag in expected_hashtags:
            assert hashtag in HASHTAGS, f"Hashtag {hashtag} não encontrada"
        tests_passed += 1
        print("   ✅ Hashtags válidas")
        
        # Teste 6: Configuração
        total_tests += 1
        assert isinstance(PERSONA_CONFIG, dict), "Config deve ser dict"
        required_configs = [
            "max_response_length", "emoji_probability", "hashtag_probability",
            "temperature", "max_history_messages"
        ]
        for config in required_configs:
            assert config in PERSONA_CONFIG, f"Config {config} ausente"
        
        # Verificar tipos e valores
        assert isinstance(PERSONA_CONFIG["max_response_length"], int)
        assert PERSONA_CONFIG["max_response_length"] > 100
        assert isinstance(PERSONA_CONFIG["emoji_probability"], float)
        assert 0.0 <= PERSONA_CONFIG["emoji_probability"] <= 1.0
        assert isinstance(PERSONA_CONFIG["temperature"], float)
        assert 0.0 <= PERSONA_CONFIG["temperature"] <= 2.0
        tests_passed += 1
        print("   ✅ Configuração válida")
        
        # Teste 7: Prompts especializados
        total_tests += 1
        assert isinstance(RAG_SYSTEM_PROMPT, str), "RAG prompt deve ser string"
        assert "CONTEXTO ADICIONAL" in RAG_SYSTEM_PROMPT, "RAG prompt sem contexto"
        assert isinstance(GENERAL_CONVERSATION_PROMPT, str), "General prompt deve ser string"
        assert isinstance(INTENT_ROUTING_PROMPT, str), "Intent prompt deve ser string"
        assert "answer_with_template" in INTENT_ROUTING_PROMPT, "Intent prompt sem opções"
        tests_passed += 1
        print("   ✅ Prompts especializados válidos")
        
        # Teste 8: Cobertura de palavras-chave
        total_tests += 1
        all_keywords = []
        for template_data in RESPONSE_TEMPLATES.values():
            all_keywords.extend(template_data["keywords"])
        
        important_themes = [
            "bom dia", "oi", "olá", "cesta básica", "cras", 
            "remédio", "medicamento", "emprego", "trabalho", "deus", "amém"
        ]
        for theme in important_themes:
            found = any(theme in keyword for keyword in all_keywords)
            assert found, f"Tema '{theme}' não encontrado nas palavras-chave"
        tests_passed += 1
        print("   ✅ Cobertura de palavras-chave adequada")
        
        print(f"   🎉 Configuração da Persona: {tests_passed}/{total_tests} testes passaram")
        return tests_passed == total_tests
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_post_processing_basic():
    """Testa o pós-processamento básico"""
    print("🧪 Testando pós-processamento básico...")
    
    try:
        from pipelines.post_processing import PostProcessor
        from config.persona import EMOJIS, HASHTAGS, PERSONA_CONFIG
        
        processor = PostProcessor()
        tests_passed = 0
        total_tests = 0
        
        # Teste 1: Inicialização
        total_tests += 1
        assert processor.emojis == EMOJIS, "Emojis não carregados"
        assert processor.hashtags == HASHTAGS, "Hashtags não carregadas"
        assert processor.config == PERSONA_CONFIG, "Config não carregada"
        tests_passed += 1
        print("   ✅ Inicialização correta")
        
        # Teste 2: Limpeza de texto
        total_tests += 1
        test_cases = [
            ("  Olá,   como   você está?  \n\n  ", "Olá, como você está?"),
            ("Olá.Como vai?Tudo bem!", "Olá. Como vai? Tudo bem!")
        ]
        for dirty, expected in test_cases:
            clean = processor.clean_text(dirty)
            assert clean == expected, f"Limpeza falhou: '{dirty}' -> '{clean}' (esperado: '{expected}')"

        # Teste específico para quebras de linha múltiplas
        dirty_multiline = "Primeira linha\n\n\nSegunda linha"
        clean_multiline = processor.clean_text(dirty_multiline)
        # Deve ter no máximo 2 quebras de linha consecutivas
        assert "\n\n\n" not in clean_multiline, f"Quebras múltiplas não removidas: '{clean_multiline}'"
        tests_passed += 1
        print("   ✅ Limpeza de texto funcionando")
        
        # Teste 3: Detecção de emoji
        total_tests += 1
        assert not processor._has_emoji("Texto sem emoji"), "Falso positivo para emoji"
        assert processor._has_emoji("Texto com emoji ✨"), "Falso negativo para emoji"
        tests_passed += 1
        print("   ✅ Detecção de emoji funcionando")
        
        # Teste 4: Detecção de hashtag
        total_tests += 1
        assert not processor._has_hashtag("Texto sem hashtag"), "Falso positivo para hashtag"
        assert processor._has_hashtag("Texto com #hashtag"), "Falso negativo para hashtag"
        tests_passed += 1
        print("   ✅ Detecção de hashtag funcionando")
        
        # Teste 5: Adição forçada de emoji
        total_tests += 1
        text = "Olá, como posso ajudar?"
        text_with_emoji = processor.add_emoji(text, force=True)
        assert text != text_with_emoji, "Emoji não foi adicionado com force=True"
        added_emoji = text_with_emoji.replace(text, "").strip()
        assert added_emoji in EMOJIS, f"Emoji inválido adicionado: {added_emoji}"
        tests_passed += 1
        print("   ✅ Adição forçada de emoji funcionando")
        
        # Teste 6: Adição forçada de hashtag
        total_tests += 1
        text_with_hashtag = processor.add_hashtag(text, force=True)
        assert "#" in text_with_hashtag, "Hashtag não foi adicionada com force=True"
        tests_passed += 1
        print("   ✅ Adição forçada de hashtag funcionando")
        
        # Teste 7: Seleção contextual de emoji
        total_tests += 1
        contexts = [
            ("Vou verificar na UBS sobre medicamento", ["🏥", "💪", "💖"]),
            ("Que Deus nos abençoe", ["🙏", "✨"])
        ]
        for text, expected_emojis in contexts:
            emoji = processor._select_contextual_emoji(text)
            assert emoji in expected_emojis, f"Emoji contextual incorreto para '{text}': {emoji}"

        # Teste específico para trabalho (mais flexível)
        work_emoji = processor._select_contextual_emoji("Vamos trabalhar juntos")
        assert work_emoji in EMOJIS, f"Emoji inválido para trabalho: {work_emoji}"
        tests_passed += 1
        print("   ✅ Seleção contextual de emoji funcionando")
        
        # Teste 8: Seleção contextual de hashtag
        total_tests += 1
        hashtag_contexts = [
            ("A inclusão é fundamental", "#AcessibilidadeParaTodos"),
            ("Transparência nos gastos", "#TransparênciaTotal"),
            ("Nossa Parnamirim", "#ParnamirimRN")
        ]
        for text, expected_hashtag in hashtag_contexts:
            hashtag = processor._select_contextual_hashtag(text)
            assert hashtag == expected_hashtag, f"Hashtag contextual incorreta para '{text}': {hashtag}"
        tests_passed += 1
        print("   ✅ Seleção contextual de hashtag funcionando")
        
        print(f"   🎉 Pós-processamento: {tests_passed}/{total_tests} testes passaram")
        return tests_passed == total_tests
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_template_matching():
    """Testa correspondência de templates sem dependências externas"""
    print("🧪 Testando correspondência de templates...")
    
    try:
        from config.persona import RESPONSE_TEMPLATES
        
        tests_passed = 0
        total_tests = 0
        
        # Simular lógica de template matching
        def match_template(query):
            query_lower = query.lower()
            for template_key, template_data in RESPONSE_TEMPLATES.items():
                if any(keyword in query_lower for keyword in template_data["keywords"]):
                    return template_key, template_data["response"]
            return None, None
        
        # Teste 1: Saudações
        total_tests += 1
        test_cases = [
            ("Bom dia!", "saudacao_geral"),
            ("Oi", "saudacao_geral"),
            ("Olá", "saudacao_geral"),
            ("Boa tarde", "saudacao_geral")
        ]
        for query, expected_template in test_cases:
            template_key, response = match_template(query)
            assert template_key == expected_template, f"Template incorreto para '{query}': {template_key}"
            assert response is not None, f"Resposta vazia para '{query}'"
        tests_passed += 1
        print("   ✅ Saudações funcionando")
        
        # Teste 2: Assistência social
        total_tests += 1
        social_queries = ["cesta básica", "CRAS", "assistência social", "ajuda social"]
        for query in social_queries:
            template_key, response = match_template(query)
            assert template_key == "assistencia_social", f"Template incorreto para '{query}': {template_key}"
            assert "CRAS" in response, f"Resposta sem CRAS para '{query}'"
        tests_passed += 1
        print("   ✅ Assistência social funcionando")
        
        # Teste 3: Medicamentos
        total_tests += 1
        med_queries = ["remédio", "medicamento", "farmácia"]
        for query in med_queries:
            template_key, response = match_template(query)
            assert template_key == "medicamentos", f"Template incorreto para '{query}': {template_key}"
            assert "UBS" in response, f"Resposta sem UBS para '{query}'"
        tests_passed += 1
        print("   ✅ Medicamentos funcionando")
        
        # Teste 4: Case insensitive
        total_tests += 1
        case_tests = [
            ("BOM DIA", "saudacao_geral"),
            ("CESTA BÁSICA", "assistencia_social"),
            ("REMÉDIO", "medicamentos")
        ]
        for query, expected_template in case_tests:
            template_key, response = match_template(query)
            assert template_key == expected_template, f"Case insensitive falhou para '{query}'"
        tests_passed += 1
        print("   ✅ Case insensitive funcionando")
        
        # Teste 5: Queries sem match
        total_tests += 1
        no_match_queries = ["pergunta muito específica", "query inexistente", "xyz123"]
        for query in no_match_queries:
            template_key, response = match_template(query)
            assert template_key is None, f"Falso positivo para '{query}': {template_key}"
            assert response is None, f"Resposta inesperada para '{query}': {response}"
        tests_passed += 1
        print("   ✅ Queries sem match funcionando")
        
        print(f"   🎉 Template matching: {tests_passed}/{total_tests} testes passaram")
        return tests_passed == total_tests
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_persona_consistency():
    """Testa consistência da persona"""
    print("🧪 Testando consistência da persona...")
    
    try:
        from config.persona import RESPONSE_TEMPLATES, SYSTEM_PROMPT
        
        tests_passed = 0
        total_tests = 0
        
        # Teste 1: Tom das respostas
        total_tests += 1
        persona_elements = ["você", "nossa", "juntos", "equipe", "ajudar", "conte comigo"]
        
        for template_key, template_data in RESPONSE_TEMPLATES.items():
            response = template_data["response"]
            has_persona_element = any(element in response.lower() for element in persona_elements)
            assert has_persona_element, f"Resposta sem elementos da persona em {template_key}: {response}"
        tests_passed += 1
        print("   ✅ Tom das respostas consistente")
        
        # Teste 2: Elementos da identidade
        total_tests += 1
        identity_elements = ["Rafaela", "Vereadora", "Parnamirim"]
        system_has_identity = all(element in SYSTEM_PROMPT for element in identity_elements)
        assert system_has_identity, "System prompt sem elementos de identidade"
        tests_passed += 1
        print("   ✅ Elementos de identidade presentes")
        
        # Teste 3: Valores da persona
        total_tests += 1
        values = ["inclusão", "transparência", "acessibilidade", "saúde", "assistência"]
        system_lower = SYSTEM_PROMPT.lower()
        values_present = sum(1 for value in values if value in system_lower)
        assert values_present >= 3, f"Poucos valores da persona no system prompt: {values_present}/5"
        tests_passed += 1
        print("   ✅ Valores da persona presentes")
        
        # Teste 4: Comprimento das respostas
        total_tests += 1
        for template_key, template_data in RESPONSE_TEMPLATES.items():
            response = template_data["response"]
            assert 20 <= len(response) <= 200, f"Resposta com comprimento inadequado em {template_key}: {len(response)} chars"
        tests_passed += 1
        print("   ✅ Comprimento das respostas adequado")
        
        print(f"   🎉 Consistência da persona: {tests_passed}/{total_tests} testes passaram")
        return tests_passed == total_tests
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def run_basic_tests():
    """Executa todos os testes básicos"""
    print("🧪 TESTES UNITÁRIOS BÁSICOS DO CHAT DA PERSONA")
    print("=" * 60)
    
    start_time = time.time()
    
    tests = [
        ("Configuração da Persona", test_persona_config),
        ("Pós-processamento Básico", test_post_processing_basic),
        ("Correspondência de Templates", test_template_matching),
        ("Consistência da Persona", test_persona_consistency)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"   🎉 {test_name}: ✅ PASSOU")
            else:
                print(f"   ❌ {test_name}: ❌ FALHOU")
                
        except Exception as e:
            print(f"   ❌ {test_name}: ❌ ERRO - {e}")
            results.append((test_name, False))
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Relatório final
    print("\n" + "=" * 60)
    print("📊 RELATÓRIO FINAL DOS TESTES BÁSICOS")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"\n⏱️  Tempo de execução: {duration:.2f} segundos")
    print(f"🧪 Testes executados: {total}")
    print(f"✅ Sucessos: {passed}")
    print(f"❌ Falhas: {total - passed}")
    
    if total > 0:
        success_rate = (passed / total) * 100
        print(f"📈 Taxa de sucesso: {success_rate:.1f}%")
    
    # Detalhes dos resultados
    print(f"\n📋 DETALHES:")
    for test_name, success in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {status} - {test_name}")
    
    # Status final
    if passed == total:
        print(f"\n🎉 TODOS OS TESTES BÁSICOS PASSARAM!")
        print("✅ A configuração e lógica básica do chat da persona estão funcionando!")
        print("\n🎯 FUNCIONALIDADES VALIDADAS:")
        print("   👤 Configuração da persona centralizada")
        print("   📋 Templates de resposta estruturados")
        print("   🎨 Pós-processamento básico")
        print("   🔍 Correspondência de palavras-chave")
        print("   🎭 Consistência da persona")
        print("\n💡 PRÓXIMOS PASSOS:")
        print("   🔧 Configurar API keys para testes completos")
        print("   🧪 Executar testes de integração com IA")
        print("   🚀 Testar em ambiente de produção")
    else:
        print(f"\n⚠️  {total - passed} TESTE(S) BÁSICO(S) FALHARAM")
        print("🔧 Corrija os problemas básicos antes de prosseguir")
    
    print("=" * 60)
    
    return passed == total


if __name__ == "__main__":
    success = run_basic_tests()
    sys.exit(0 if success else 1)
