import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>con<PERSON>utton,
  <PERSON>u,
  MenuI<PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>,
  <PERSON>,
  Divider,
  But<PERSON>
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Message as MessageIcon,
  Info as InfoIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useWebSocket } from '../hooks/useWebSocket';

const NotificationCenter = () => {
  const { isConnected, notifications, removeNotification, clearNotifications } = useWebSocket();
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'message':
        return <MessageIcon fontSize="small" />;
      case 'status':
        return <InfoIcon fontSize="small" />;
      default:
        return <NotificationsIcon fontSize="small" />;
    }
  };

  const formatTime = (timestamp) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleTimeString('pt-BR', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch {
      return '';
    }
  };

  return (
    <>
      <IconButton
        color="inherit"
        onClick={handleClick}
        sx={{ mr: 1 }}
      >
        <Badge badgeContent={notifications.length} color="error">
          <NotificationsIcon />
        </Badge>
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: 350,
            maxHeight: 400,
            overflow: 'auto'
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <Box sx={{ p: 2, pb: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Notificações
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip
                size="small"
                label={isConnected ? 'Online' : 'Offline'}
                color={isConnected ? 'success' : 'error'}
                variant="outlined"
              />
              {notifications.length > 0 && (
                <Button
                  size="small"
                  onClick={clearNotifications}
                  sx={{ minWidth: 'auto', p: 0.5 }}
                >
                  Limpar
                </Button>
              )}
            </Box>
          </Box>
        </Box>

        <Divider />

        {notifications.length === 0 ? (
          <MenuItem disabled>
            <Typography variant="body2" color="text.secondary">
              Nenhuma notificação
            </Typography>
          </MenuItem>
        ) : (
          notifications.slice(-10).reverse().map((notification) => (
            <MenuItem
              key={notification.id}
              sx={{
                flexDirection: 'column',
                alignItems: 'flex-start',
                whiteSpace: 'normal',
                py: 1.5
              }}
            >
              <Box sx={{ display: 'flex', width: '100%', alignItems: 'flex-start' }}>
                <Box sx={{ mr: 1, mt: 0.5 }}>
                  {getNotificationIcon(notification.type)}
                </Box>
                <Box sx={{ flex: 1, minWidth: 0 }}>
                  <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                    {notification.title}
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color="text.secondary"
                    sx={{ 
                      wordBreak: 'break-word',
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden'
                    }}
                  >
                    {notification.message}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {formatTime(notification.timestamp)}
                  </Typography>
                </Box>
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    removeNotification(notification.id);
                  }}
                  sx={{ ml: 1 }}
                >
                  <CloseIcon fontSize="small" />
                </IconButton>
              </Box>
            </MenuItem>
          ))
        )}

        {!isConnected && [
          <Divider key="divider" />,
          <MenuItem key="offline-status" disabled>
            <Typography variant="caption" color="error">
              ⚠️ Desconectado do servidor de notificações
            </Typography>
          </MenuItem>
        ]}
      </Menu>
    </>
  );
};

export default NotificationCenter;
