{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\\\xC1rea de Trabalho\\\\VER\\\\admin_panel\\\\src\\\\components\\\\ConversationDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Box, Typography, TextField, Button, Paper, Avatar, Chip, IconButton, Divider, List, ListItem, ListItemText, ListItemAvatar, CircularProgress } from '@mui/material';\nimport { Close as CloseIcon, Send as SendIcon, Person as PersonIcon, SmartToy as BotIcon, Phone as PhoneIcon, AccessTime as TimeIcon } from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport { ptBR } from 'date-fns/locale';\nimport axiosInstance from '../api/axiosInstance';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConversationDetail = ({\n  open,\n  onClose,\n  conversation\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [sending, setSending] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    if (open && conversation) {\n      fetchMessages();\n    }\n  }, [open, conversation]);\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const fetchMessages = async () => {\n    if (!(conversation !== null && conversation !== void 0 && conversation.id)) return;\n    setLoading(true);\n    try {\n      const response = await axiosInstance.get(`/conversations/${conversation.id}/messages`);\n      setMessages(response.data || []);\n    } catch (error) {\n      console.error('Erro ao buscar mensagens:', error);\n      setMessages([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const sendMessage = async () => {\n    if (!newMessage.trim() || sending) return;\n    setSending(true);\n    try {\n      const response = await axiosInstance.post('/chat/', {\n        query: newMessage,\n        contact_id: conversation.contact_id,\n        contact_name: conversation.contact_name\n      });\n\n      // Adicionar mensagem do usuário\n      const userMessage = {\n        id: Date.now(),\n        sender: 'user',\n        content: newMessage,\n        created_at: new Date().toISOString()\n      };\n\n      // Adicionar resposta da IA\n      const aiMessage = {\n        id: Date.now() + 1,\n        sender: 'ai',\n        content: response.data.response,\n        created_at: new Date().toISOString()\n      };\n      setMessages(prev => [...prev, userMessage, aiMessage]);\n      setNewMessage('');\n    } catch (error) {\n      console.error('Erro ao enviar mensagem:', error);\n    } finally {\n      setSending(false);\n    }\n  };\n  const handleKeyPress = event => {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      sendMessage();\n    }\n  };\n  const formatMessageTime = timestamp => {\n    try {\n      const date = new Date(timestamp);\n      return format(date, 'HH:mm', {\n        locale: ptBR\n      });\n    } catch {\n      return '';\n    }\n  };\n  const getMessageAvatar = sender => {\n    if (sender === 'user') {\n      return /*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: 'primary.main',\n          width: 32,\n          height: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(PersonIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(Avatar, {\n        sx: {\n          bgcolor: 'secondary.main',\n          width: 32,\n          height: 32\n        },\n        children: /*#__PURE__*/_jsxDEV(BotIcon, {\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this);\n    }\n  };\n  if (!conversation) return null;\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    PaperProps: {\n      sx: {\n        height: '80vh',\n        display: 'flex',\n        flexDirection: 'column'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      sx: {\n        pb: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            sx: {\n              bgcolor: 'primary.main'\n            },\n            children: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              children: conversation.contact_name || 'Contato'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(PhoneIcon, {\n                fontSize: \"small\",\n                color: \"action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: conversation.contact_id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [conversation.requires_human_attention && /*#__PURE__*/_jsxDEV(Chip, {\n            label: \"Requer Aten\\xE7\\xE3o\",\n            color: \"warning\",\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: onClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      sx: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        p: 0\n      },\n      children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          flex: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(List, {\n        sx: {\n          flex: 1,\n          overflow: 'auto',\n          px: 2\n        },\n        children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            py: 4\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            children: \"Nenhuma mensagem encontrada\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 15\n        }, this) : messages.map((message, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n          sx: {\n            flexDirection: message.sender === 'user' ? 'row-reverse' : 'row',\n            alignItems: 'flex-start',\n            gap: 1,\n            mb: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n            sx: {\n              minWidth: 'auto'\n            },\n            children: getMessageAvatar(message.sender)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 1,\n            sx: {\n              p: 2,\n              maxWidth: '70%',\n              bgcolor: message.sender === 'user' ? 'primary.light' : 'grey.100',\n              color: message.sender === 'user' ? 'primary.contrastText' : 'text.primary'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 0.5\n              },\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 0.5\n              },\n              children: [/*#__PURE__*/_jsxDEV(TimeIcon, {\n                fontSize: \"small\",\n                sx: {\n                  opacity: 0.7\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  opacity: 0.7\n                },\n                children: formatMessageTime(message.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 19\n          }, this)]\n        }, message.id || index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 17\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      sx: {\n        p: 2,\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        fullWidth: true,\n        multiline: true,\n        maxRows: 3,\n        placeholder: \"Digite sua mensagem...\",\n        value: newMessage,\n        onChange: e => setNewMessage(e.target.value),\n        onKeyPress: handleKeyPress,\n        disabled: sending,\n        variant: \"outlined\",\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: sendMessage,\n        disabled: !newMessage.trim() || sending,\n        sx: {\n          minWidth: 'auto',\n          px: 2\n        },\n        children: sending ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 22\n        }, this) : /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(ConversationDetail, \"rDnIO1JSQBDn3AOb1onEcsenjkk=\");\n_c = ConversationDetail;\nexport default ConversationDetail;\nvar _c;\n$RefreshReg$(_c, \"ConversationDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Box", "Typography", "TextField", "<PERSON><PERSON>", "Paper", "Avatar", "Chip", "IconButton", "Divider", "List", "ListItem", "ListItemText", "ListItemAvatar", "CircularProgress", "Close", "CloseIcon", "Send", "SendIcon", "Person", "PersonIcon", "SmartToy", "BotIcon", "Phone", "PhoneIcon", "AccessTime", "TimeIcon", "format", "ptBR", "axiosInstance", "jsxDEV", "_jsxDEV", "ConversationDetail", "open", "onClose", "conversation", "_s", "messages", "setMessages", "newMessage", "setNewMessage", "loading", "setLoading", "sending", "setSending", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "fetchMessages", "id", "response", "get", "data", "error", "console", "sendMessage", "trim", "post", "query", "contact_id", "contact_name", "userMessage", "Date", "now", "sender", "content", "created_at", "toISOString", "aiMessage", "prev", "handleKeyPress", "event", "key", "shift<PERSON>ey", "preventDefault", "formatMessageTime", "timestamp", "date", "locale", "getMessageAvatar", "sx", "bgcolor", "width", "height", "children", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "fullWidth", "PaperProps", "display", "flexDirection", "pb", "alignItems", "justifyContent", "gap", "variant", "color", "requires_human_attention", "label", "size", "onClick", "flex", "p", "overflow", "px", "length", "textAlign", "py", "map", "message", "index", "mb", "min<PERSON><PERSON><PERSON>", "elevation", "opacity", "ref", "multiline", "maxRows", "placeholder", "value", "onChange", "e", "target", "onKeyPress", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/<PERSON><PERSON>/VER/admin_panel/src/components/ConversationDetail.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Box,\n  Typography,\n  TextField,\n  Button,\n  Paper,\n  Avatar,\n  Chip,\n  IconButton,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  CircularProgress\n} from '@mui/material';\nimport {\n  Close as CloseIcon,\n  Send as SendIcon,\n  Person as PersonIcon,\n  SmartToy as BotIcon,\n  Phone as PhoneIcon,\n  AccessTime as TimeIcon\n} from '@mui/icons-material';\nimport { format } from 'date-fns';\nimport { ptBR } from 'date-fns/locale';\nimport axiosInstance from '../api/axiosInstance';\n\nconst ConversationDetail = ({ open, onClose, conversation }) => {\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [sending, setSending] = useState(false);\n  const messagesEndRef = useRef(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\n  };\n\n  useEffect(() => {\n    if (open && conversation) {\n      fetchMessages();\n    }\n  }, [open, conversation]);\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const fetchMessages = async () => {\n    if (!conversation?.id) return;\n    \n    setLoading(true);\n    try {\n      const response = await axiosInstance.get(`/conversations/${conversation.id}/messages`);\n      setMessages(response.data || []);\n    } catch (error) {\n      console.error('Erro ao buscar mensagens:', error);\n      setMessages([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendMessage = async () => {\n    if (!newMessage.trim() || sending) return;\n\n    setSending(true);\n    try {\n      const response = await axiosInstance.post('/chat/', {\n        query: newMessage,\n        contact_id: conversation.contact_id,\n        contact_name: conversation.contact_name\n      });\n\n      // Adicionar mensagem do usuário\n      const userMessage = {\n        id: Date.now(),\n        sender: 'user',\n        content: newMessage,\n        created_at: new Date().toISOString()\n      };\n\n      // Adicionar resposta da IA\n      const aiMessage = {\n        id: Date.now() + 1,\n        sender: 'ai',\n        content: response.data.response,\n        created_at: new Date().toISOString()\n      };\n\n      setMessages(prev => [...prev, userMessage, aiMessage]);\n      setNewMessage('');\n    } catch (error) {\n      console.error('Erro ao enviar mensagem:', error);\n    } finally {\n      setSending(false);\n    }\n  };\n\n  const handleKeyPress = (event) => {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const formatMessageTime = (timestamp) => {\n    try {\n      const date = new Date(timestamp);\n      return format(date, 'HH:mm', { locale: ptBR });\n    } catch {\n      return '';\n    }\n  };\n\n  const getMessageAvatar = (sender) => {\n    if (sender === 'user') {\n      return (\n        <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>\n          <PersonIcon fontSize=\"small\" />\n        </Avatar>\n      );\n    } else {\n      return (\n        <Avatar sx={{ bgcolor: 'secondary.main', width: 32, height: 32 }}>\n          <BotIcon fontSize=\"small\" />\n        </Avatar>\n      );\n    }\n  };\n\n  if (!conversation) return null;\n\n  return (\n    <Dialog\n      open={open}\n      onClose={onClose}\n      maxWidth=\"md\"\n      fullWidth\n      PaperProps={{\n        sx: { height: '80vh', display: 'flex', flexDirection: 'column' }\n      }}\n    >\n      <DialogTitle sx={{ pb: 1 }}>\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <Avatar sx={{ bgcolor: 'primary.main' }}>\n              <PersonIcon />\n            </Avatar>\n            <Box>\n              <Typography variant=\"h6\">\n                {conversation.contact_name || 'Contato'}\n              </Typography>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                <PhoneIcon fontSize=\"small\" color=\"action\" />\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {conversation.contact_id}\n                </Typography>\n              </Box>\n            </Box>\n          </Box>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            {conversation.requires_human_attention && (\n              <Chip\n                label=\"Requer Atenção\"\n                color=\"warning\"\n                size=\"small\"\n              />\n            )}\n            <IconButton onClick={onClose}>\n              <CloseIcon />\n            </IconButton>\n          </Box>\n        </Box>\n      </DialogTitle>\n\n      <Divider />\n\n      <DialogContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', p: 0 }}>\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flex: 1 }}>\n            <CircularProgress />\n          </Box>\n        ) : (\n          <List sx={{ flex: 1, overflow: 'auto', px: 2 }}>\n            {messages.length === 0 ? (\n              <Box sx={{ textAlign: 'center', py: 4 }}>\n                <Typography color=\"text.secondary\">\n                  Nenhuma mensagem encontrada\n                </Typography>\n              </Box>\n            ) : (\n              messages.map((message, index) => (\n                <ListItem\n                  key={message.id || index}\n                  sx={{\n                    flexDirection: message.sender === 'user' ? 'row-reverse' : 'row',\n                    alignItems: 'flex-start',\n                    gap: 1,\n                    mb: 1\n                  }}\n                >\n                  <ListItemAvatar sx={{ minWidth: 'auto' }}>\n                    {getMessageAvatar(message.sender)}\n                  </ListItemAvatar>\n                  <Paper\n                    elevation={1}\n                    sx={{\n                      p: 2,\n                      maxWidth: '70%',\n                      bgcolor: message.sender === 'user' ? 'primary.light' : 'grey.100',\n                      color: message.sender === 'user' ? 'primary.contrastText' : 'text.primary'\n                    }}\n                  >\n                    <Typography variant=\"body1\" sx={{ mb: 0.5 }}>\n                      {message.content}\n                    </Typography>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n                      <TimeIcon fontSize=\"small\" sx={{ opacity: 0.7 }} />\n                      <Typography variant=\"caption\" sx={{ opacity: 0.7 }}>\n                        {formatMessageTime(message.created_at)}\n                      </Typography>\n                    </Box>\n                  </Paper>\n                </ListItem>\n              ))\n            )}\n            <div ref={messagesEndRef} />\n          </List>\n        )}\n      </DialogContent>\n\n      <Divider />\n\n      <DialogActions sx={{ p: 2, gap: 1 }}>\n        <TextField\n          fullWidth\n          multiline\n          maxRows={3}\n          placeholder=\"Digite sua mensagem...\"\n          value={newMessage}\n          onChange={(e) => setNewMessage(e.target.value)}\n          onKeyPress={handleKeyPress}\n          disabled={sending}\n          variant=\"outlined\"\n          size=\"small\"\n        />\n        <Button\n          variant=\"contained\"\n          onClick={sendMessage}\n          disabled={!newMessage.trim() || sending}\n          sx={{ minWidth: 'auto', px: 2 }}\n        >\n          {sending ? <CircularProgress size={20} /> : <SendIcon />}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default ConversationDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,MAAM,EACNC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,gBAAgB,QACX,eAAe;AACtB,SACEC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,OAAO,EACnBC,KAAK,IAAIC,SAAS,EAClBC,UAAU,IAAIC,QAAQ,QACjB,qBAAqB;AAC5B,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,IAAI,QAAQ,iBAAiB;AACtC,OAAOC,aAAa,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMmD,cAAc,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMkD,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDvD,SAAS,CAAC,MAAM;IACd,IAAIsC,IAAI,IAAIE,YAAY,EAAE;MACxBgB,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAClB,IAAI,EAAEE,YAAY,CAAC,CAAC;EAExBxC,SAAS,CAAC,MAAM;IACdmD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;EAEd,MAAMc,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,EAAChB,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEiB,EAAE,GAAE;IAEvBV,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMxB,aAAa,CAACyB,GAAG,CAAC,kBAAkBnB,YAAY,CAACiB,EAAE,WAAW,CAAC;MACtFd,WAAW,CAACe,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDlB,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,SAAS;MACRI,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACnB,UAAU,CAACoB,IAAI,CAAC,CAAC,IAAIhB,OAAO,EAAE;IAEnCC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMxB,aAAa,CAAC+B,IAAI,CAAC,QAAQ,EAAE;QAClDC,KAAK,EAAEtB,UAAU;QACjBuB,UAAU,EAAE3B,YAAY,CAAC2B,UAAU;QACnCC,YAAY,EAAE5B,YAAY,CAAC4B;MAC7B,CAAC,CAAC;;MAEF;MACA,MAAMC,WAAW,GAAG;QAClBZ,EAAE,EAAEa,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE7B,UAAU;QACnB8B,UAAU,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;MACrC,CAAC;;MAED;MACA,MAAMC,SAAS,GAAG;QAChBnB,EAAE,EAAEa,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAEf,QAAQ,CAACE,IAAI,CAACF,QAAQ;QAC/BgB,UAAU,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;MACrC,CAAC;MAEDhC,WAAW,CAACkC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,WAAW,EAAEO,SAAS,CAAC,CAAC;MACtD/B,aAAa,CAAC,EAAE,CAAC;IACnB,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,cAAc,GAAIC,KAAK,IAAK;IAChC,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,KAAK,CAACE,QAAQ,EAAE;MAC5CF,KAAK,CAACG,cAAc,CAAC,CAAC;MACtBnB,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMoB,iBAAiB,GAAIC,SAAS,IAAK;IACvC,IAAI;MACF,MAAMC,IAAI,GAAG,IAAIf,IAAI,CAACc,SAAS,CAAC;MAChC,OAAOpD,MAAM,CAACqD,IAAI,EAAE,OAAO,EAAE;QAAEC,MAAM,EAAErD;MAAK,CAAC,CAAC;IAChD,CAAC,CAAC,MAAM;MACN,OAAO,EAAE;IACX;EACF,CAAC;EAED,MAAMsD,gBAAgB,GAAIf,MAAM,IAAK;IACnC,IAAIA,MAAM,KAAK,MAAM,EAAE;MACrB,oBACEpC,OAAA,CAACzB,MAAM;QAAC6E,EAAE,EAAE;UAAEC,OAAO,EAAE,cAAc;UAAEC,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAG,CAAE;QAAAC,QAAA,eAC7DxD,OAAA,CAACX,UAAU;UAACoE,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAEb,CAAC,MAAM;MACL,oBACE7D,OAAA,CAACzB,MAAM;QAAC6E,EAAE,EAAE;UAAEC,OAAO,EAAE,gBAAgB;UAAEC,KAAK,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAG,CAAE;QAAAC,QAAA,eAC/DxD,OAAA,CAACT,OAAO;UAACkE,QAAQ,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAEb;EACF,CAAC;EAED,IAAI,CAACzD,YAAY,EAAE,OAAO,IAAI;EAE9B,oBACEJ,OAAA,CAAClC,MAAM;IACLoC,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEA,OAAQ;IACjB2D,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,UAAU,EAAE;MACVZ,EAAE,EAAE;QAAEG,MAAM,EAAE,MAAM;QAAEU,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAS;IACjE,CAAE;IAAAV,QAAA,gBAEFxD,OAAA,CAACjC,WAAW;MAACqF,EAAE,EAAE;QAAEe,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,eACzBxD,OAAA,CAAC9B,GAAG;QAACkF,EAAE,EAAE;UAAEa,OAAO,EAAE,MAAM;UAAEG,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAb,QAAA,gBAClFxD,OAAA,CAAC9B,GAAG;UAACkF,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEG,UAAU,EAAE,QAAQ;YAAEE,GAAG,EAAE;UAAE,CAAE;UAAAd,QAAA,gBACzDxD,OAAA,CAACzB,MAAM;YAAC6E,EAAE,EAAE;cAAEC,OAAO,EAAE;YAAe,CAAE;YAAAG,QAAA,eACtCxD,OAAA,CAACX,UAAU;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACT7D,OAAA,CAAC9B,GAAG;YAAAsF,QAAA,gBACFxD,OAAA,CAAC7B,UAAU;cAACoG,OAAO,EAAC,IAAI;cAAAf,QAAA,EACrBpD,YAAY,CAAC4B,YAAY,IAAI;YAAS;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACb7D,OAAA,CAAC9B,GAAG;cAACkF,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEG,UAAU,EAAE,QAAQ;gBAAEE,GAAG,EAAE;cAAE,CAAE;cAAAd,QAAA,gBACzDxD,OAAA,CAACP,SAAS;gBAACgE,QAAQ,EAAC,OAAO;gBAACe,KAAK,EAAC;cAAQ;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7C7D,OAAA,CAAC7B,UAAU;gBAACoG,OAAO,EAAC,OAAO;gBAACC,KAAK,EAAC,gBAAgB;gBAAAhB,QAAA,EAC/CpD,YAAY,CAAC2B;cAAU;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN7D,OAAA,CAAC9B,GAAG;UAACkF,EAAE,EAAE;YAAEa,OAAO,EAAE,MAAM;YAAEG,UAAU,EAAE,QAAQ;YAAEE,GAAG,EAAE;UAAE,CAAE;UAAAd,QAAA,GACxDpD,YAAY,CAACqE,wBAAwB,iBACpCzE,OAAA,CAACxB,IAAI;YACHkG,KAAK,EAAC,sBAAgB;YACtBF,KAAK,EAAC,SAAS;YACfG,IAAI,EAAC;UAAO;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACF,eACD7D,OAAA,CAACvB,UAAU;YAACmG,OAAO,EAAEzE,OAAQ;YAAAqD,QAAA,eAC3BxD,OAAA,CAACf,SAAS;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd7D,OAAA,CAACtB,OAAO;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEX7D,OAAA,CAAChC,aAAa;MAACoF,EAAE,EAAE;QAAEyB,IAAI,EAAE,CAAC;QAAEZ,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEY,CAAC,EAAE;MAAE,CAAE;MAAAtB,QAAA,EAC5E9C,OAAO,gBACNV,OAAA,CAAC9B,GAAG;QAACkF,EAAE,EAAE;UAAEa,OAAO,EAAE,MAAM;UAAEI,cAAc,EAAE,QAAQ;UAAED,UAAU,EAAE,QAAQ;UAAES,IAAI,EAAE;QAAE,CAAE;QAAArB,QAAA,eACpFxD,OAAA,CAACjB,gBAAgB;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,gBAEN7D,OAAA,CAACrB,IAAI;QAACyE,EAAE,EAAE;UAAEyB,IAAI,EAAE,CAAC;UAAEE,QAAQ,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,GAC5ClD,QAAQ,CAAC2E,MAAM,KAAK,CAAC,gBACpBjF,OAAA,CAAC9B,GAAG;UAACkF,EAAE,EAAE;YAAE8B,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eACtCxD,OAAA,CAAC7B,UAAU;YAACqG,KAAK,EAAC,gBAAgB;YAAAhB,QAAA,EAAC;UAEnC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,GAENvD,QAAQ,CAAC8E,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC1BtF,OAAA,CAACpB,QAAQ;UAEPwE,EAAE,EAAE;YACFc,aAAa,EAAEmB,OAAO,CAACjD,MAAM,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK;YAChEgC,UAAU,EAAE,YAAY;YACxBE,GAAG,EAAE,CAAC;YACNiB,EAAE,EAAE;UACN,CAAE;UAAA/B,QAAA,gBAEFxD,OAAA,CAAClB,cAAc;YAACsE,EAAE,EAAE;cAAEoC,QAAQ,EAAE;YAAO,CAAE;YAAAhC,QAAA,EACtCL,gBAAgB,CAACkC,OAAO,CAACjD,MAAM;UAAC;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACjB7D,OAAA,CAAC1B,KAAK;YACJmH,SAAS,EAAE,CAAE;YACbrC,EAAE,EAAE;cACF0B,CAAC,EAAE,CAAC;cACJhB,QAAQ,EAAE,KAAK;cACfT,OAAO,EAAEgC,OAAO,CAACjD,MAAM,KAAK,MAAM,GAAG,eAAe,GAAG,UAAU;cACjEoC,KAAK,EAAEa,OAAO,CAACjD,MAAM,KAAK,MAAM,GAAG,sBAAsB,GAAG;YAC9D,CAAE;YAAAoB,QAAA,gBAEFxD,OAAA,CAAC7B,UAAU;cAACoG,OAAO,EAAC,OAAO;cAACnB,EAAE,EAAE;gBAAEmC,EAAE,EAAE;cAAI,CAAE;cAAA/B,QAAA,EACzC6B,OAAO,CAAChD;YAAO;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACb7D,OAAA,CAAC9B,GAAG;cAACkF,EAAE,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEG,UAAU,EAAE,QAAQ;gBAAEE,GAAG,EAAE;cAAI,CAAE;cAAAd,QAAA,gBAC3DxD,OAAA,CAACL,QAAQ;gBAAC8D,QAAQ,EAAC,OAAO;gBAACL,EAAE,EAAE;kBAAEsC,OAAO,EAAE;gBAAI;cAAE;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnD7D,OAAA,CAAC7B,UAAU;gBAACoG,OAAO,EAAC,SAAS;gBAACnB,EAAE,EAAE;kBAAEsC,OAAO,EAAE;gBAAI,CAAE;gBAAAlC,QAAA,EAChDT,iBAAiB,CAACsC,OAAO,CAAC/C,UAAU;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GA7BHwB,OAAO,CAAChE,EAAE,IAAIiE,KAAK;UAAA5B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BhB,CACX,CACF,eACD7D,OAAA;UAAK2F,GAAG,EAAE7E;QAAe;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB;IACP;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eAEhB7D,OAAA,CAACtB,OAAO;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEX7D,OAAA,CAAC/B,aAAa;MAACmF,EAAE,EAAE;QAAE0B,CAAC,EAAE,CAAC;QAAER,GAAG,EAAE;MAAE,CAAE;MAAAd,QAAA,gBAClCxD,OAAA,CAAC5B,SAAS;QACR2F,SAAS;QACT6B,SAAS;QACTC,OAAO,EAAE,CAAE;QACXC,WAAW,EAAC,wBAAwB;QACpCC,KAAK,EAAEvF,UAAW;QAClBwF,QAAQ,EAAGC,CAAC,IAAKxF,aAAa,CAACwF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;QAC/CI,UAAU,EAAEzD,cAAe;QAC3B0D,QAAQ,EAAExF,OAAQ;QAClB2D,OAAO,EAAC,UAAU;QAClBI,IAAI,EAAC;MAAO;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eACF7D,OAAA,CAAC3B,MAAM;QACLkG,OAAO,EAAC,WAAW;QACnBK,OAAO,EAAEjD,WAAY;QACrByE,QAAQ,EAAE,CAAC5F,UAAU,CAACoB,IAAI,CAAC,CAAC,IAAIhB,OAAQ;QACxCwC,EAAE,EAAE;UAAEoC,QAAQ,EAAE,MAAM;UAAER,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,EAE/B5C,OAAO,gBAAGZ,OAAA,CAACjB,gBAAgB;UAAC4F,IAAI,EAAE;QAAG;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG7D,OAAA,CAACb,QAAQ;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACxD,EAAA,CAvOIJ,kBAAkB;AAAAoG,EAAA,GAAlBpG,kBAAkB;AAyOxB,eAAeA,kBAAkB;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}