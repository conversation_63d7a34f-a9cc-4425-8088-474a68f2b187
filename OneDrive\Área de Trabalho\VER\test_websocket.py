#!/usr/bin/env python3
"""
Script para testar WebSocket e notificações em tempo real.
"""
import asyncio
import websockets
import json
import time
from datetime import datetime, timezone

async def test_websocket_connection():
    """Testa conexão WebSocket básica."""
    uri = "ws://localhost:8000/ws"
    
    try:
        print("🔌 Conectando ao WebSocket...")
        async with websockets.connect(uri) as websocket:
            print("✅ Conectado ao WebSocket!")
            
            # Enviar ping
            await websocket.send("ping")
            response = await websocket.recv()
            print(f"📨 Resposta ao ping: {response}")
            
            # Enviar mensagem de teste
            test_message = "Mensagem de teste do Python"
            await websocket.send(test_message)
            print(f"📤 Enviado: {test_message}")
            
            # Aguardar resposta
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"📨 Resposta: {response}")
            except asyncio.TimeoutError:
                print("⏰ Timeout aguardando resposta")
            
            print("✅ Teste de conexão WebSocket concluído")
            
    except Exception as e:
        print(f"❌ Erro na conexão WebSocket: {e}")

async def test_notification_system():
    """Testa sistema de notificações."""
    import requests
    
    # Obter token de autenticação
    try:
        response = requests.post(
            "http://localhost:8000/token",
            data={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code != 200:
            print(f"❌ Erro no login: {response.text}")
            return
            
        token = response.json()['access_token']
        print(f"✅ Token obtido: {token[:50]}...")
        
    except Exception as e:
        print(f"❌ Erro na autenticação: {e}")
        return
    
    # Conectar ao WebSocket e aguardar notificações
    uri = "ws://localhost:8000/ws"
    
    try:
        print("🔌 Conectando ao WebSocket para aguardar notificações...")
        async with websockets.connect(uri) as websocket:
            print("✅ Conectado! Aguardando notificações...")
            
            # Criar uma tarefa para enviar mensagens via API
            async def send_test_messages():
                await asyncio.sleep(2)  # Aguardar conexão estabilizar
                
                headers = {"Authorization": f"Bearer {token}"}
                
                # Enviar algumas mensagens de teste
                test_messages = [
                    {
                        "query": "Primeira mensagem de teste",
                        "contact_id": f"test_notif_{int(time.time())}<EMAIL>",
                        "contact_name": "Teste Notificação 1"
                    },
                    {
                        "query": "Segunda mensagem de teste",
                        "contact_id": f"test_notif_{int(time.time())}<EMAIL>",
                        "contact_name": "Teste Notificação 2"
                    }
                ]
                
                for i, message in enumerate(test_messages, 1):
                    try:
                        print(f"📤 Enviando mensagem {i} via API...")
                        response = requests.post(
                            "http://localhost:8000/chat/",
                            json=message,
                            headers=headers
                        )
                        
                        if response.status_code == 200:
                            print(f"✅ Mensagem {i} enviada com sucesso")
                        else:
                            print(f"❌ Erro ao enviar mensagem {i}: {response.status_code}")
                            
                        await asyncio.sleep(3)  # Pausa entre mensagens
                        
                    except Exception as e:
                        print(f"❌ Erro ao enviar mensagem {i}: {e}")
            
            # Iniciar tarefa de envio de mensagens
            send_task = asyncio.create_task(send_test_messages())
            
            # Aguardar notificações por 30 segundos
            timeout = 30
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    # Aguardar mensagem com timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    
                    try:
                        # Tentar parsear como JSON
                        data = json.loads(message)
                        print(f"📨 Notificação recebida:")
                        print(f"   Tipo: {data.get('type', 'N/A')}")
                        print(f"   Contato: {data.get('contact_name', 'N/A')}")
                        print(f"   Mensagem: {data.get('message', 'N/A')}")
                        print(f"   Timestamp: {data.get('timestamp', 'N/A')}")
                    except json.JSONDecodeError:
                        print(f"📨 Mensagem de texto: {message}")
                        
                except asyncio.TimeoutError:
                    # Timeout normal, continuar aguardando
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print("❌ Conexão WebSocket fechada")
                    break
            
            # Aguardar tarefa de envio terminar
            await send_task
            
            print("✅ Teste de notificações concluído")
            
    except Exception as e:
        print(f"❌ Erro no teste de notificações: {e}")

async def main():
    print("🧪 Testando sistema WebSocket...")
    
    # Teste 1: Conexão básica
    print("\n--- Teste 1: Conexão Básica ---")
    await test_websocket_connection()
    
    # Teste 2: Sistema de notificações
    print("\n--- Teste 2: Sistema de Notificações ---")
    await test_notification_system()
    
    print("\n🎉 Testes WebSocket concluídos!")

if __name__ == "__main__":
    asyncio.run(main())
