#!/usr/bin/env python3

"""
Gerador de relatórios detalhados para testes do chat da persona
"""

import unittest
import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, Any

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar testes
from tests.test_performance import TestPerformance
from tests.test_coverage import TestCodeCoverage
from tests.test_quality import TestQuality
from test_persona_basic import (
    test_persona_config, test_post_processing_basic,
    test_template_matching, test_persona_consistency
)


class ReportGenerator:
    """
    Gerador de relatórios de teste
    """
    
    def __init__(self):
        self.reports = {}
        self.timestamp = datetime.now()
    
    def run_all_tests_with_reports(self):
        """Executa todos os testes e gera relatórios"""
        print("🧪 EXECUTANDO SUITE COMPLETA DE TESTES COM RELATÓRIOS")
        print("=" * 70)
        
        start_time = time.time()
        
        # 1. Testes básicos
        print("\n📋 1. TESTES BÁSICOS")
        print("-" * 40)
        basic_results = self._run_basic_tests()
        
        # 2. Testes de performance
        print("\n📋 2. TESTES DE PERFORMANCE")
        print("-" * 40)
        performance_results = self._run_performance_tests()
        
        # 3. Testes de cobertura
        print("\n📋 3. TESTES DE COBERTURA")
        print("-" * 40)
        coverage_results = self._run_coverage_tests()
        
        # 4. Testes de qualidade
        print("\n📋 4. TESTES DE QUALIDADE")
        print("-" * 40)
        quality_results = self._run_quality_tests()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Compilar relatório final
        self.reports = {
            'metadata': {
                'timestamp': self.timestamp.isoformat(),
                'duration': total_duration,
                'version': '1.0.0'
            },
            'basic_tests': basic_results,
            'performance': performance_results,
            'coverage': coverage_results,
            'quality': quality_results
        }
        
        # Gerar relatórios
        self._generate_console_report()
        self._generate_json_report()
        self._generate_html_report()
        self._generate_summary_report()
        
        return self.reports
    
    def _run_basic_tests(self):
        """Executa testes básicos"""
        results = {}
        
        try:
            print("   🧪 Configuração da persona...")
            results['persona_config'] = test_persona_config()
            print("   ✅ Concluído")
        except Exception as e:
            results['persona_config'] = False
            print(f"   ❌ Erro: {e}")
        
        try:
            print("   🧪 Pós-processamento...")
            results['post_processing'] = test_post_processing_basic()
            print("   ✅ Concluído")
        except Exception as e:
            results['post_processing'] = False
            print(f"   ❌ Erro: {e}")
        
        try:
            print("   🧪 Template matching...")
            results['template_matching'] = test_template_matching()
            print("   ✅ Concluído")
        except Exception as e:
            results['template_matching'] = False
            print(f"   ❌ Erro: {e}")
        
        try:
            print("   🧪 Consistência da persona...")
            results['persona_consistency'] = test_persona_consistency()
            print("   ✅ Concluído")
        except Exception as e:
            results['persona_consistency'] = False
            print(f"   ❌ Erro: {e}")
        
        return results
    
    def _run_performance_tests(self):
        """Executa testes de performance"""
        try:
            suite = unittest.TestLoader().loadTestsFromTestCase(TestPerformance)
            runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
            result = runner.run(suite)
            
            # Obter resultados de performance
            test_instance = TestPerformance()
            test_instance.setUp()
            
            # Executar testes individuais para obter métricas
            test_instance.test_post_processing_speed()
            test_instance.test_template_matching_speed()
            test_instance.test_concurrent_processing()
            test_instance.test_memory_usage()
            test_instance.test_stress_template_matching()
            test_instance.test_emoji_hashtag_performance()
            
            return test_instance.get_performance_report()
            
        except Exception as e:
            print(f"   ❌ Erro nos testes de performance: {e}")
            return {'error': str(e)}
    
    def _run_coverage_tests(self):
        """Executa testes de cobertura"""
        try:
            suite = unittest.TestLoader().loadTestsFromTestCase(TestCodeCoverage)
            runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
            result = runner.run(suite)
            
            # Obter resultados de cobertura
            test_instance = TestCodeCoverage()
            test_instance.setUp()
            
            # Executar testes individuais
            test_instance.test_all_template_keywords()
            test_instance.test_all_emojis_usage()
            test_instance.test_all_hashtags_usage()
            test_instance.test_edge_cases_coverage()
            test_instance.test_function_coverage()
            
            return test_instance.get_coverage_report()
            
        except Exception as e:
            print(f"   ❌ Erro nos testes de cobertura: {e}")
            return {'error': str(e)}
    
    def _run_quality_tests(self):
        """Executa testes de qualidade"""
        try:
            suite = unittest.TestLoader().loadTestsFromTestCase(TestQuality)
            runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
            result = runner.run(suite)
            
            # Obter resultados de qualidade
            test_instance = TestQuality()
            test_instance.setUp()
            
            # Executar testes individuais
            test_instance.test_response_quality_metrics()
            test_instance.test_persona_consistency_metrics()
            test_instance.test_accessibility_compliance()
            test_instance.test_technical_compliance()
            
            return test_instance.get_quality_report()
            
        except Exception as e:
            print(f"   ❌ Erro nos testes de qualidade: {e}")
            return {'error': str(e)}
    
    def _generate_console_report(self):
        """Gera relatório no console"""
        print("\n" + "=" * 70)
        print("📊 RELATÓRIO FINAL DETALHADO")
        print("=" * 70)
        
        # Resumo geral
        basic_passed = sum(1 for result in self.reports['basic_tests'].values() if result)
        basic_total = len(self.reports['basic_tests'])
        
        print(f"\n⏱️  Tempo total de execução: {self.reports['metadata']['duration']:.2f} segundos")
        print(f"📅 Data/hora: {self.timestamp.strftime('%d/%m/%Y %H:%M:%S')}")
        print(f"🧪 Testes básicos: {basic_passed}/{basic_total} passaram")
        
        # Performance
        if 'error' not in self.reports['performance']:
            print(f"\n📈 PERFORMANCE:")
            perf = self.reports['performance']
            if 'post_processing' in perf:
                avg_time = perf['post_processing']['avg_time'] * 1000
                print(f"   Pós-processamento: {avg_time:.1f}ms (média)")
            if 'template_matching' in perf:
                match_rate = perf['template_matching']['match_rate']
                print(f"   Template matching: {match_rate:.1%} taxa de acerto")
            if 'concurrent_processing' in perf:
                throughput = perf['concurrent_processing']['throughput']
                print(f"   Throughput: {throughput:.1f} req/s")
        
        # Cobertura
        if 'error' not in self.reports['coverage']:
            print(f"\n📋 COBERTURA:")
            cov = self.reports['coverage']
            if 'template_keywords' in cov:
                coverage_rate = cov['template_keywords']['coverage_rate']
                print(f"   Keywords: {coverage_rate:.1%}")
            if 'emoji_usage' in cov:
                emoji_coverage = cov['emoji_usage']['coverage_rate']
                print(f"   Emojis: {emoji_coverage:.1%}")
            if 'function_coverage' in cov:
                func_coverage = cov['function_coverage']['coverage_rate']
                print(f"   Funções: {func_coverage:.1%}")
        
        # Qualidade
        if 'error' not in self.reports['quality']:
            print(f"\n🎯 QUALIDADE:")
            qual = self.reports['quality']
            if 'response_quality' in qual:
                percentages = qual['response_quality']['percentages']
                avg_quality = sum(percentages.values()) / len(percentages)
                print(f"   Qualidade das respostas: {avg_quality:.1f}%")
            if 'technical_compliance' in qual:
                compliance_score = qual['technical_compliance']['score']
                print(f"   Conformidade técnica: {compliance_score:.0f}%")
        
        print("\n" + "=" * 70)
    
    def _generate_json_report(self):
        """Gera relatório em JSON"""
        filename = f"test_report_{self.timestamp.strftime('%Y%m%d_%H%M%S')}.json"
        filepath = os.path.join(os.path.dirname(__file__), 'reports', filename)
        
        # Criar diretório se não existir
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.reports, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📄 Relatório JSON salvo: {filepath}")
    
    def _generate_html_report(self):
        """Gera relatório em HTML"""
        filename = f"test_report_{self.timestamp.strftime('%Y%m%d_%H%M%S')}.html"
        filepath = os.path.join(os.path.dirname(__file__), 'reports', filename)
        
        # Criar diretório se não existir
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        html_content = self._generate_html_content()
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"🌐 Relatório HTML salvo: {filepath}")
    
    def _generate_html_content(self):
        """Gera conteúdo HTML do relatório"""
        basic_passed = sum(1 for result in self.reports['basic_tests'].values() if result)
        basic_total = len(self.reports['basic_tests'])
        
        html = f"""
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Testes - Chat da Persona</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 20px; }}
        .section {{ margin: 20px 0; padding: 15px; border-left: 4px solid #4CAF50; background-color: #f9f9f9; }}
        .metric {{ display: inline-block; margin: 10px; padding: 10px; background: #e8f5e8; border-radius: 5px; }}
        .success {{ color: #4CAF50; }}
        .warning {{ color: #FF9800; }}
        .error {{ color: #F44336; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #4CAF50; color: white; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Relatório de Testes - Chat da Persona</h1>
            <p>Gerado em: {self.timestamp.strftime('%d/%m/%Y %H:%M:%S')}</p>
            <p>Duração: {self.reports['metadata']['duration']:.2f} segundos</p>
        </div>
        
        <div class="section">
            <h2>📊 Resumo Geral</h2>
            <div class="metric">
                <strong>Testes Básicos:</strong> 
                <span class="{'success' if basic_passed == basic_total else 'warning'}">{basic_passed}/{basic_total}</span>
            </div>
        </div>
        
        <div class="section">
            <h2>🧪 Testes Básicos</h2>
            <table>
                <tr><th>Teste</th><th>Status</th></tr>
        """
        
        for test_name, result in self.reports['basic_tests'].items():
            status_class = "success" if result else "error"
            status_text = "✅ PASSOU" if result else "❌ FALHOU"
            html += f'<tr><td>{test_name}</td><td class="{status_class}">{status_text}</td></tr>'
        
        html += """
            </table>
        </div>
        
        <div class="section">
            <h2>📈 Performance</h2>
        """
        
        if 'error' not in self.reports['performance']:
            perf = self.reports['performance']
            html += "<table><tr><th>Métrica</th><th>Valor</th></tr>"
            
            if 'post_processing' in perf:
                avg_time = perf['post_processing']['avg_time'] * 1000
                html += f"<tr><td>Pós-processamento (média)</td><td>{avg_time:.1f}ms</td></tr>"
            
            if 'template_matching' in perf:
                match_rate = perf['template_matching']['match_rate']
                html += f"<tr><td>Taxa de acerto (templates)</td><td>{match_rate:.1%}</td></tr>"
            
            if 'concurrent_processing' in perf:
                throughput = perf['concurrent_processing']['throughput']
                html += f"<tr><td>Throughput</td><td>{throughput:.1f} req/s</td></tr>"
            
            html += "</table>"
        else:
            html += f'<p class="error">Erro: {self.reports["performance"]["error"]}</p>'
        
        html += """
        </div>
        
        <div class="section">
            <h2>📋 Cobertura</h2>
        """
        
        if 'error' not in self.reports['coverage']:
            cov = self.reports['coverage']
            html += "<table><tr><th>Área</th><th>Cobertura</th></tr>"
            
            if 'template_keywords' in cov:
                coverage_rate = cov['template_keywords']['coverage_rate']
                html += f"<tr><td>Keywords</td><td>{coverage_rate:.1%}</td></tr>"
            
            if 'emoji_usage' in cov:
                emoji_coverage = cov['emoji_usage']['coverage_rate']
                html += f"<tr><td>Emojis</td><td>{emoji_coverage:.1%}</td></tr>"
            
            if 'function_coverage' in cov:
                func_coverage = cov['function_coverage']['coverage_rate']
                html += f"<tr><td>Funções</td><td>{func_coverage:.1%}</td></tr>"
            
            html += "</table>"
        else:
            html += f'<p class="error">Erro: {self.reports["coverage"]["error"]}</p>'
        
        html += """
        </div>
        
        <div class="section">
            <h2>🎯 Qualidade</h2>
        """
        
        if 'error' not in self.reports['quality']:
            qual = self.reports['quality']
            html += "<table><tr><th>Métrica</th><th>Score</th></tr>"
            
            if 'response_quality' in qual:
                percentages = qual['response_quality']['percentages']
                for metric, percentage in percentages.items():
                    html += f"<tr><td>{metric}</td><td>{percentage:.1f}%</td></tr>"
            
            if 'technical_compliance' in qual:
                compliance_score = qual['technical_compliance']['score']
                html += f"<tr><td>Conformidade técnica</td><td>{compliance_score:.0f}%</td></tr>"
            
            html += "</table>"
        else:
            html += f'<p class="error">Erro: {self.reports["quality"]["error"]}</p>'
        
        html += """
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def _generate_summary_report(self):
        """Gera relatório resumido"""
        filename = f"summary_{self.timestamp.strftime('%Y%m%d_%H%M%S')}.txt"
        filepath = os.path.join(os.path.dirname(__file__), 'reports', filename)
        
        # Criar diretório se não existir
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        basic_passed = sum(1 for result in self.reports['basic_tests'].values() if result)
        basic_total = len(self.reports['basic_tests'])
        
        summary = f"""
RELATÓRIO RESUMIDO - CHAT DA PERSONA
====================================

Data/Hora: {self.timestamp.strftime('%d/%m/%Y %H:%M:%S')}
Duração: {self.reports['metadata']['duration']:.2f} segundos

RESULTADOS GERAIS:
- Testes básicos: {basic_passed}/{basic_total} passaram
- Taxa de sucesso: {(basic_passed/basic_total)*100:.1f}%

STATUS: {'✅ APROVADO' if basic_passed == basic_total else '⚠️ ATENÇÃO NECESSÁRIA'}

PRÓXIMOS PASSOS:
{'- Sistema pronto para produção' if basic_passed == basic_total else '- Corrigir testes que falharam'}
- Monitorar performance em produção
- Executar testes regularmente
        """
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(summary)
        
        print(f"📋 Resumo salvo: {filepath}")


def main():
    """Função principal"""
    generator = ReportGenerator()
    reports = generator.run_all_tests_with_reports()
    
    print(f"\n🎉 RELATÓRIOS GERADOS COM SUCESSO!")
    print(f"📁 Verifique a pasta 'reports' para os arquivos detalhados")
    
    return reports


if __name__ == "__main__":
    main()
