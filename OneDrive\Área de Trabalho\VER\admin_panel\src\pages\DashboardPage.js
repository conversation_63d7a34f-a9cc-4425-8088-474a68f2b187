import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Container, Typography, Grid, Paper, Box, CircularProgress,
  Alert, useTheme
} from '@mui/material';
import {
  <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip,
  ResponsiveContainer, Cell
} from 'recharts';
import PeopleIcon from '@mui/icons-material/People';
import MessageIcon from '@mui/icons-material/Message';
import DescriptionIcon from '@mui/icons-material/Description';
import TimerIcon from '@mui/icons-material/Timer';
import axiosInstance from '../api/axiosInstance';

// --- API Fetchers ---
const fetchDashboardStats = async () => {
  const { data } = await axiosInstance.get('/dashboard/stats');
  return data;
};

// Removido fetchRecentConversations - não será mais usado

// --- Sub-componentes de UI ---
const StatCard = ({ title, value, icon, unit = '', color, gradient }) => {
  const theme = useTheme();
  return (
    <Paper sx={{
      p: 3,
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between',
      height: '100%',
      minHeight: 160,
      borderRadius: 3,
      background: gradient || `linear-gradient(135deg, ${color}15 0%, ${color}08 100%)`,
      border: '1px solid',
      borderColor: 'divider',
      boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
      position: 'relative',
      overflow: 'hidden',
      transition: 'all 0.3s ease',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
      }
    }}>
      {/* Decorative element */}
      <Box sx={{
        position: 'absolute',
        top: -20,
        right: -20,
        width: 80,
        height: 80,
        borderRadius: '50%',
        background: `linear-gradient(135deg, ${color}20, ${color}10)`,
        opacity: 0.6
      }} />

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', position: 'relative', zIndex: 1 }}>
        <Typography variant="subtitle1" color="text.secondary" sx={{ fontWeight: 500 }}>
          {title}
        </Typography>
        <Box sx={{
          p: 1.5,
          borderRadius: 2,
          bgcolor: `${color}15`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          {icon}
        </Box>
      </Box>

      <Box sx={{ position: 'relative', zIndex: 1 }}>
        <Typography variant="h3" component="p" sx={{
          fontWeight: 700,
          color: 'text.primary',
          mb: 0.5
        }}>
          {value}
          {unit && (
            <Typography variant="h6" component="span" sx={{
              ml: 1,
              fontWeight: 400,
              color: 'text.secondary'
            }}>
              {unit}
            </Typography>
          )}
        </Typography>
        <Box sx={{
          width: 40,
          height: 3,
          bgcolor: color,
          borderRadius: 2,
          mt: 1
        }} />
      </Box>
    </Paper>
  );
};

const ChartContainer = ({ title, children, height = 500 }) => (
  <Paper sx={{
    p: 4,
    height: '100%',
    minHeight: height,
    display: 'flex',
    flexDirection: 'column',
    borderRadius: 3,
    boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
    background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
    border: '1px solid',
    borderColor: 'divider'
  }}>
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
      <Box sx={{
        width: 4,
        height: 24,
        bgcolor: 'primary.main',
        borderRadius: 2,
        mr: 2
      }} />
      <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
        {title}
      </Typography>
    </Box>
    <Box sx={{ flexGrow: 1 }}>
      {children}
    </Box>
  </Paper>
);

// --- Componente Principal do Dashboard ---
const DashboardPage = () => {
  const theme = useTheme();

  // Queries para buscar os dados
  const { data: stats, isLoading: isLoadingStats, isError: isErrorStats, error: errorStats } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: fetchDashboardStats,
  });

  // Removido query de conversas recentes

  // Tratamento de Loading e Erro
  if (isLoadingStats) return <CircularProgress />;
  if (isErrorStats) return <Alert severity="error">Erro ao carregar estatísticas: {errorStats.message}</Alert>;

  // Dados para o gráfico
  const dailyChartData = stats?.daily_conversations || [];

  const formatXAxis = (tickItem) => {
    const date = new Date(`${tickItem}T00:00:00`);
    return date.toLocaleDateString('pt-BR', {
      weekday: 'short',
      day: '2-digit',
      month: '2-digit'
    });
  };



  // Tooltip customizado
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const date = new Date(`${label}T00:00:00`);
      const formattedDate = date.toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });

      return (
        <Box sx={{
          bgcolor: 'background.paper',
          p: 2,
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
          border: '1px solid',
          borderColor: 'divider',
          minWidth: 200
        }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
            {formattedDate}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box sx={{
              width: 12,
              height: 12,
              bgcolor: payload[0].color,
              borderRadius: '50%'
            }} />
            <Typography variant="body2">
              <strong>{payload[0].value}</strong> conversas
            </Typography>
          </Box>
        </Box>
      );
    }
    return null;
  };
  
  // Removido formatLastMessageTime - não será mais usado

  return (
    <Box sx={{ flexGrow: 1, p: 3, backgroundColor: theme.palette.background.default }}>
      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 4, fontWeight: 'bold', color: theme.palette.text.primary }}>
          Dashboard de Análise
        </Typography>
        
        {/* Cards de Métricas */}
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Total de Conversas" value={stats?.total_conversations ?? '...'} icon={<PeopleIcon color="primary" sx={{ fontSize: 32 }} />} color={theme.palette.primary.main} />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Total de Mensagens" value={stats?.total_messages ?? '...'} icon={<MessageIcon sx={{ fontSize: 32, color: '#00C49F' }} />} color="#00C49F" />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Documentos na Base" value={stats?.total_documents ?? '...'} icon={<DescriptionIcon sx={{ fontSize: 32, color: '#FFBB28' }} />} color="#FFBB28" />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Tempo Médio de Resposta" value={stats?.avg_response_time ?? '...'} unit="segs" icon={<TimerIcon sx={{ fontSize: 32, color: '#FF8042' }} />} color="#FF8042" />
          </Grid>
        </Grid>

        {/* Gráfico Principal - Melhorado */}
        <Grid container spacing={3} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <ChartContainer title="📊 Conversas nos Últimos 7 Dias" height={650}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={dailyChartData}
                  margin={{ top: 30, right: 40, left: 20, bottom: 60 }}
                  barCategoryGap="20%"
                >
                  <defs>
                    <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor={theme.palette.primary.main} stopOpacity={1}/>
                      <stop offset="100%" stopColor={theme.palette.primary.light} stopOpacity={0.8}/>
                    </linearGradient>
                    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
                      <feDropShadow dx="0" dy="4" stdDeviation="3" floodColor="rgba(0,0,0,0.1)"/>
                    </filter>
                  </defs>

                  <CartesianGrid
                    strokeDasharray="2 4"
                    vertical={false}
                    stroke={theme.palette.divider}
                    strokeOpacity={0.3}
                  />

                  <XAxis
                    dataKey="day"
                    tickFormatter={formatXAxis}
                    tick={{
                      fill: theme.palette.text.secondary,
                      fontSize: 13,
                      fontWeight: 500
                    }}
                    axisLine={{
                      stroke: theme.palette.divider,
                      strokeWidth: 2
                    }}
                    tickLine={{
                      stroke: theme.palette.divider,
                      strokeWidth: 1
                    }}
                    height={60}
                  />

                  <YAxis
                    allowDecimals={false}
                    tick={{
                      fill: theme.palette.text.secondary,
                      fontSize: 13,
                      fontWeight: 500
                    }}
                    axisLine={{
                      stroke: theme.palette.divider,
                      strokeWidth: 2
                    }}
                    tickLine={{
                      stroke: theme.palette.divider,
                      strokeWidth: 1
                    }}
                    label={{
                      value: 'Número de Conversas',
                      angle: -90,
                      position: 'insideLeft',
                      style: {
                        textAnchor: 'middle',
                        fill: theme.palette.text.secondary,
                        fontSize: '14px',
                        fontWeight: 500
                      }
                    }}
                  />

                  <Tooltip content={<CustomTooltip />} />

                  <Bar
                    dataKey="count"
                    fill="url(#barGradient)"
                    name="Conversas"
                    barSize={80}
                    radius={[12, 12, 4, 4]}
                    filter="url(#shadow)"
                  >
                    {dailyChartData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={`url(#barGradient)`}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default DashboardPage;
