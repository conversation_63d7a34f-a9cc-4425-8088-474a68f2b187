import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Container, Typography, Grid, Paper, Box, CircularProgress, 
  Alert, useTheme, List, ListItem, ListItemText, ListItemAvatar, Avatar, Divider 
} from '@mui/material';
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, 
  ResponsiveContainer, PieChart, Pie, Cell 
} from 'recharts';
import PeopleIcon from '@mui/icons-material/People';
import MessageIcon from '@mui/icons-material/Message';
import DescriptionIcon from '@mui/icons-material/Description';
import TimerIcon from '@mui/icons-material/Timer';
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';
import axiosInstance from '../api/axiosInstance';

// --- API Fetchers ---
const fetchDashboardStats = async () => {
  const { data } = await axiosInstance.get('/dashboard/stats');
  return data;
};

const fetchRecentConversations = async () => {
  // Pega apenas as 5 conversas mais recentes
  const { data } = await axiosInstance.get('/conversations?limit=5');
  return data.conversations; // Retorna o array de conversas
};

// --- Sub-componentes de UI ---
const StatCard = ({ title, value, icon, unit = '', color }) => {
  const theme = useTheme();
  return (
    <Paper sx={{ 
      p: 3, 
      display: 'flex', 
      flexDirection: 'column', 
      justifyContent: 'space-between', 
      height: '100%', 
      minHeight: 140,
      borderLeft: `4px solid ${color || theme.palette.primary.main}`
    }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Typography variant="subtitle1" color="text.secondary">{title}</Typography>
        {icon}
      </Box>
      <Box>
        <Typography variant="h4" component="p" sx={{ fontWeight: 'bold' }}>
          {value}
          {unit && <Typography variant="h6" component="span" sx={{ ml: 0.5, fontWeight: 'normal' }}>{unit}</Typography>}
        </Typography>
      </Box>
    </Paper>
  );
};

const ChartContainer = ({ title, children }) => (
  <Paper sx={{ p: 2, height: '100%', minHeight: 400, display: 'flex', flexDirection: 'column' }}>
    <Typography variant="h6" sx={{ mb: 2 }}>{title}</Typography>
    <Box sx={{ flexGrow: 1 }}>
      {children}
    </Box>
  </Paper>
);

// --- Componente Principal do Dashboard ---
const DashboardPage = () => {
  const theme = useTheme();

  // Queries para buscar os dados
  const { data: stats, isLoading: isLoadingStats, isError: isErrorStats, error: errorStats } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: fetchDashboardStats,
  });

  const { data: conversations, isLoading: isLoadingConvos } = useQuery({
    queryKey: ['recentConversations'],
    queryFn: fetchRecentConversations,
  });

  // Tratamento de Loading e Erro
  if (isLoadingStats) return <CircularProgress />;
  if (isErrorStats) return <Alert severity="error">Erro ao carregar estatísticas: {errorStats.message}</Alert>;

  // Dados para os gráficos
  const dailyChartData = stats?.daily_conversations || [];
  const topicsChartData = stats?.top_topics || [];
  const PIE_COLORS = [theme.palette.primary.main, '#00C49F', '#FFBB28', '#FF8042', '#AF19FF'];

  const formatXAxis = (tickItem) => {
    const date = new Date(`${tickItem}T00:00:00`);
    return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
  };
  
  const formatLastMessageTime = (isoString) => {
    if (!isoString) return '';
    const date = new Date(isoString);
    return date.toLocaleDateString('pt-BR', { day: 'numeric', month: 'long' }) + ' às ' + date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3, backgroundColor: theme.palette.background.default }}>
      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 4, fontWeight: 'bold', color: theme.palette.text.primary }}>
          Dashboard de Análise
        </Typography>
        
        {/* Cards de Métricas */}
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Total de Conversas" value={stats?.total_conversations ?? '...'} icon={<PeopleIcon color="primary" sx={{ fontSize: 32 }} />} color={theme.palette.primary.main} />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Total de Mensagens" value={stats?.total_messages ?? '...'} icon={<MessageIcon sx={{ fontSize: 32, color: '#00C49F' }} />} color="#00C49F" />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Documentos na Base" value={stats?.total_documents ?? '...'} icon={<DescriptionIcon sx={{ fontSize: 32, color: '#FFBB28' }} />} color="#FFBB28" />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Tempo Médio de Resposta" value={stats?.avg_response_time ?? '...'} unit="segs" icon={<TimerIcon sx={{ fontSize: 32, color: '#FF8042' }} />} color="#FF8042" />
          </Grid>
        </Grid>

        {/* Gráficos e Conversas Recentes */}
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Gráfico de Conversas por Dia */}
          <Grid item xs={12} lg={8}>
            <ChartContainer title="Conversas nos Últimos 7 Dias">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={dailyChartData} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="day" tickFormatter={formatXAxis} tick={{ fill: theme.palette.text.secondary }} />
                  <YAxis allowDecimals={false} tick={{ fill: theme.palette.text.secondary }} />
                  <Tooltip contentStyle={{ backgroundColor: theme.palette.background.paper, borderRadius: '8px' }} />
                  <Legend />
                  <Bar dataKey="count" fill={theme.palette.primary.main} name="Nº de Conversas" barSize={30} radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </Grid>

          {/* Gráfico de Tópicos */}
          <Grid item xs={12} lg={4}>
            <ChartContainer title="Tópicos Mais Frequentes">
              {topicsChartData.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie data={topicsChartData} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={100} fill={theme.palette.primary.main}>
                      {topicsChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip contentStyle={{ backgroundColor: theme.palette.background.paper, borderRadius: '8px' }} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%'}}>
                  <Typography color="text.secondary">Sem dados de tópicos.</Typography>
                </Box>
              )}
            </ChartContainer>
          </Grid>

          {/* Conversas Recentes */}
          <Grid item xs={12}>
             <Paper sx={{ p: 2, mt: 2 }}>
                <Typography variant="h6" sx={{ mb: 1 }}>Conversas Recentes</Typography>
                <List>
                  {isLoadingConvos ? <CircularProgress size={24} /> :
                    conversations?.map((convo, index) => (
                      <React.Fragment key={convo.id}>
                        <ListItem alignItems="flex-start">
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: theme.palette.primary.light }}>
                              <ChatBubbleOutlineIcon />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={convo.contact_name || 'Nome não disponível'}
                            secondary={`Última mensagem: ${formatLastMessageTime(convo.last_message_at)}`}
                          />
                        </ListItem>
                        {index < conversations.length - 1 && <Divider variant="inset" component="li" />}
                      </React.Fragment>
                    ))
                  }
                </List>
             </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default DashboardPage;
