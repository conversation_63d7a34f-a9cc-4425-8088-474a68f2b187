import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Container, Typography, Grid, Paper, Box, CircularProgress,
  Alert, useTheme
} from '@mui/material';
import {
  <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend,
  ResponsiveContainer
} from 'recharts';
import PeopleIcon from '@mui/icons-material/People';
import MessageIcon from '@mui/icons-material/Message';
import DescriptionIcon from '@mui/icons-material/Description';
import TimerIcon from '@mui/icons-material/Timer';
import axiosInstance from '../api/axiosInstance';

// --- API Fetchers ---
const fetchDashboardStats = async () => {
  const { data } = await axiosInstance.get('/dashboard/stats');
  return data;
};

// Removido fetchRecentConversations - não será mais usado

// --- Sub-componentes de UI ---
const StatCard = ({ title, value, icon, unit = '', color }) => {
  const theme = useTheme();
  return (
    <Paper sx={{ 
      p: 3, 
      display: 'flex', 
      flexDirection: 'column', 
      justifyContent: 'space-between', 
      height: '100%', 
      minHeight: 140,
      borderLeft: `4px solid ${color || theme.palette.primary.main}`
    }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Typography variant="subtitle1" color="text.secondary">{title}</Typography>
        {icon}
      </Box>
      <Box>
        <Typography variant="h4" component="p" sx={{ fontWeight: 'bold' }}>
          {value}
          {unit && <Typography variant="h6" component="span" sx={{ ml: 0.5, fontWeight: 'normal' }}>{unit}</Typography>}
        </Typography>
      </Box>
    </Paper>
  );
};

const ChartContainer = ({ title, children, height = 500 }) => (
  <Paper sx={{ p: 3, height: '100%', minHeight: height, display: 'flex', flexDirection: 'column' }}>
    <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold' }}>{title}</Typography>
    <Box sx={{ flexGrow: 1 }}>
      {children}
    </Box>
  </Paper>
);

// --- Componente Principal do Dashboard ---
const DashboardPage = () => {
  const theme = useTheme();

  // Queries para buscar os dados
  const { data: stats, isLoading: isLoadingStats, isError: isErrorStats, error: errorStats } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: fetchDashboardStats,
  });

  // Removido query de conversas recentes

  // Tratamento de Loading e Erro
  if (isLoadingStats) return <CircularProgress />;
  if (isErrorStats) return <Alert severity="error">Erro ao carregar estatísticas: {errorStats.message}</Alert>;

  // Dados para o gráfico
  const dailyChartData = stats?.daily_conversations || [];

  const formatXAxis = (tickItem) => {
    const date = new Date(`${tickItem}T00:00:00`);
    return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
  };
  
  // Removido formatLastMessageTime - não será mais usado

  return (
    <Box sx={{ flexGrow: 1, p: 3, backgroundColor: theme.palette.background.default }}>
      <Container maxWidth="xl">
        <Typography variant="h4" sx={{ mb: 4, fontWeight: 'bold', color: theme.palette.text.primary }}>
          Dashboard de Análise
        </Typography>
        
        {/* Cards de Métricas */}
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Total de Conversas" value={stats?.total_conversations ?? '...'} icon={<PeopleIcon color="primary" sx={{ fontSize: 32 }} />} color={theme.palette.primary.main} />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Total de Mensagens" value={stats?.total_messages ?? '...'} icon={<MessageIcon sx={{ fontSize: 32, color: '#00C49F' }} />} color="#00C49F" />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Documentos na Base" value={stats?.total_documents ?? '...'} icon={<DescriptionIcon sx={{ fontSize: 32, color: '#FFBB28' }} />} color="#FFBB28" />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard title="Tempo Médio de Resposta" value={stats?.avg_response_time ?? '...'} unit="segs" icon={<TimerIcon sx={{ fontSize: 32, color: '#FF8042' }} />} color="#FF8042" />
          </Grid>
        </Grid>

        {/* Gráfico Principal - Aumentado */}
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Gráfico de Conversas por Dia - Agora ocupa toda a largura */}
          <Grid item xs={12}>
            <ChartContainer title="Conversas nos Últimos 7 Dias" height={600}>
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={dailyChartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="day"
                    tickFormatter={formatXAxis}
                    tick={{ fill: theme.palette.text.secondary, fontSize: 14 }}
                    axisLine={{ stroke: theme.palette.divider }}
                  />
                  <YAxis
                    allowDecimals={false}
                    tick={{ fill: theme.palette.text.secondary, fontSize: 14 }}
                    axisLine={{ stroke: theme.palette.divider }}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: theme.palette.background.paper,
                      borderRadius: '12px',
                      border: `1px solid ${theme.palette.divider}`,
                      boxShadow: theme.shadows[4]
                    }}
                  />
                  <Legend />
                  <Bar
                    dataKey="count"
                    fill={theme.palette.primary.main}
                    name="Número de Conversas"
                    barSize={60}
                    radius={[8, 8, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default DashboardPage;
