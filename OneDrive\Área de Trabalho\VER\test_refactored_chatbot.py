#!/usr/bin/env python3

"""
Teste da nova arquitetura refatorada do chatbot
"""

import asyncio
import sys
import os

# Adicionar o diretório do backend ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend_python'))

async def test_refactored_chatbot():
    """
    Testa os novos módulos refatorados do chatbot.
    """
    print("🔧 TESTE DA NOVA ARQUITETURA REFATORADA\n")
    
    try:
        # 1. Testar importações dos novos módulos
        print("📦 1. TESTANDO IMPORTAÇÕES...")
        
        try:
            from config.persona import SYSTEM_PROMPT, RESPONSE_TEMPLATES, EMOJIS, HASHTAGS, PERSONA_CONFIG
            print("✅ config.persona importado com sucesso")
            print(f"   - {len(RESPONSE_TEMPLATES)} templates de resposta")
            print(f"   - {len(EMOJIS)} emojis disponíveis")
            print(f"   - {len(HASHTAGS)} hashtags disponíveis")
        except Exception as e:
            print(f"❌ Erro ao importar config.persona: {e}")
            return
        
        try:
            from pipelines.post_processing import process_response, PostProcessor
            print("✅ pipelines.post_processing importado com sucesso")
        except Exception as e:
            print(f"❌ Erro ao importar pipelines.post_processing: {e}")
            return
        
        try:
            from pipelines.chat_pipeline import route_intent, generate_general_response
            print("✅ pipelines.chat_pipeline importado com sucesso")
        except Exception as e:
            print(f"❌ Erro ao importar pipelines.chat_pipeline: {e}")
            return
        
        try:
            from handlers.intent_handlers import create_intent_handler
            print("✅ handlers.intent_handlers importado com sucesso")
        except Exception as e:
            print(f"❌ Erro ao importar handlers.intent_handlers: {e}")
            return
        
        # 2. Testar pós-processamento
        print("\n🎨 2. TESTANDO PÓS-PROCESSAMENTO...")
        
        post_processor = PostProcessor()
        
        # Testar limpeza de texto
        dirty_text = "  Olá,   como   você está?  \n\n  "
        clean_text = post_processor.clean_text(dirty_text)
        print(f"   Texto limpo: '{clean_text}'")
        
        # Testar adição de emoji
        text_with_emoji = post_processor.add_emoji("Olá, como posso ajudar?", force=True)
        print(f"   Com emoji: '{text_with_emoji}'")
        
        # Testar adição de hashtag
        text_with_hashtag = post_processor.add_hashtag("Vamos trabalhar juntos!", force=True)
        print(f"   Com hashtag: '{text_with_hashtag}'")
        
        # Testar pipeline completo
        processed_text = process_response("Olá! Como posso ajudar você hoje?")
        print(f"   Pipeline completo: '{processed_text}'")
        
        # 3. Testar handlers de intenção
        print("\n🎯 3. TESTANDO HANDLERS DE INTENÇÃO...")
        
        intent_handler = create_intent_handler()
        
        # Testar template query
        template_response = intent_handler.handle_template_query("Bom dia!")
        if template_response:
            print(f"   Template response: '{template_response}'")
        else:
            print("   Nenhum template encontrado para 'Bom dia!'")
        
        # Testar fallback
        fallback_response = intent_handler.get_fallback_response()
        print(f"   Fallback response: '{fallback_response}'")
        
        # 4. Testar roteamento de intenção (se API key disponível)
        print("\n🧭 4. TESTANDO ROTEAMENTO DE INTENÇÃO...")
        
        if os.getenv("GEMINI_API_KEY"):
            try:
                # Testar com mensagem simples
                intent = await route_intent("Bom dia!", [])
                print(f"   Intent para 'Bom dia!': {intent}")
                
                # Testar com pergunta complexa
                intent2 = await route_intent("Quais são os programas sociais disponíveis?", [])
                print(f"   Intent para pergunta complexa: {intent2}")
                
            except Exception as e:
                print(f"   ⚠️  Erro no roteamento (normal se API key não configurada): {e}")
        else:
            print("   ⚠️  GEMINI_API_KEY não configurada - pulando teste de roteamento")
        
        # 5. Testar configurações da persona
        print("\n👤 5. TESTANDO CONFIGURAÇÕES DA PERSONA...")
        
        print(f"   System prompt: {len(SYSTEM_PROMPT)} caracteres")
        print(f"   Max response length: {PERSONA_CONFIG['max_response_length']}")
        print(f"   Temperature: {PERSONA_CONFIG['temperature']}")
        print(f"   Max history messages: {PERSONA_CONFIG['max_history_messages']}")
        
        # Mostrar alguns templates
        print("\n   📋 Templates disponíveis:")
        for key, template in list(RESPONSE_TEMPLATES.items())[:3]:
            print(f"     - {key}: {template['keywords'][:2]}...")
        
        # 6. Testar detecção contextual
        print("\n🔍 6. TESTANDO DETECÇÃO CONTEXTUAL...")
        
        # Testar emoji contextual
        health_text = "Vou verificar na UBS sobre seu medicamento"
        health_emoji = post_processor._select_contextual_emoji(health_text)
        print(f"   Emoji para saúde: {health_emoji}")
        
        work_text = "Vou verificar oportunidades de trabalho"
        work_emoji = post_processor._select_contextual_emoji(work_text)
        print(f"   Emoji para trabalho: {work_emoji}")
        
        # Testar hashtag contextual
        transparency_text = "Todos os gastos estão disponíveis para consulta"
        transparency_hashtag = post_processor._select_contextual_hashtag(transparency_text)
        print(f"   Hashtag para transparência: {transparency_hashtag}")
        
        # 7. Resumo final
        print("\n" + "=" * 60)
        print("🎯 RESUMO DA REFATORAÇÃO")
        print("=" * 60)
        
        print("\n✅ MÓDULOS IMPLEMENTADOS:")
        print("   📄 config/persona.py - Configuração centralizada")
        print("   🔧 pipelines/post_processing.py - Pós-processamento")
        print("   💬 pipelines/chat_pipeline.py - Pipeline de chat")
        print("   🎯 handlers/intent_handlers.py - Handlers de intenção")
        
        print("\n✅ BENEFÍCIOS ALCANÇADOS:")
        print("   🧹 Código mais limpo e organizado")
        print("   🔧 Manutenibilidade melhorada")
        print("   📈 Escalabilidade aumentada")
        print("   🎯 Separação clara de responsabilidades")
        print("   🔄 Reutilização de código")
        
        print("\n✅ FUNCIONALIDADES:")
        print("   👤 Persona centralizada e configurável")
        print("   🎨 Pós-processamento inteligente")
        print("   💬 Chat com lista de mensagens")
        print("   🎯 Roteamento de intenções")
        print("   🔧 Handlers modulares")
        
        print("\n🎉 REFATORAÇÃO CONCLUÍDA COM SUCESSO!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ ERRO NO TESTE: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_refactored_chatbot())
