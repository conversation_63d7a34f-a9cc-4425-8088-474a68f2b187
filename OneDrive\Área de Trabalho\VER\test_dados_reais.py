#!/usr/bin/env python3
"""
Teste completo do sistema com dados reais (sem mocks).
"""
import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def print_header(title):
    """Imprime cabeçalho formatado."""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def print_success(message):
    """Imprime mensagem de sucesso."""
    print(f"✅ {message}")

def print_error(message):
    """Imprime mensagem de erro."""
    print(f"❌ {message}")

def print_info(message):
    """Imprime mensagem informativa."""
    print(f"ℹ️  {message}")

def get_auth_token():
    """Obtém token de autenticação."""
    try:
        response = requests.post(
            f"{BASE_URL}/token",
            data={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            return response.json()['access_token']
        else:
            print_error(f"Erro no login: {response.text}")
            return None
    except Exception as e:
        print_error(f"Erro na autenticação: {e}")
        return None

def test_whatsapp_real_data(token):
    """Testa se o WhatsApp está retornando dados reais."""
    print_header("TESTE DO WHATSAPP COM DADOS REAIS")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Teste 1: Status do WhatsApp
        status_response = requests.get(f"{BASE_URL}/whatsapp/status", headers=headers)
        if status_response.status_code == 200:
            status = status_response.json()
            print_success(f"WhatsApp Status: {status.get('status')}")
        else:
            print_error(f"Erro no status: {status_response.status_code}")
            return False
        
        # Teste 2: Conversas do WhatsApp
        conv_response = requests.get(f"{BASE_URL}/whatsapp/conversations", headers=headers)
        if conv_response.status_code == 200:
            conversations = conv_response.json()
            print_success(f"Conversas encontradas: {len(conversations)}")
            
            # Verificar se são dados reais ou simulados
            if conversations:
                first_conv = conversations[0]
                if 'João Silva' in str(first_conv) or 'Maria Santos' in str(first_conv):
                    print_info("⚠️  Ainda usando dados simulados")
                    return False
                else:
                    print_success("✅ Usando dados reais do WhatsApp!")
                    print_info(f"Primeira conversa: {first_conv.get('name', 'N/A')}")
                    return True
            else:
                print_info("Nenhuma conversa encontrada")
                return True
        else:
            print_error(f"Erro nas conversas: {conv_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erro ao testar WhatsApp: {e}")
        return False

def test_database_real_data(token):
    """Testa se o banco de dados está salvando dados reais."""
    print_header("TESTE DO BANCO DE DADOS COM DADOS REAIS")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Teste 1: Enviar mensagem para criar dados reais
        test_contact_id = f"test_real_{int(time.time())}@c.us"
        test_contact_name = "Teste Dados Reais"
        test_message = "Esta é uma mensagem de teste para verificar dados reais"
        
        chat_response = requests.post(
            f"{BASE_URL}/chat/",
            json={
                "query": test_message,
                "contact_id": test_contact_id,
                "contact_name": test_contact_name
            },
            headers=headers
        )
        
        if chat_response.status_code == 200:
            print_success("Mensagem enviada com sucesso")
            
            # Aguardar um pouco para o banco processar
            time.sleep(2)
            
            # Teste 2: Verificar se a conversa foi salva
            conv_response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
            if conv_response.status_code == 200:
                conv_data = conv_response.json()
                # O endpoint retorna um objeto com 'conversations' e 'total_count'
                if isinstance(conv_data, dict) and 'conversations' in conv_data:
                    conversations = conv_data['conversations']
                else:
                    conversations = conv_data

                print_success(f"Conversas no banco: {len(conversations)}")

                # Procurar nossa conversa de teste
                test_conv = None
                for conv in conversations:
                    if conv.get('contact_id') == test_contact_id:
                        test_conv = conv
                        break
                
                if test_conv:
                    print_success("✅ Conversa salva no banco de dados!")
                    print_info(f"ID da conversa: {test_conv.get('id')}")
                    
                    # Teste 3: Verificar mensagens
                    msg_response = requests.get(
                        f"{BASE_URL}/conversations/{test_conv['id']}/messages",
                        headers=headers
                    )
                    
                    if msg_response.status_code == 200:
                        messages = msg_response.json()
                        print_success(f"Mensagens encontradas: {len(messages)}")
                        
                        # Verificar se tem nossa mensagem
                        user_msg = None
                        ai_msg = None
                        for msg in messages:
                            if msg.get('sender') == 'user' and test_message in msg.get('content', ''):
                                user_msg = msg
                            elif msg.get('sender') == 'ai':
                                ai_msg = msg
                        
                        if user_msg:
                            print_success("✅ Mensagem do usuário salva!")
                        if ai_msg:
                            print_success("✅ Resposta da IA salva!")
                        
                        return user_msg is not None and ai_msg is not None
                    else:
                        print_error(f"Erro ao buscar mensagens: {msg_response.status_code}")
                        return False
                else:
                    print_error("Conversa não encontrada no banco")
                    return False
            else:
                print_error(f"Erro ao buscar conversas: {conv_response.status_code}")
                return False
        else:
            print_error(f"Erro no chat: {chat_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erro ao testar banco: {e}")
        return False

def test_dashboard_real_data(token):
    """Testa se o dashboard está usando dados reais."""
    print_header("TESTE DO DASHBOARD COM DADOS REAIS")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/dashboard/stats", headers=headers)
        if response.status_code == 200:
            stats = response.json()
            print_success("Dashboard funcionando")
            
            # Verificar se os dados parecem reais
            total_conv = stats.get('total_conversations', 0)
            total_msg = stats.get('total_messages', 0)
            daily_data = stats.get('daily_conversations', [])
            
            print_info(f"Total de conversas: {total_conv}")
            print_info(f"Total de mensagens: {total_msg}")
            print_info(f"Dados diários: {len(daily_data)} dias")
            
            # Verificar se não são os valores mockados antigos
            if total_conv == 15 and total_msg == 67:
                print_info("⚠️  Ainda usando valores mínimos (possivelmente mockados)")
                return False
            else:
                print_success("✅ Usando dados reais do banco!")
                return True
        else:
            print_error(f"Erro no dashboard: {response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Erro ao testar dashboard: {e}")
        return False

def main():
    """Executa todos os testes com dados reais."""
    print_header("🚀 TESTE COMPLETO COM DADOS REAIS")
    print_info(f"Iniciado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. Autenticação
    token = get_auth_token()
    if not token:
        print_error("Não é possível continuar sem autenticação")
        return
    
    # 2. Teste WhatsApp
    whatsapp_real = test_whatsapp_real_data(token)
    
    # 3. Teste Banco de Dados
    database_real = test_database_real_data(token)
    
    # 4. Teste Dashboard
    dashboard_real = test_dashboard_real_data(token)
    
    # Resultado final
    print_header("🎉 RESULTADO FINAL")
    
    if whatsapp_real and database_real and dashboard_real:
        print_success("🎉 SISTEMA 100% COM DADOS REAIS!")
        print_info("✅ WhatsApp: Dados reais")
        print_info("✅ Banco de Dados: Salvando dados reais")
        print_info("✅ Dashboard: Exibindo dados reais")
        print_success("🚀 Migração dos dados mockados CONCLUÍDA!")
    else:
        print_info("📊 Status da migração:")
        print_info(f"  WhatsApp: {'✅ Real' if whatsapp_real else '⚠️  Simulado'}")
        print_info(f"  Banco: {'✅ Real' if database_real else '⚠️  Simulado'}")
        print_info(f"  Dashboard: {'✅ Real' if dashboard_real else '⚠️  Simulado'}")
        
        if not whatsapp_real:
            print_info("💡 WhatsApp pode estar usando fallback simulado")
        if not database_real:
            print_info("💡 Verificar configuração do Supabase")
        if not dashboard_real:
            print_info("💡 Dashboard pode estar usando dados mínimos")

if __name__ == "__main__":
    main()
