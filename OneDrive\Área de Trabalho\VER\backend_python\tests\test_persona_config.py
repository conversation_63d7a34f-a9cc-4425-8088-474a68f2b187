"""
Testes unitários para a configuração da persona
"""

import unittest
import sys
import os

# Adicionar o diretório pai ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.persona import (
    SYSTEM_PROMPT, 
    RESPONSE_TEMPLATES, 
    EMOJIS, 
    HASHTAGS, 
    PERSONA_CONFIG,
    RAG_SYSTEM_PROMPT,
    GENERAL_CONVERSATION_PROMPT,
    INTENT_ROUTING_PROMPT
)


class TestPersonaConfig(unittest.TestCase):
    """
    Testes para verificar a configuração da persona
    """
    
    def test_system_prompt_exists_and_valid(self):
        """Testa se o system prompt existe e é válido"""
        self.assertIsInstance(SYSTEM_PROMPT, str)
        self.assertGreater(len(SYSTEM_PROMPT), 100)
        self.assertIn("Vereadora Rafaela <PERSON>lda", SYSTEM_PROMPT)
        self.assertIn("<PERSON><PERSON><PERSON><PERSON>", SYSTEM_PROMPT)
        
    def test_response_templates_structure(self):
        """Testa a estrutura dos templates de resposta"""
        self.assertIsInstance(RESPONSE_TEMPLATES, dict)
        self.assertGreater(len(RESPONSE_TEMPLATES), 5)
        
        # Verificar templates essenciais
        essential_templates = [
            "saudacao_geral", 
            "assistencia_social", 
            "medicamentos",
            "pedido_emprego"
        ]
        
        for template in essential_templates:
            self.assertIn(template, RESPONSE_TEMPLATES)
            
        # Verificar estrutura de cada template
        for template_key, template_data in RESPONSE_TEMPLATES.items():
            self.assertIn("keywords", template_data)
            self.assertIn("response", template_data)
            self.assertIsInstance(template_data["keywords"], list)
            self.assertIsInstance(template_data["response"], str)
            self.assertGreater(len(template_data["keywords"]), 0)
            self.assertGreater(len(template_data["response"]), 10)
    
    def test_emojis_and_hashtags(self):
        """Testa se emojis e hashtags estão configurados"""
        self.assertIsInstance(EMOJIS, list)
        self.assertGreater(len(EMOJIS), 5)
        
        self.assertIsInstance(HASHTAGS, list)
        self.assertGreater(len(HASHTAGS), 3)
        
        # Verificar se hashtags começam com #
        for hashtag in HASHTAGS:
            self.assertTrue(hashtag.startswith("#"))
            
        # Verificar hashtags essenciais
        essential_hashtags = ["#RafaelaDeNilda", "#ParnamirimRN"]
        for hashtag in essential_hashtags:
            self.assertIn(hashtag, HASHTAGS)
    
    def test_persona_config_structure(self):
        """Testa a estrutura da configuração da persona"""
        self.assertIsInstance(PERSONA_CONFIG, dict)
        
        required_configs = [
            "max_response_length",
            "emoji_probability", 
            "hashtag_probability",
            "temperature",
            "max_history_messages"
        ]
        
        for config in required_configs:
            self.assertIn(config, PERSONA_CONFIG)
            
        # Verificar tipos e valores
        self.assertIsInstance(PERSONA_CONFIG["max_response_length"], int)
        self.assertGreater(PERSONA_CONFIG["max_response_length"], 100)
        
        self.assertIsInstance(PERSONA_CONFIG["emoji_probability"], float)
        self.assertGreaterEqual(PERSONA_CONFIG["emoji_probability"], 0.0)
        self.assertLessEqual(PERSONA_CONFIG["emoji_probability"], 1.0)
        
        self.assertIsInstance(PERSONA_CONFIG["temperature"], float)
        self.assertGreaterEqual(PERSONA_CONFIG["temperature"], 0.0)
        self.assertLessEqual(PERSONA_CONFIG["temperature"], 2.0)
    
    def test_specialized_prompts(self):
        """Testa os prompts especializados"""
        # RAG System Prompt
        self.assertIsInstance(RAG_SYSTEM_PROMPT, str)
        self.assertIn("CONTEXTO ADICIONAL", RAG_SYSTEM_PROMPT)
        self.assertIn("documentos", RAG_SYSTEM_PROMPT.lower())
        
        # General Conversation Prompt
        self.assertIsInstance(GENERAL_CONVERSATION_PROMPT, str)
        self.assertIn("conversa geral", GENERAL_CONVERSATION_PROMPT.lower())
        
        # Intent Routing Prompt
        self.assertIsInstance(INTENT_ROUTING_PROMPT, str)
        self.assertIn("classificador", INTENT_ROUTING_PROMPT.lower())
        self.assertIn("answer_with_template", INTENT_ROUTING_PROMPT)
        self.assertIn("search_documents_rag", INTENT_ROUTING_PROMPT)
    
    def test_template_keywords_coverage(self):
        """Testa se as palavras-chave cobrem cenários importantes"""
        all_keywords = []
        for template_data in RESPONSE_TEMPLATES.values():
            all_keywords.extend(template_data["keywords"])
        
        # Verificar cobertura de temas importantes
        important_themes = [
            "bom dia", "oi", "olá",  # Saudações
            "cesta básica", "cras",  # Assistência social
            "remédio", "medicamento",  # Saúde
            "emprego", "trabalho",  # Trabalho
            "deus", "amém"  # Fé
        ]
        
        for theme in important_themes:
            found = any(theme in keyword for keyword in all_keywords)
            self.assertTrue(found, f"Tema '{theme}' não encontrado nas palavras-chave")
    
    def test_response_quality(self):
        """Testa a qualidade das respostas dos templates"""
        for template_key, template_data in RESPONSE_TEMPLATES.items():
            response = template_data["response"]
            
            # Verificar se a resposta tem tom adequado
            self.assertGreater(len(response), 20, f"Resposta muito curta em {template_key}")
            
            # Verificar se contém elementos da persona
            persona_elements = ["você", "nossa", "juntos", "equipe"]
            has_persona_element = any(element in response.lower() for element in persona_elements)
            self.assertTrue(has_persona_element, f"Resposta sem elementos da persona em {template_key}")


if __name__ == "__main__":
    unittest.main()
