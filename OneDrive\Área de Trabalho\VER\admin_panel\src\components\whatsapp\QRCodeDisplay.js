import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Box, CircularProgress, Typography, Paper, Alert } from '@mui/material';
import axiosInstance from '../../api/axiosInstance';

const fetchQRCode = async () => {
  const { data } = await axiosInstance.get('/whatsapp/qr-code');
  return data;
};

const QRCodeDisplay = () => {
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ['whatsapp-qr'],
    queryFn: fetchQRCode,
  });

  if (isLoading) {
    return <CircularProgress />;
  }

  if (isError) {
    return <Alert severity="warning">Não foi possível obter o QR Code: {error.message}</Alert>;
  }

  return (
    <Paper sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
      <Typography variant="h6">Escaneie o QR Code</Typography>
      <Typography variant="body2" color="text.secondary">
        Abra o WhatsApp no seu celular, vá em Aparelhos Conectados e escaneie a imagem abaixo.
      </Typography>
      {data?.qrCode ? (
        <img src={data.qrCode} alt="QR Code do WhatsApp" style={{ width: '100%', maxWidth: '300px', height: 'auto' }} />
      ) : (
        <Alert severity="info">Aguardando geração do QR Code...</Alert>
      )}
    </Paper>
  );
};

export default QRCodeDisplay;
