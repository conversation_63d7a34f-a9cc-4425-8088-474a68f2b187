import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Box, CircularProgress, Typography, Paper, Alert, Chip } from '@mui/material';
import { QrCode, Refresh } from '@mui/icons-material';
import axiosInstance from '../../api/axiosInstance';

const fetchQRCode = async () => {
  const { data } = await axiosInstance.get('/whatsapp/qr-code');
  return data;
};

const QRCodeDisplay = () => {
  const { data, isLoading, isError, error, isFetching } = useQuery({
    queryKey: ['whatsapp-qr'],
    queryFn: fetchQRCode,
    refetchInterval: 2000, // Atualiza o QR Code a cada 2 segundos
    refetchIntervalInBackground: true, // Continua atualizando mesmo quando a aba não está ativa
    retry: 3, // Tenta 3 vezes em caso de erro
    retryDelay: 1000, // Aguarda 1 segundo entre tentativas
  });

  if (isLoading) {
    return <CircularProgress />;
  }

  if (isError) {
    return <Alert severity="warning">Não foi possível obter o QR Code: {error.message}</Alert>;
  }

  return (
    <Paper sx={{ p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
        <QrCode color="primary" />
        <Typography variant="h6">Escaneie o QR Code</Typography>
        {isFetching && (
          <Chip
            icon={<Refresh />}
            label="Atualizando..."
            size="small"
            color="primary"
            variant="outlined"
          />
        )}
      </Box>

      <Typography variant="body2" color="text.secondary" textAlign="center">
        Abra o WhatsApp no seu celular, vá em <strong>Aparelhos Conectados</strong> e escaneie a imagem abaixo.
      </Typography>

      <Typography variant="caption" color="text.secondary" textAlign="center">
        ⏱️ O QR Code é atualizado automaticamente a cada 2 segundos
      </Typography>

      {data?.qrCode ? (
        <Box sx={{ position: 'relative', display: 'inline-block' }}>
          <img
            src={data.qrCode}
            alt="QR Code do WhatsApp"
            style={{
              width: '100%',
              maxWidth: '300px',
              height: 'auto',
              border: '2px solid #e0e0e0',
              borderRadius: '8px',
              opacity: isFetching ? 0.7 : 1,
              transition: 'opacity 0.3s ease'
            }}
          />
          {isFetching && (
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                borderRadius: '50%',
                p: 1
              }}
            >
              <CircularProgress size={24} />
            </Box>
          )}
        </Box>
      ) : (
        <Alert severity="info" sx={{ width: '100%', maxWidth: '300px' }}>
          <Typography variant="body2">
            🔄 Aguardando geração do QR Code...
          </Typography>
        </Alert>
      )}

      <Alert severity="warning" sx={{ width: '100%', maxWidth: '400px', mt: 2 }}>
        <Typography variant="body2">
          <strong>⚠️ Importante:</strong> Você tem aproximadamente 90 segundos para escanear cada QR Code.
          Se não conseguir, um novo será gerado automaticamente.
        </Typography>
      </Alert>
    </Paper>
  );
};

export default QRCodeDisplay;
