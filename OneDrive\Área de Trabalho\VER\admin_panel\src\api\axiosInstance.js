import axios from 'axios';
import useAuthStore from '../store/authStore';

const axiosInstance = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? process.env.REACT_APP_API_URL : 'http://localhost:8000',
});

// Interceptor para adicionar o token de autenticação em cada requisição
axiosInstance.interceptors.request.use(
  (config) => {
    const token = useAuthStore.getState().authToken;
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Interceptor para lidar com erros de autenticação (ex: token expirado)
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.status === 401) {
      // Se o erro for 401 Unauthorized, desloga o usuário
      useAuthStore.getState().logout();
      // Redireciona para a página de login
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;
