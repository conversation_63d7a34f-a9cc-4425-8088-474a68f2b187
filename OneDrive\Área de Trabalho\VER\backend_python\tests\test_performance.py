"""
Testes de performance e stress para o chat da persona
"""

import unittest
import time
import sys
import os
import asyncio
import statistics
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import Mock, patch

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pipelines.post_processing import PostProcessor, process_response
from handlers.intent_handlers import IntentHandlers
from config.persona import RESPONSE_TEMPLATES, EMOJIS, HASHTAGS


class TestPerformance(unittest.TestCase):
    """
    Testes de performance para o sistema de chat
    """
    
    def setUp(self):
        """Configurar testes de performance"""
        self.processor = PostProcessor()
        self.mock_supabase = Mock()
        self.handler = IntentHandlers(self.mock_supabase)
        self.performance_results = {}
    
    def test_post_processing_speed(self):
        """Testa velocidade do pós-processamento"""
        test_texts = [
            "Ol<PERSON>, como posso ajudar você hoje?",
            "Vou verificar essa informação para você na nossa base de dados.",
            "Nossa equipe está sempre trabalhando para melhorar os serviços de Parnamirim.",
            "A transparência é fundamental para nossa gestão pública.",
            "Juntos somos mais fortes e podemos construir uma cidade melhor."
        ]
        
        times = []
        
        for text in test_texts:
            start_time = time.time()
            processed = process_response(text)
            end_time = time.time()
            
            processing_time = end_time - start_time
            times.append(processing_time)
            
            # Verificar se processou corretamente
            self.assertIsInstance(processed, str)
            self.assertGreaterEqual(len(processed), len(text))
        
        # Calcular estatísticas
        avg_time = statistics.mean(times)
        max_time = max(times)
        min_time = min(times)
        
        self.performance_results['post_processing'] = {
            'avg_time': avg_time,
            'max_time': max_time,
            'min_time': min_time,
            'total_tests': len(test_texts)
        }
        
        # Verificar se está dentro de limites aceitáveis (< 100ms)
        self.assertLess(avg_time, 0.1, f"Pós-processamento muito lento: {avg_time:.3f}s")
        
        print(f"   📊 Pós-processamento - Média: {avg_time*1000:.1f}ms, Máx: {max_time*1000:.1f}ms")
    
    def test_template_matching_speed(self):
        """Testa velocidade da correspondência de templates"""
        test_queries = [
            "Bom dia!", "Oi", "Olá", "Boa tarde",
            "cesta básica", "CRAS", "assistência social",
            "remédio", "medicamento", "farmácia",
            "emprego", "trabalho", "oportunidade",
            "cirurgia", "exame", "consulta",
            "dentista", "tratamento dental",
            "amém", "Deus", "fé",
            "agenda", "reunião", "encontro"
        ]
        
        times = []
        matches = 0
        
        for query in test_queries:
            start_time = time.time()
            response = self.handler.handle_template_query(query)
            end_time = time.time()
            
            processing_time = end_time - start_time
            times.append(processing_time)
            
            if response:
                matches += 1
        
        # Calcular estatísticas
        avg_time = statistics.mean(times)
        max_time = max(times)
        min_time = min(times)
        match_rate = matches / len(test_queries)
        
        self.performance_results['template_matching'] = {
            'avg_time': avg_time,
            'max_time': max_time,
            'min_time': min_time,
            'match_rate': match_rate,
            'total_queries': len(test_queries)
        }
        
        # Verificar performance
        self.assertLess(avg_time, 0.01, f"Template matching muito lento: {avg_time:.3f}s")
        self.assertGreater(match_rate, 0.8, f"Taxa de match muito baixa: {match_rate:.2f}")
        
        print(f"   📊 Template matching - Média: {avg_time*1000:.1f}ms, Match rate: {match_rate:.1%}")
    
    def test_concurrent_processing(self):
        """Testa processamento concorrente"""
        test_text = "Olá, como posso ajudar você hoje?"
        num_threads = 10
        num_requests_per_thread = 20
        
        def process_multiple():
            times = []
            for _ in range(num_requests_per_thread):
                start_time = time.time()
                processed = process_response(test_text)
                end_time = time.time()
                times.append(end_time - start_time)
                
                # Verificar resultado
                self.assertIsInstance(processed, str)
            return times
        
        # Executar concorrentemente
        start_total = time.time()
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(process_multiple) for _ in range(num_threads)]
            all_times = []
            for future in futures:
                all_times.extend(future.result())
        end_total = time.time()
        
        # Calcular estatísticas
        total_requests = num_threads * num_requests_per_thread
        total_time = end_total - start_total
        avg_time = statistics.mean(all_times)
        throughput = total_requests / total_time
        
        self.performance_results['concurrent_processing'] = {
            'total_requests': total_requests,
            'total_time': total_time,
            'avg_time': avg_time,
            'throughput': throughput,
            'num_threads': num_threads
        }
        
        # Verificar se mantém performance sob carga
        self.assertLess(avg_time, 0.2, f"Performance degradada sob carga: {avg_time:.3f}s")
        self.assertGreater(throughput, 50, f"Throughput muito baixo: {throughput:.1f} req/s")
        
        print(f"   📊 Concorrência - {total_requests} requests, {throughput:.1f} req/s")
    
    def test_memory_usage(self):
        """Testa uso de memória"""
        import psutil
        import gc
        
        process = psutil.Process()
        
        # Medição inicial
        gc.collect()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Processar muitos textos
        large_texts = []
        for i in range(1000):
            text = f"Esta é uma mensagem de teste número {i} para verificar o uso de memória. " * 5
            processed = process_response(text)
            large_texts.append(processed)
        
        # Medição após processamento
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Limpar e medir novamente
        del large_texts
        gc.collect()
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_increase = peak_memory - initial_memory
        memory_leak = final_memory - initial_memory
        
        self.performance_results['memory_usage'] = {
            'initial_memory': initial_memory,
            'peak_memory': peak_memory,
            'final_memory': final_memory,
            'memory_increase': memory_increase,
            'memory_leak': memory_leak
        }
        
        # Verificar se não há vazamentos significativos
        self.assertLess(memory_leak, 10, f"Possível vazamento de memória: {memory_leak:.1f}MB")
        self.assertLess(memory_increase, 50, f"Uso excessivo de memória: {memory_increase:.1f}MB")
        
        print(f"   📊 Memória - Pico: {peak_memory:.1f}MB, Vazamento: {memory_leak:.1f}MB")
    
    def test_stress_template_matching(self):
        """Teste de stress para template matching"""
        # Gerar muitas queries variadas
        base_queries = list(RESPONSE_TEMPLATES.keys())
        stress_queries = []
        
        # Multiplicar queries com variações
        for template_key in base_queries:
            template_data = RESPONSE_TEMPLATES[template_key]
            for keyword in template_data["keywords"]:
                stress_queries.extend([
                    keyword,
                    keyword.upper(),
                    keyword.capitalize(),
                    f"Eu preciso de {keyword}",
                    f"Como faço para {keyword}?",
                    f"Informações sobre {keyword}"
                ])
        
        # Adicionar queries que não devem fazer match
        stress_queries.extend([
            "query inexistente xyz123",
            "pergunta muito específica sem template",
            "texto aleatório sem sentido",
            "abcdefghijklmnop"
        ] * 50)
        
        start_time = time.time()
        matches = 0
        errors = 0
        
        for query in stress_queries:
            try:
                response = self.handler.handle_template_query(query)
                if response:
                    matches += 1
            except Exception:
                errors += 1
        
        end_time = time.time()
        total_time = end_time - start_time
        
        self.performance_results['stress_test'] = {
            'total_queries': len(stress_queries),
            'total_time': total_time,
            'matches': matches,
            'errors': errors,
            'queries_per_second': len(stress_queries) / total_time
        }
        
        # Verificar se não houve erros
        self.assertEqual(errors, 0, f"Erros durante teste de stress: {errors}")
        self.assertGreater(len(stress_queries) / total_time, 100, "Performance insuficiente no stress test")
        
        print(f"   📊 Stress test - {len(stress_queries)} queries, {len(stress_queries)/total_time:.0f} q/s")
    
    def test_emoji_hashtag_performance(self):
        """Testa performance da seleção de emojis e hashtags"""
        test_contexts = [
            "Vou verificar na UBS sobre seu medicamento",
            "Vamos trabalhar juntos para melhorar nossa cidade",
            "Que Deus nos abençoe nesta caminhada",
            "A transparência é fundamental na gestão pública",
            "Nossa equipe está sempre disponível para ajudar",
            "A inclusão é uma das minhas principais bandeiras",
            "Parnamirim merece o melhor atendimento",
            "Juntos somos mais fortes e unidos"
        ]
        
        emoji_times = []
        hashtag_times = []
        
        for context in test_contexts:
            # Testar seleção de emoji
            start_time = time.time()
            emoji = self.processor._select_contextual_emoji(context)
            emoji_time = time.time() - start_time
            emoji_times.append(emoji_time)
            
            # Testar seleção de hashtag
            start_time = time.time()
            hashtag = self.processor._select_contextual_hashtag(context)
            hashtag_time = time.time() - start_time
            hashtag_times.append(hashtag_time)
            
            # Verificar resultados
            self.assertIn(emoji, EMOJIS)
            self.assertIn(hashtag, HASHTAGS)
        
        avg_emoji_time = statistics.mean(emoji_times)
        avg_hashtag_time = statistics.mean(hashtag_times)
        
        self.performance_results['emoji_hashtag'] = {
            'avg_emoji_time': avg_emoji_time,
            'avg_hashtag_time': avg_hashtag_time,
            'total_contexts': len(test_contexts)
        }
        
        # Verificar performance
        self.assertLess(avg_emoji_time, 0.001, f"Seleção de emoji muito lenta: {avg_emoji_time:.4f}s")
        self.assertLess(avg_hashtag_time, 0.001, f"Seleção de hashtag muito lenta: {avg_hashtag_time:.4f}s")
        
        print(f"   📊 Emoji/Hashtag - Emoji: {avg_emoji_time*1000:.2f}ms, Hashtag: {avg_hashtag_time*1000:.2f}ms")
    
    def get_performance_report(self):
        """Retorna relatório de performance"""
        return self.performance_results


if __name__ == "__main__":
    unittest.main()
