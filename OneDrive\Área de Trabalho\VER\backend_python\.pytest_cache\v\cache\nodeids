["test_api.py::test_delete_document", "test_api.py::test_delete_document_not_found", "test_api.py::test_delete_document_success", "test_api.py::test_delete_non_existent_document", "test_api.py::test_get_document_content", "test_api.py::test_get_whatsapp_conversations", "test_api.py::test_get_whatsapp_conversations_error", "test_api.py::test_get_whatsapp_conversations_success", "test_api.py::test_get_whatsapp_qr_code", "test_api.py::test_get_whatsapp_qr_code_error", "test_api.py::test_get_whatsapp_qr_code_success", "test_api.py::test_ingest_document", "test_api.py::test_ingest_document_and_list", "test_api.py::test_ingest_empty_file", "test_api.py::test_ingest_unsupported_file_type", "test_api.py::test_login_for_access_token", "test_api.py::test_persona_assistencia_social", "test_api.py::test_persona_cirurgias_exames", "test_api.py::test_persona_encontro_presencial", "test_api.py::test_persona_medicamentos", "test_api.py::test_persona_mensagem_fe", "test_api.py::test_persona_pedido_emprego", "test_api.py::test_persona_query_requires_auth", "test_api.py::test_persona_saudacao_geral", "test_api.py::test_persona_tratamentos_dentarios", "test_api.py::test_rag_no_relevant_info", "test_api.py::test_rag_success_with_document", "test_api.py::test_read_users_me", "test_api.py::test_register_user", "test_api.py::test_transcribe_audio_api_error", "test_api.py::test_transcribe_audio_success"]