#!/usr/bin/env python3
"""
Script para testar sincronização com banco de dados.
"""
import requests
import json
import time
from datetime import datetime

BASE_URL = "http://localhost:8000"

def get_auth_token():
    """Obtém token de autenticação."""
    try:
        response = requests.post(
            f"{BASE_URL}/token",
            data={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            return response.json()['access_token']
        else:
            print(f"❌ Erro no login: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Erro na autenticação: {e}")
        return None

def test_conversation_sync(token):
    """Testa sincronização de conversas."""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("🔄 Testando sincronização de conversas...")
    
    # Dados de teste para sincronização
    test_conversation = {
        "contact_id": f"test_conversation_{int(time.time())}@c.us",
        "contact_name": "Teste Sincronização",
        "last_message_at": datetime.now().isoformat(),
        "whatsapp_data": {
            "unreadCount": 1,
            "isArchived": False,
            "isPinned": False
        }
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/sync-conversation",
            json=test_conversation,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Conversa sincronizada com sucesso")
            print(f"   ID: {data.get('conversation_id', 'N/A')}")
            return data.get('conversation_id')
        else:
            print(f"❌ Erro na sincronização: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Erro na sincronização: {e}")
        return None

def test_database_queries(token):
    """Testa consultas ao banco de dados."""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📊 Testando consultas ao banco de dados...")
    
    # Testar listagem de conversas
    try:
        response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
        if response.status_code == 200:
            conversations = response.json()
            print(f"✅ Conversas no banco: {len(conversations)}")
        else:
            print(f"❌ Erro ao consultar conversas: {response.status_code}")
    except Exception as e:
        print(f"❌ Erro na consulta de conversas: {e}")
    
    # Testar conversas que requerem atenção
    try:
        response = requests.get(f"{BASE_URL}/conversations/?requires_attention=true", headers=headers)
        if response.status_code == 200:
            pending = response.json()
            print(f"✅ Conversas pendentes: {len(pending)}")
        else:
            print(f"❌ Erro ao consultar conversas pendentes: {response.status_code}")
    except Exception as e:
        print(f"❌ Erro na consulta de conversas pendentes: {e}")

def test_whatsapp_sync(token):
    """Testa sincronização com WhatsApp."""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n📱 Testando sincronização com WhatsApp...")
    
    try:
        # Obter conversas do WhatsApp
        response = requests.get(f"{BASE_URL}/whatsapp/conversations", headers=headers)
        if response.status_code == 200:
            whatsapp_conversations = response.json()
            print(f"✅ Conversas do WhatsApp: {len(whatsapp_conversations)}")
            
            # Testar sincronização de uma conversa específica
            if whatsapp_conversations:
                first_conv = whatsapp_conversations[0]
                contact_id = first_conv.get('id', {}).get('_serialized', '')
                
                if contact_id:
                    sync_data = {
                        "contact_id": contact_id,
                        "contact_name": first_conv.get('name', 'Sem nome'),
                        "last_message_at": datetime.now().isoformat(),
                        "whatsapp_data": {
                            "unreadCount": first_conv.get('unreadCount', 0),
                            "isArchived": first_conv.get('isArchived', False),
                            "isPinned": first_conv.get('isPinned', False)
                        }
                    }
                    
                    sync_response = requests.post(
                        f"{BASE_URL}/sync-conversation",
                        json=sync_data,
                        headers=headers
                    )
                    
                    if sync_response.status_code == 200:
                        print(f"✅ Conversa {contact_id} sincronizada com sucesso")
                    else:
                        print(f"❌ Erro ao sincronizar conversa: {sync_response.status_code}")
        else:
            print(f"❌ Erro ao obter conversas do WhatsApp: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro na sincronização com WhatsApp: {e}")

def test_message_flow(token):
    """Testa fluxo completo de mensagens."""
    headers = {"Authorization": f"Bearer {token}"}
    
    print("\n💬 Testando fluxo completo de mensagens...")
    
    # Enviar mensagem via chat
    test_message = {
        "query": "Teste de fluxo completo de mensagem",
        "contact_id": f"test_flow_{int(time.time())}@c.us",
        "contact_name": "Teste Fluxo"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/chat/",
            json=test_message,
            headers=headers
        )
        
        if response.status_code == 200:
            chat_response = response.json()
            print(f"✅ Mensagem processada via chat")
            print(f"   Resposta: {chat_response.get('response', 'N/A')[:50]}...")
            
            # Verificar se a conversa foi criada no banco
            time.sleep(1)  # Aguardar processamento
            
            conv_response = requests.get(f"{BASE_URL}/conversations/", headers=headers)
            if conv_response.status_code == 200:
                conversations = conv_response.json()
                # Procurar pela conversa criada
                test_conv = None
                for conv in conversations:
                    if conv.get('contact_id') == test_message['contact_id']:
                        test_conv = conv
                        break
                
                if test_conv:
                    print(f"✅ Conversa criada no banco: ID {test_conv.get('id')}")
                    
                    # Verificar mensagens da conversa
                    msg_response = requests.get(
                        f"{BASE_URL}/conversations/{test_conv['id']}/messages",
                        headers=headers
                    )
                    
                    if msg_response.status_code == 200:
                        messages = msg_response.json()
                        print(f"✅ Mensagens na conversa: {len(messages)}")
                    else:
                        print(f"❌ Erro ao buscar mensagens: {msg_response.status_code}")
                else:
                    print("⚠️ Conversa não encontrada no banco")
        else:
            print(f"❌ Erro no chat: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Erro no fluxo de mensagens: {e}")

def main():
    print("🔄 Testando sincronização com banco de dados...")
    
    # Obter token
    token = get_auth_token()
    if not token:
        print("❌ Não foi possível obter token de autenticação")
        return
    
    print(f"✅ Token obtido: {token[:50]}...")
    
    # Executar testes
    test_conversation_sync(token)
    test_database_queries(token)
    test_whatsapp_sync(token)
    test_message_flow(token)
    
    print("\n🎉 Testes de sincronização concluídos!")

if __name__ == "__main__":
    main()
