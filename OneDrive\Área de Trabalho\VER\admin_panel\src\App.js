import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, CssBaseline } from '@mui/material';
import theme from './theme/theme';
import useAuthStore from './store/authStore';
import DashboardLayout from './components/layout/DashboardLayout';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import DocumentManagementPage from './pages/DocumentManagementPage';
import ChatPage from './pages/ChatPage';
import WhatsAppPage from './pages/WhatsAppPage';
import HistoryPage from './pages/HistoryPage';
import ConversationDetailPage from './pages/ConversationDetailPage';
import InboxPage from './pages/InboxPage';
import ConversationReplyPage from './pages/ConversationReplyPage';

// Componente que envolve as páginas do dashboard com o layout principal
const DashboardRoute = ({ children }) => {
  const { isAuthenticated } = useAuthStore();
  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }
  return <DashboardLayout>{children}</DashboardLayout>;
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Routes>
        <Route path="/login" element={<LoginPage />} />
        
        {/* Rotas que usam o DashboardLayout */}
        <Route path="/" element={<DashboardRoute><DashboardPage /></DashboardRoute>} />
        <Route path="/inbox" element={<DashboardRoute><InboxPage /></DashboardRoute>} />
        <Route path="/inbox/:conversationId" element={<DashboardRoute><ConversationReplyPage /></DashboardRoute>} />
        <Route path="/documents" element={<DashboardRoute><DocumentManagementPage /></DashboardRoute>} />
        <Route path="/chat" element={<DashboardRoute><ChatPage /></DashboardRoute>} />
        <Route path="/history" element={<DashboardRoute><HistoryPage /></DashboardRoute>} />
        <Route path="/history/:conversationId" element={<DashboardRoute><ConversationDetailPage /></DashboardRoute>} />
        <Route path="/whatsapp" element={<DashboardRoute><WhatsAppPage /></DashboardRoute>} />

        {/* Rota de fallback para redirecionar para o dashboard se estiver logado */}
        <Route path="*" element={<Navigate to="/" />} />
        </Routes>
      </Router>
    </ThemeProvider>
  );
}

export default App;



