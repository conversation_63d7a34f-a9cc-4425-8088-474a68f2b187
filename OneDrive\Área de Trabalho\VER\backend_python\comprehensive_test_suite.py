#!/usr/bin/env python3

"""
Suite completa de testes com relatórios (sem dependências de API)
"""

import sys
import os
import time
import json
import statistics
import psutil
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.persona import (
    SYSTEM_PROMPT, RESPONSE_TEMPLATES, EMOJIS, HASHTAGS, PERSONA_CONFIG,
    RAG_SYSTEM_PROMPT, GENERAL_CONVERSATION_PROMPT
)
from pipelines.post_processing import PostProcessor, process_response


class ComprehensiveTestSuite:
    """
    Suite completa de testes com relatórios detalhados
    """
    
    def __init__(self):
        self.results = {}
        self.timestamp = datetime.now()
        self.processor = PostProcessor()
    
    def run_all_tests(self):
        """Executa todos os testes"""
        print("🧪 SUITE COMPLETA DE TESTES DO CHAT DA PERSONA")
        print("=" * 70)
        
        start_time = time.time()
        
        # 1. Testes básicos
        print("\n📋 1. TESTES BÁSICOS")
        print("-" * 40)
        self.results['basic'] = self._run_basic_tests()
        
        # 2. Testes de performance
        print("\n📋 2. TESTES DE PERFORMANCE")
        print("-" * 40)
        self.results['performance'] = self._run_performance_tests()
        
        # 3. Testes de cobertura
        print("\n📋 3. TESTES DE COBERTURA")
        print("-" * 40)
        self.results['coverage'] = self._run_coverage_tests()
        
        # 4. Testes de qualidade
        print("\n📋 4. TESTES DE QUALIDADE")
        print("-" * 40)
        self.results['quality'] = self._run_quality_tests()
        
        # 5. Testes de stress
        print("\n📋 5. TESTES DE STRESS")
        print("-" * 40)
        self.results['stress'] = self._run_stress_tests()
        
        end_time = time.time()
        self.results['metadata'] = {
            'timestamp': self.timestamp.isoformat(),
            'duration': end_time - start_time,
            'total_tests': sum(len(category) for category in self.results.values() if isinstance(category, dict))
        }
        
        # Gerar relatórios
        self._generate_console_report()
        self._generate_detailed_reports()
        
        return self.results
    
    def _run_basic_tests(self):
        """Executa testes básicos"""
        results = {}
        
        # Teste 1: Configuração da persona
        print("   🧪 Configuração da persona...")
        try:
            assert isinstance(SYSTEM_PROMPT, str) and len(SYSTEM_PROMPT) > 100
            assert "Vereadora Rafaela de Nilda" in SYSTEM_PROMPT
            assert isinstance(RESPONSE_TEMPLATES, dict) and len(RESPONSE_TEMPLATES) >= 8
            assert isinstance(EMOJIS, list) and len(EMOJIS) >= 8
            assert isinstance(HASHTAGS, list) and len(HASHTAGS) >= 4
            results['persona_config'] = {'status': 'PASS', 'score': 100}
            print("   ✅ Configuração válida")
        except Exception as e:
            results['persona_config'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        # Teste 2: Pós-processamento
        print("   🧪 Pós-processamento...")
        try:
            test_text = "Olá, como posso ajudar você?"
            processed = process_response(test_text)
            assert isinstance(processed, str)
            assert len(processed) >= len(test_text)
            
            # Testar limpeza
            dirty = "  Texto   com   espaços  "
            clean = self.processor.clean_text(dirty)
            assert clean == "Texto com espaços"
            
            results['post_processing'] = {'status': 'PASS', 'score': 100}
            print("   ✅ Pós-processamento funcionando")
        except Exception as e:
            results['post_processing'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        # Teste 3: Template matching
        print("   🧪 Template matching...")
        try:
            def match_template(query):
                query_lower = query.lower()
                for template_data in RESPONSE_TEMPLATES.values():
                    if any(keyword in query_lower for keyword in template_data["keywords"]):
                        return template_data["response"]
                return None
            
            test_queries = ["Bom dia", "cesta básica", "remédio", "emprego"]
            matches = sum(1 for query in test_queries if match_template(query))
            match_rate = matches / len(test_queries)
            
            assert match_rate >= 0.75
            results['template_matching'] = {'status': 'PASS', 'score': match_rate * 100, 'match_rate': match_rate}
            print(f"   ✅ Template matching: {match_rate:.1%}")
        except Exception as e:
            results['template_matching'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        return results
    
    def _run_performance_tests(self):
        """Executa testes de performance"""
        results = {}
        
        # Teste 1: Velocidade do pós-processamento
        print("   🧪 Velocidade do pós-processamento...")
        try:
            test_texts = [
                "Olá, como posso ajudar você hoje?",
                "Nossa equipe está sempre trabalhando para melhorar.",
                "Juntos somos mais fortes e podemos construir uma cidade melhor."
            ]
            
            times = []
            for text in test_texts:
                start_time = time.time()
                process_response(text)
                end_time = time.time()
                times.append(end_time - start_time)
            
            avg_time = statistics.mean(times)
            max_time = max(times)
            
            results['processing_speed'] = {
                'avg_time_ms': avg_time * 1000,
                'max_time_ms': max_time * 1000,
                'status': 'PASS' if avg_time < 0.1 else 'WARN'
            }
            print(f"   ✅ Velocidade: {avg_time*1000:.1f}ms (média)")
        except Exception as e:
            results['processing_speed'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        # Teste 2: Throughput
        print("   🧪 Throughput...")
        try:
            text = "Teste de throughput"
            num_requests = 100
            
            start_time = time.time()
            for _ in range(num_requests):
                process_response(text)
            end_time = time.time()
            
            total_time = end_time - start_time
            throughput = num_requests / total_time
            
            results['throughput'] = {
                'requests_per_second': throughput,
                'total_requests': num_requests,
                'total_time': total_time,
                'status': 'PASS' if throughput > 50 else 'WARN'
            }
            print(f"   ✅ Throughput: {throughput:.1f} req/s")
        except Exception as e:
            results['throughput'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        # Teste 3: Uso de memória
        print("   🧪 Uso de memória...")
        try:
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Processar muitos textos
            for i in range(1000):
                text = f"Teste de memória {i}"
                process_response(text)
            
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = peak_memory - initial_memory
            
            results['memory_usage'] = {
                'initial_mb': initial_memory,
                'peak_mb': peak_memory,
                'increase_mb': memory_increase,
                'status': 'PASS' if memory_increase < 50 else 'WARN'
            }
            print(f"   ✅ Memória: +{memory_increase:.1f}MB")
        except Exception as e:
            results['memory_usage'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        return results
    
    def _run_coverage_tests(self):
        """Executa testes de cobertura"""
        results = {}
        
        # Teste 1: Cobertura de keywords
        print("   🧪 Cobertura de keywords...")
        try:
            def match_template(query):
                query_lower = query.lower()
                for template_data in RESPONSE_TEMPLATES.values():
                    if any(keyword in query_lower for keyword in template_data["keywords"]):
                        return True
                return False
            
            all_keywords = []
            for template_data in RESPONSE_TEMPLATES.values():
                all_keywords.extend(template_data["keywords"])
            
            tested_keywords = 0
            for keyword in all_keywords:
                if match_template(keyword):
                    tested_keywords += 1
            
            coverage_rate = tested_keywords / len(all_keywords)
            
            results['keyword_coverage'] = {
                'total_keywords': len(all_keywords),
                'tested_keywords': tested_keywords,
                'coverage_rate': coverage_rate,
                'status': 'PASS' if coverage_rate > 0.9 else 'WARN'
            }
            print(f"   ✅ Keywords: {coverage_rate:.1%} ({tested_keywords}/{len(all_keywords)})")
        except Exception as e:
            results['keyword_coverage'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        # Teste 2: Cobertura de emojis
        print("   🧪 Cobertura de emojis...")
        try:
            used_emojis = set()
            
            test_contexts = [
                "Vou verificar na UBS", "Vamos trabalhar juntos", "Que Deus nos abençoe",
                "Transparência é fundamental", "Nossa equipe ajuda", "Inclusão é importante",
                "Parnamirim merece o melhor", "Juntos somos mais fortes"
            ]
            
            for context in test_contexts:
                emoji = self.processor._select_contextual_emoji(context)
                used_emojis.add(emoji)
            
            # Adicionar testes aleatórios
            for _ in range(20):
                emoji = self.processor._select_contextual_emoji("Texto genérico")
                used_emojis.add(emoji)
            
            emoji_coverage = len(used_emojis) / len(EMOJIS)
            
            results['emoji_coverage'] = {
                'total_emojis': len(EMOJIS),
                'used_emojis': len(used_emojis),
                'coverage_rate': emoji_coverage,
                'status': 'PASS' if emoji_coverage > 0.7 else 'WARN'
            }
            print(f"   ✅ Emojis: {emoji_coverage:.1%} ({len(used_emojis)}/{len(EMOJIS)})")
        except Exception as e:
            results['emoji_coverage'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        return results
    
    def _run_quality_tests(self):
        """Executa testes de qualidade"""
        results = {}
        
        # Teste 1: Qualidade das respostas
        print("   🧪 Qualidade das respostas...")
        try:
            quality_scores = []
            
            for template_data in RESPONSE_TEMPLATES.values():
                response = template_data["response"]
                score = 0
                
                # Comprimento adequado (20-200 chars)
                if 20 <= len(response) <= 200:
                    score += 25
                
                # Elementos da persona
                persona_elements = ["você", "nossa", "juntos", "equipe", "ajudar"]
                if any(element in response.lower() for element in persona_elements):
                    score += 25
                
                # Pontuação correta
                if response.strip().endswith(('.', '!', '?')):
                    score += 25
                
                # Tom positivo
                positive_words = ["ajudar", "juntos", "sempre", "melhor", "força"]
                if any(word in response.lower() for word in positive_words):
                    score += 25
                
                quality_scores.append(score)
            
            avg_quality = statistics.mean(quality_scores)
            
            results['response_quality'] = {
                'average_score': avg_quality,
                'min_score': min(quality_scores),
                'max_score': max(quality_scores),
                'total_responses': len(quality_scores),
                'status': 'PASS' if avg_quality >= 80 else 'WARN'
            }
            print(f"   ✅ Qualidade: {avg_quality:.1f}% (média)")
        except Exception as e:
            results['response_quality'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        # Teste 2: Consistência da persona
        print("   🧪 Consistência da persona...")
        try:
            consistency_score = 0
            total_checks = 0
            
            # Verificar system prompt
            total_checks += 1
            if all(element in SYSTEM_PROMPT for element in ["Rafaela", "Vereadora", "Parnamirim"]):
                consistency_score += 1
            
            # Verificar respostas
            for template_data in RESPONSE_TEMPLATES.values():
                response = template_data["response"]
                total_checks += 1
                
                # Deve ter tom da persona
                persona_indicators = ["você", "nossa", "juntos", "equipe"]
                if any(indicator in response.lower() for indicator in persona_indicators):
                    consistency_score += 1
            
            consistency_rate = consistency_score / total_checks
            
            results['persona_consistency'] = {
                'consistency_rate': consistency_rate,
                'passed_checks': consistency_score,
                'total_checks': total_checks,
                'status': 'PASS' if consistency_rate >= 0.8 else 'WARN'
            }
            print(f"   ✅ Consistência: {consistency_rate:.1%}")
        except Exception as e:
            results['persona_consistency'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        return results
    
    def _run_stress_tests(self):
        """Executa testes de stress"""
        results = {}
        
        # Teste 1: Processamento concorrente
        print("   🧪 Processamento concorrente...")
        try:
            def process_batch():
                for _ in range(50):
                    process_response("Teste de stress concorrente")
                return True
            
            num_threads = 5
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(process_batch) for _ in range(num_threads)]
                results_list = [future.result() for future in futures]
            
            end_time = time.time()
            total_time = end_time - start_time
            total_requests = num_threads * 50
            concurrent_throughput = total_requests / total_time
            
            results['concurrent_processing'] = {
                'total_requests': total_requests,
                'total_time': total_time,
                'throughput': concurrent_throughput,
                'threads': num_threads,
                'status': 'PASS' if concurrent_throughput > 30 else 'WARN'
            }
            print(f"   ✅ Concorrência: {concurrent_throughput:.1f} req/s ({num_threads} threads)")
        except Exception as e:
            results['concurrent_processing'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        # Teste 2: Stress de templates
        print("   🧪 Stress de templates...")
        try:
            def match_template(query):
                query_lower = query.lower()
                for template_data in RESPONSE_TEMPLATES.values():
                    if any(keyword in query_lower for keyword in template_data["keywords"]):
                        return True
                return False
            
            # Gerar muitas queries
            stress_queries = []
            for template_data in RESPONSE_TEMPLATES.values():
                for keyword in template_data["keywords"]:
                    stress_queries.extend([
                        keyword, keyword.upper(), f"Preciso de {keyword}",
                        f"Como faço {keyword}?", f"Informações sobre {keyword}"
                    ])
            
            start_time = time.time()
            matches = sum(1 for query in stress_queries if match_template(query))
            end_time = time.time()
            
            total_time = end_time - start_time
            queries_per_second = len(stress_queries) / total_time
            
            results['template_stress'] = {
                'total_queries': len(stress_queries),
                'matches': matches,
                'total_time': total_time,
                'queries_per_second': queries_per_second,
                'status': 'PASS' if queries_per_second > 1000 else 'WARN'
            }
            print(f"   ✅ Template stress: {queries_per_second:.0f} q/s ({len(stress_queries)} queries)")
        except Exception as e:
            results['template_stress'] = {'status': 'FAIL', 'error': str(e)}
            print(f"   ❌ Erro: {e}")
        
        return results
    
    def _generate_console_report(self):
        """Gera relatório no console"""
        print("\n" + "=" * 70)
        print("📊 RELATÓRIO FINAL COMPLETO")
        print("=" * 70)
        
        # Calcular estatísticas gerais
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.results.items():
            if category == 'metadata':
                continue
            
            for test_name, test_result in tests.items():
                total_tests += 1
                if test_result.get('status') == 'PASS':
                    passed_tests += 1
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n⏱️  Tempo total: {self.results['metadata']['duration']:.2f} segundos")
        print(f"📅 Data/hora: {self.timestamp.strftime('%d/%m/%Y %H:%M:%S')}")
        print(f"🧪 Testes executados: {total_tests}")
        print(f"✅ Testes aprovados: {passed_tests}")
        print(f"📈 Taxa de sucesso: {success_rate:.1f}%")
        
        # Resumo por categoria
        print(f"\n📋 RESUMO POR CATEGORIA:")
        for category, tests in self.results.items():
            if category == 'metadata':
                continue
            
            category_passed = sum(1 for test in tests.values() if test.get('status') == 'PASS')
            category_total = len(tests)
            category_rate = (category_passed / category_total) * 100 if category_total > 0 else 0
            
            print(f"   {category.upper()}: {category_passed}/{category_total} ({category_rate:.1f}%)")
        
        # Status final
        if success_rate >= 90:
            print(f"\n🎉 EXCELENTE! Sistema aprovado com {success_rate:.1f}% de sucesso!")
        elif success_rate >= 80:
            print(f"\n✅ BOM! Sistema aprovado com {success_rate:.1f}% de sucesso!")
        elif success_rate >= 70:
            print(f"\n⚠️  ATENÇÃO! Sistema precisa de melhorias ({success_rate:.1f}% de sucesso)")
        else:
            print(f"\n❌ CRÍTICO! Sistema precisa de correções urgentes ({success_rate:.1f}% de sucesso)")
        
        print("=" * 70)
    
    def _generate_detailed_reports(self):
        """Gera relatórios detalhados em arquivos"""
        # Criar diretório de relatórios
        reports_dir = os.path.join(os.path.dirname(__file__), 'reports')
        os.makedirs(reports_dir, exist_ok=True)
        
        timestamp_str = self.timestamp.strftime('%Y%m%d_%H%M%S')
        
        # 1. Relatório JSON
        json_file = os.path.join(reports_dir, f'comprehensive_report_{timestamp_str}.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        print(f"📄 Relatório JSON: {json_file}")
        
        # 2. Relatório de texto
        txt_file = os.path.join(reports_dir, f'summary_{timestamp_str}.txt')
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(f"RELATÓRIO COMPLETO - CHAT DA PERSONA\n")
            f.write(f"{'='*50}\n\n")
            f.write(f"Data/Hora: {self.timestamp.strftime('%d/%m/%Y %H:%M:%S')}\n")
            f.write(f"Duração: {self.results['metadata']['duration']:.2f} segundos\n\n")
            
            for category, tests in self.results.items():
                if category == 'metadata':
                    continue
                
                f.write(f"{category.upper()}:\n")
                for test_name, test_result in tests.items():
                    status = test_result.get('status', 'UNKNOWN')
                    f.write(f"  - {test_name}: {status}\n")
                f.write("\n")
        
        print(f"📋 Relatório de texto: {txt_file}")
        
        # 3. Relatório CSV para análise
        csv_file = os.path.join(reports_dir, f'metrics_{timestamp_str}.csv')
        with open(csv_file, 'w', encoding='utf-8') as f:
            f.write("Category,Test,Status,Score,Metric,Value\n")
            
            for category, tests in self.results.items():
                if category == 'metadata':
                    continue
                
                for test_name, test_result in tests.items():
                    status = test_result.get('status', 'UNKNOWN')
                    score = test_result.get('score', '')
                    
                    # Extrair métricas específicas
                    for key, value in test_result.items():
                        if key not in ['status', 'error']:
                            f.write(f"{category},{test_name},{status},{score},{key},{value}\n")
        
        print(f"📊 Relatório CSV: {csv_file}")


def main():
    """Função principal"""
    suite = ComprehensiveTestSuite()
    results = suite.run_all_tests()
    
    print(f"\n🎉 SUITE COMPLETA DE TESTES CONCLUÍDA!")
    print(f"📁 Relatórios salvos na pasta 'reports'")
    
    return results


if __name__ == "__main__":
    main()
