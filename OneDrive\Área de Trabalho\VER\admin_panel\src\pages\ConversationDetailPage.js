import React, { useRef, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { Typography, Paper, Box, CircularProgress, Alert } from '@mui/material';
import axiosInstance from '../api/axiosInstance';

const fetchMessages = async (conversationId) => {
  const { data } = await axiosInstance.get(`/conversations/${conversationId}/messages`);
  return data;
};

const ConversationDetailPage = () => {
  const { conversationId } = useParams();
  const messagesEndRef = useRef(null);

  const { data: messages, isLoading, isError, error } = useQuery({
    queryKey: ['messages', conversationId],
    queryFn: () => fetchMessages(conversationId),
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  if (isLoading) return <CircularProgress />;
  if (isError) return <Alert severity="error">Erro ao buscar mensagens: {error.message}</Alert>;

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: 'calc(100vh - 120px)' }}>
      <Typography variant="h4" sx={{ mb: 2 }}>
        Detalhes da Conversa
      </Typography>
      <Paper elevation={2} sx={{ flexGrow: 1, p: 2, overflowY: 'auto' }}>
        {messages.map((msg) => (
          <Box 
            key={msg.id} 
            sx={{ 
              display: 'flex', 
              justifyContent: msg.sender === 'user' ? 'flex-end' : 'flex-start',
              mb: 1,
            }}
          >
            <Paper 
              elevation={1}
              sx={{ 
                p: 1.5, 
                backgroundColor: msg.sender === 'user' ? 'primary.main' : 'grey.200',
                color: msg.sender === 'user' ? 'primary.contrastText' : 'text.primary',
                borderRadius: msg.sender === 'user' ? '20px 20px 5px 20px' : '20px 20px 20px 5px',
                maxWidth: '70%',
                whiteSpace: 'pre-wrap'
              }}
            >
              {msg.content}
            </Paper>
          </Box>
        ))}
        <div ref={messagesEndRef} />
      </Paper>
    </Box>
  );
};

export default ConversationDetailPage;
