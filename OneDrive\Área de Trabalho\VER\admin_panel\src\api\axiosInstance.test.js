import axios from 'axios';
import axiosInstance from './axiosInstance';
import useAuthStore from '../store/authStore';

// Mock axios
jest.mock('axios');

// Mock the auth store
jest.mock('../store/authStore');

// Mock window.location
delete window.location;
window.location = { href: '' };

describe('axiosInstance', () => {
  const mockAxiosCreate = jest.fn();
  const mockRequest = { headers: {} };
  const mockResponse = { data: 'test' };
  const mockError = { response: { status: 401 } };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock axios.create
    axios.create.mockReturnValue({
      interceptors: {
        request: { use: jest.fn() },
        response: { use: jest.fn() }
      }
    });
  });

  it('should create axios instance with correct baseURL in development', () => {
    // Set NODE_ENV to development
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    // Re-import to get fresh instance
    jest.resetModules();
    require('./axiosInstance');

    expect(axios.create).toHaveBeenCalledWith({
      baseURL: 'http://localhost:8000'
    });

    // Restore original NODE_ENV
    process.env.NODE_ENV = originalEnv;
  });

  it('should create axios instance with production URL when in production', () => {
    const originalEnv = process.env.NODE_ENV;
    const originalApiUrl = process.env.REACT_APP_API_URL;
    
    process.env.NODE_ENV = 'production';
    process.env.REACT_APP_API_URL = 'https://api.example.com';

    // Re-import to get fresh instance
    jest.resetModules();
    require('./axiosInstance');

    expect(axios.create).toHaveBeenCalledWith({
      baseURL: 'https://api.example.com'
    });

    // Restore original values
    process.env.NODE_ENV = originalEnv;
    process.env.REACT_APP_API_URL = originalApiUrl;
  });

  describe('request interceptor', () => {
    let requestInterceptor;

    beforeEach(() => {
      // Get the request interceptor function
      const mockAxiosInstance = {
        interceptors: {
          request: { 
            use: jest.fn((success, error) => {
              requestInterceptor = { success, error };
            })
          },
          response: { use: jest.fn() }
        }
      };
      
      axios.create.mockReturnValue(mockAxiosInstance);
      
      // Re-import to set up interceptors
      jest.resetModules();
      require('./axiosInstance');
    });

    it('should add authorization header when token exists', () => {
      const mockToken = 'test-token-123';
      useAuthStore.getState = jest.fn().mockReturnValue({
        authToken: mockToken
      });

      const config = { headers: {} };
      const result = requestInterceptor.success(config);

      expect(result.headers['Authorization']).toBe(`Bearer ${mockToken}`);
    });

    it('should not add authorization header when token does not exist', () => {
      useAuthStore.getState = jest.fn().mockReturnValue({
        authToken: null
      });

      const config = { headers: {} };
      const result = requestInterceptor.success(config);

      expect(result.headers['Authorization']).toBeUndefined();
    });

    it('should reject promise on request error', () => {
      const error = new Error('Request error');
      
      expect(requestInterceptor.error(error)).rejects.toBe(error);
    });
  });

  describe('response interceptor', () => {
    let responseInterceptor;
    const mockLogout = jest.fn();

    beforeEach(() => {
      // Get the response interceptor function
      const mockAxiosInstance = {
        interceptors: {
          request: { use: jest.fn() },
          response: { 
            use: jest.fn((success, error) => {
              responseInterceptor = { success, error };
            })
          }
        }
      };
      
      axios.create.mockReturnValue(mockAxiosInstance);
      
      // Mock the auth store
      useAuthStore.getState = jest.fn().mockReturnValue({
        logout: mockLogout
      });
      
      // Re-import to set up interceptors
      jest.resetModules();
      require('./axiosInstance');
    });

    it('should return response on success', () => {
      const response = { data: 'test data' };
      const result = responseInterceptor.success(response);
      
      expect(result).toBe(response);
    });

    it('should logout and redirect on 401 error', async () => {
      const error = {
        response: { status: 401 }
      };

      try {
        await responseInterceptor.error(error);
      } catch (e) {
        expect(mockLogout).toHaveBeenCalled();
        expect(window.location.href).toBe('/login');
        expect(e).toBe(error);
      }
    });

    it('should not logout on non-401 errors', async () => {
      const error = {
        response: { status: 500 }
      };

      try {
        await responseInterceptor.error(error);
      } catch (e) {
        expect(mockLogout).not.toHaveBeenCalled();
        expect(window.location.href).toBe('');
        expect(e).toBe(error);
      }
    });

    it('should handle errors without response object', async () => {
      const error = new Error('Network error');

      try {
        await responseInterceptor.error(error);
      } catch (e) {
        expect(mockLogout).not.toHaveBeenCalled();
        expect(e).toBe(error);
      }
    });
  });
});
