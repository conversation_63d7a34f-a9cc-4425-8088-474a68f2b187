#!/usr/bin/env python3

"""
Suite principal de testes para o chat da persona
"""

import unittest
import sys
import os
import time
from io import StringIO

# Adicionar o diretório atual ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Importar todos os módulos de teste
from tests.test_persona_config import TestPersonaConfig
from tests.test_post_processing import TestPostProcessing
from tests.test_intent_handlers import TestIntentHandlers
from tests.test_chat_pipeline import TestChatPipeline
from tests.test_integration import TestChatPersonaIntegration


class ColoredTextTestResult(unittest.TextTestResult):
    """
    Resultado de teste com cores para melhor visualização
    """
    
    def __init__(self, stream, descriptions, verbosity):
        super().__init__(stream, descriptions, verbosity)
        self.success_count = 0
        
    def addSuccess(self, test):
        super().addSuccess(test)
        self.success_count += 1
        if self.verbosity > 1:
            self.stream.write("✅ ")
            self.stream.write(str(test))
            self.stream.write("\n")
    
    def addError(self, test, err):
        super().addError(test, err)
        if self.verbosity > 1:
            self.stream.write("❌ ")
            self.stream.write(str(test))
            self.stream.write(" - ERROR\n")
    
    def addFailure(self, test, err):
        super().addFailure(test, err)
        if self.verbosity > 1:
            self.stream.write("❌ ")
            self.stream.write(str(test))
            self.stream.write(" - FAIL\n")
    
    def addSkip(self, test, reason):
        super().addSkip(test, reason)
        if self.verbosity > 1:
            self.stream.write("⏭️  ")
            self.stream.write(str(test))
            self.stream.write(f" - SKIP ({reason})\n")


def create_test_suite():
    """
    Cria a suite completa de testes
    """
    suite = unittest.TestSuite()
    
    # Adicionar testes de configuração da persona
    suite.addTest(unittest.makeSuite(TestPersonaConfig))
    
    # Adicionar testes de pós-processamento
    suite.addTest(unittest.makeSuite(TestPostProcessing))
    
    # Adicionar testes de handlers
    suite.addTest(unittest.makeSuite(TestIntentHandlers))
    
    # Adicionar testes de pipeline de chat
    suite.addTest(unittest.makeSuite(TestChatPipeline))
    
    # Adicionar testes de integração
    suite.addTest(unittest.makeSuite(TestChatPersonaIntegration))
    
    return suite


def run_specific_test_module(module_name):
    """
    Executa um módulo específico de testes
    """
    print(f"\n🧪 EXECUTANDO TESTES: {module_name}")
    print("=" * 60)
    
    if module_name == "persona":
        suite = unittest.makeSuite(TestPersonaConfig)
    elif module_name == "post_processing":
        suite = unittest.makeSuite(TestPostProcessing)
    elif module_name == "handlers":
        suite = unittest.makeSuite(TestIntentHandlers)
    elif module_name == "pipeline":
        suite = unittest.makeSuite(TestChatPipeline)
    elif module_name == "integration":
        suite = unittest.makeSuite(TestChatPersonaIntegration)
    else:
        print(f"❌ Módulo '{module_name}' não encontrado")
        return False
    
    runner = unittest.TextTestRunner(
        verbosity=2,
        resultclass=ColoredTextTestResult
    )
    result = runner.run(suite)
    
    return result.wasSuccessful()


def run_all_tests():
    """
    Executa todos os testes
    """
    print("🧪 EXECUTANDO SUITE COMPLETA DE TESTES DO CHAT DA PERSONA")
    print("=" * 70)
    
    start_time = time.time()
    
    # Criar e executar suite
    suite = create_test_suite()
    runner = unittest.TextTestRunner(
        verbosity=2,
        resultclass=ColoredTextTestResult
    )
    
    result = runner.run(suite)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # Relatório final
    print("\n" + "=" * 70)
    print("📊 RELATÓRIO FINAL DOS TESTES")
    print("=" * 70)
    
    print(f"\n⏱️  Tempo de execução: {duration:.2f} segundos")
    print(f"🧪 Testes executados: {result.testsRun}")
    print(f"✅ Sucessos: {result.success_count}")
    print(f"❌ Falhas: {len(result.failures)}")
    print(f"❌ Erros: {len(result.errors)}")
    print(f"⏭️  Pulados: {len(result.skipped)}")
    
    # Calcular taxa de sucesso
    if result.testsRun > 0:
        success_rate = (result.success_count / result.testsRun) * 100
        print(f"📈 Taxa de sucesso: {success_rate:.1f}%")
    
    # Mostrar detalhes de falhas se houver
    if result.failures:
        print(f"\n❌ FALHAS DETALHADAS:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n❌ ERROS DETALHADOS:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    # Status final
    if result.wasSuccessful():
        print(f"\n🎉 TODOS OS TESTES PASSARAM COM SUCESSO!")
        print("✅ O sistema de chat da persona está funcionando corretamente!")
    else:
        print(f"\n⚠️  ALGUNS TESTES FALHARAM")
        print("🔧 Verifique os erros acima e corrija antes de prosseguir")
    
    print("=" * 70)
    
    return result.wasSuccessful()


def show_test_coverage():
    """
    Mostra a cobertura dos testes
    """
    print("📋 COBERTURA DOS TESTES")
    print("=" * 50)
    
    coverage_areas = {
        "Configuração da Persona": [
            "✅ System prompt validação",
            "✅ Templates de resposta",
            "✅ Emojis e hashtags",
            "✅ Configurações gerais",
            "✅ Prompts especializados"
        ],
        "Pós-processamento": [
            "✅ Limpeza de texto",
            "✅ Adição de emojis",
            "✅ Adição de hashtags",
            "✅ Limitação de comprimento",
            "✅ Seleção contextual",
            "✅ Pipeline completo"
        ],
        "Handlers de Intenção": [
            "✅ Template queries",
            "✅ RAG queries",
            "✅ Conversa geral",
            "✅ Escalação humana",
            "✅ Emergências",
            "✅ Busca de documentos"
        ],
        "Pipeline de Chat": [
            "✅ Formatação de mensagens",
            "✅ Roteamento de intenção",
            "✅ Geração de respostas",
            "✅ Tratamento de erros",
            "✅ Resumo de conversas"
        ],
        "Integração": [
            "✅ Fluxo completo de templates",
            "✅ Integração RAG + pós-processamento",
            "✅ Consistência da persona",
            "✅ Elementos contextuais",
            "✅ Simulação de conversas"
        ]
    }
    
    for area, tests in coverage_areas.items():
        print(f"\n🎯 {area}:")
        for test in tests:
            print(f"   {test}")
    
    print(f"\n📊 Total de áreas cobertas: {len(coverage_areas)}")
    total_tests = sum(len(tests) for tests in coverage_areas.values())
    print(f"📊 Total de funcionalidades testadas: {total_tests}")


def main():
    """
    Função principal
    """
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "coverage":
            show_test_coverage()
        elif command in ["persona", "post_processing", "handlers", "pipeline", "integration"]:
            success = run_specific_test_module(command)
            sys.exit(0 if success else 1)
        elif command == "help":
            print("🧪 COMANDOS DISPONÍVEIS:")
            print("  python run_tests.py              - Executar todos os testes")
            print("  python run_tests.py persona      - Testar configuração da persona")
            print("  python run_tests.py post_processing - Testar pós-processamento")
            print("  python run_tests.py handlers     - Testar handlers de intenção")
            print("  python run_tests.py pipeline     - Testar pipeline de chat")
            print("  python run_tests.py integration  - Testar integração")
            print("  python run_tests.py coverage     - Mostrar cobertura")
            print("  python run_tests.py help         - Mostrar esta ajuda")
        else:
            print(f"❌ Comando '{command}' não reconhecido. Use 'help' para ver comandos disponíveis.")
            sys.exit(1)
    else:
        # Executar todos os testes
        success = run_all_tests()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
