"""
Pipeline de chat usando lista de mensagens com <PERSON><PERSON>hain
"""

from typing import List, Dict, Optional, Tuple
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_google_genai import ChatGoogleGenerativeAI
from config.persona import (
    SYSTEM_PROMPT, 
    RAG_SYSTEM_PROMPT, 
    GENERAL_CONVERSATION_PROMPT,
    INTENT_ROUTING_PROMPT,
    PERSONA_CONFIG
)
import os


class ChatPipeline:
    """
    Pipeline de chat usando lista de mensagens estruturadas.
    """
    
    def __init__(self):
        self.intent_router_model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash", 
            google_api_key=os.getenv("GEMINI_API_KEY"), 
            temperature=0
        )
        
        self.chat_model = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash", 
            google_api_key=os.getenv("GEMINI_API_KEY"), 
            temperature=PERSONA_CONFIG["temperature"]
        )
    
    def format_conversation_history(self, messages: List[Dict]) -> List:
        """
        Converte histórico de mensagens para formato LangChain.
        
        Args:
            messages: Lista de mensagens do banco de dados
            
        Returns:
            Lista de mensagens formatadas para LangChain
        """
        formatted_messages = []
        
        for msg in messages:
            sender = msg.get('sender', '')
            content = msg.get('content', '')
            
            if sender == 'human' or sender == 'user':
                formatted_messages.append(HumanMessage(content=content))
            elif sender == 'ai' or sender == 'assistant':
                formatted_messages.append(AIMessage(content=content))
        
        return formatted_messages
    
    async def route_intent(self, query: str, history_messages: List[Dict]) -> str:
        """
        Determina a intenção da mensagem usando o roteador.
        
        Args:
            query: Pergunta atual do usuário
            history_messages: Histórico de mensagens
            
        Returns:
            Nome da intenção detectada
        """
        try:
            # Formatar histórico para string
            history_str = ""
            if history_messages:
                history_str = "\n".join([
                    f"{msg['sender']}: {msg['content']}" 
                    for msg in history_messages[-4:]  # Últimas 4 mensagens
                ])
            
            # Criar prompt de roteamento
            routing_prompt = INTENT_ROUTING_PROMPT.format(
                history=history_str,
                query=query
            )
            
            # Criar mensagens para o modelo
            messages = [
                SystemMessage(content="Você é um classificador de intenções preciso e eficiente."),
                HumanMessage(content=routing_prompt)
            ]
            
            # Obter resposta do modelo
            response = await self.intent_router_model.ainvoke(messages)
            
            # Limpar e retornar resposta
            intent = response.content.strip().replace("'", "").replace('"', '')
            print(f"🎯 Intenção detectada: {intent}")
            return intent
            
        except Exception as e:
            print(f"❌ Erro ao rotear intenção: {e}")
            return "search_documents_rag"  # Fallback seguro
    
    async def generate_rag_response(self, query: str, context: str, history_messages: List[Dict]) -> str:
        """
        Gera resposta usando RAG com documentos.
        
        Args:
            query: Pergunta do usuário
            context: Contexto dos documentos recuperados
            history_messages: Histórico da conversa
            
        Returns:
            Resposta gerada
        """
        try:
            # Criar mensagens estruturadas
            messages = [
                SystemMessage(content=RAG_SYSTEM_PROMPT)
            ]
            
            # Adicionar histórico da conversa
            if history_messages:
                formatted_history = self.format_conversation_history(history_messages[-4:])
                messages.extend(formatted_history)
            
            # Adicionar contexto e pergunta atual
            context_message = f"""
CONTEXTO DOS DOCUMENTOS:
{context}

PERGUNTA ATUAL: {query}
"""
            messages.append(HumanMessage(content=context_message))
            
            # Gerar resposta
            response = await self.chat_model.ainvoke(messages)
            return response.content
            
        except Exception as e:
            print(f"❌ Erro ao gerar resposta RAG: {e}")
            return None
    
    async def generate_general_response(self, query: str, history_messages: List[Dict]) -> str:
        """
        Gera resposta para conversa geral.
        
        Args:
            query: Pergunta do usuário
            history_messages: Histórico da conversa
            
        Returns:
            Resposta gerada
        """
        try:
            # Criar mensagens estruturadas
            messages = [
                SystemMessage(content=GENERAL_CONVERSATION_PROMPT)
            ]
            
            # Adicionar histórico da conversa
            if history_messages:
                formatted_history = self.format_conversation_history(history_messages[-4:])
                messages.extend(formatted_history)
            
            # Adicionar pergunta atual
            messages.append(HumanMessage(content=query))
            
            # Gerar resposta
            response = await self.chat_model.ainvoke(messages)
            return response.content
            
        except Exception as e:
            print(f"❌ Erro ao gerar resposta geral: {e}")
            return None
    
    async def generate_contextual_response(
        self, 
        query: str, 
        context: Optional[str] = None, 
        history_messages: Optional[List[Dict]] = None,
        system_prompt: Optional[str] = None
    ) -> str:
        """
        Gera resposta contextual usando sistema de mensagens.
        
        Args:
            query: Pergunta do usuário
            context: Contexto adicional (opcional)
            history_messages: Histórico da conversa (opcional)
            system_prompt: Prompt de sistema customizado (opcional)
            
        Returns:
            Resposta gerada
        """
        try:
            # Usar prompt padrão se não fornecido
            if not system_prompt:
                system_prompt = SYSTEM_PROMPT
            
            # Criar mensagens estruturadas
            messages = [
                SystemMessage(content=system_prompt)
            ]
            
            # Adicionar histórico da conversa
            if history_messages:
                formatted_history = self.format_conversation_history(history_messages[-6:])
                messages.extend(formatted_history)
            
            # Preparar mensagem atual
            current_message = query
            if context:
                current_message = f"""
CONTEXTO ADICIONAL:
{context}

PERGUNTA: {query}
"""
            
            messages.append(HumanMessage(content=current_message))
            
            # Gerar resposta
            response = await self.chat_model.ainvoke(messages)
            return response.content
            
        except Exception as e:
            print(f"❌ Erro ao gerar resposta contextual: {e}")
            return None
    
    def create_conversation_summary(self, messages: List[Dict], max_length: int = 500) -> str:
        """
        Cria um resumo da conversa para contexto.
        
        Args:
            messages: Lista de mensagens
            max_length: Comprimento máximo do resumo
            
        Returns:
            Resumo da conversa
        """
        if not messages:
            return ""
        
        # Pegar últimas mensagens
        recent_messages = messages[-6:]
        
        # Criar resumo
        summary_parts = []
        for msg in recent_messages:
            sender = "Usuário" if msg['sender'] in ['human', 'user'] else "Rafaela"
            content = msg['content'][:100]  # Limitar conteúdo
            summary_parts.append(f"{sender}: {content}")
        
        summary = "\n".join(summary_parts)
        
        # Limitar comprimento total
        if len(summary) > max_length:
            summary = summary[:max_length] + "..."
        
        return summary


# Instância global do pipeline
chat_pipeline = ChatPipeline()


async def route_intent(query: str, history_messages: List[Dict]) -> str:
    """Função de conveniência para roteamento de intenção."""
    return await chat_pipeline.route_intent(query, history_messages)


async def generate_rag_response(query: str, context: str, history_messages: List[Dict]) -> str:
    """Função de conveniência para resposta RAG."""
    return await chat_pipeline.generate_rag_response(query, context, history_messages)


async def generate_general_response(query: str, history_messages: List[Dict]) -> str:
    """Função de conveniência para resposta geral."""
    return await chat_pipeline.generate_general_response(query, history_messages)


async def generate_contextual_response(
    query: str, 
    context: Optional[str] = None, 
    history_messages: Optional[List[Dict]] = None,
    system_prompt: Optional[str] = None
) -> str:
    """Função de conveniência para resposta contextual."""
    return await chat_pipeline.generate_contextual_response(
        query, context, history_messages, system_prompt
    )
