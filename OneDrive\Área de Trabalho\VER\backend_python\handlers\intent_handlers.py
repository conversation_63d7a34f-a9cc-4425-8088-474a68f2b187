"""
Handlers para diferentes tipos de intenções do chatbot
"""

from typing import List, Dict, Optional
import random
import sys
import os

# Adicionar path para importações
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.persona import RESPONSE_TEMPLATES
from pipelines.chat_pipeline import generate_rag_response, generate_general_response
from pipelines.post_processing import process_response

# Importar novos sistemas
try:
    from memory.conversation_memory import conversation_memory
    from cache.intelligent_cache import cache_manager
    MEMORY_ENABLED = True
    CACHE_ENABLED = True
except ImportError:
    MEMORY_ENABLED = False
    CACHE_ENABLED = False
    print("⚠️ Memória conversacional e cache não disponíveis")


class IntentHandlers:
    """
    Classe que centraliza todos os handlers de intenção.
    """
    
    def __init__(self, supabase_client=None):
        self.supabase = supabase_client
        self.templates = RESPONSE_TEMPLATES
    
    async def handle_template_query(self, query: str, user_id: str = None, conversation_id: str = None) -> Optional[str]:
        """
        Processa consultas que podem ser respondidas com templates.
        Agora com memória conversacional e cache inteligente.

        Args:
            query: Pergunta do usuário
            user_id: ID do usuário (para memória)
            conversation_id: ID da conversa (para contexto)

        Returns:
            Resposta do template ou None se não encontrar
        """
        # Verificar cache primeiro
        if CACHE_ENABLED:
            cached_response = await cache_manager.get_template_match(query)
            if cached_response:
                print(f"🎯 Cache hit para template: {query[:30]}...")
                return cached_response

        query_lower = query.lower()

        # Extrair informações do usuário se disponível
        if MEMORY_ENABLED and user_id:
            await conversation_memory.extract_user_info_from_message(user_id, query)

        # Procurar por palavras-chave nos templates
        for template_key, template_data in self.templates.items():
            keywords = template_data["keywords"]

            # Verificar se alguma palavra-chave está presente
            if any(keyword in query_lower for keyword in keywords):
                response = template_data["response"]

                # Personalizar resposta com memória conversacional
                if MEMORY_ENABLED and user_id:
                    response = await self._personalize_response(response, user_id, conversation_id)

                print(f"📋 Template usado: {template_key}")
                processed_response = process_response(response)

                # Armazenar no cache
                if CACHE_ENABLED:
                    await cache_manager.cache_template_match(query, processed_response)

                return processed_response

        return None

    async def _personalize_response(self, response: str, user_id: str, conversation_id: str = None) -> str:
        """
        Personaliza resposta baseada na memória conversacional
        """
        if not MEMORY_ENABLED:
            return response

        try:
            # Obter contexto personalizado
            context = await conversation_memory.get_personalized_response_context(user_id, conversation_id or f"conv_{user_id}")

            # Personalizar com nome
            if context.get('user_name'):
                response = response.replace("{nome}", context['user_name'])
                response = response.replace("você", f"você, {context['user_name']}")

            # Personalizar com bairro
            if context.get('user_neighborhood'):
                response = response.replace("{bairro}", context['user_neighborhood'])
                if "sua região" in response:
                    response = response.replace("sua região", f"o {context['user_neighborhood']}")

            # Ajustar tom baseado na preferência
            if context.get('preferred_tone') == 'formal':
                response = response.replace("Oi", "Olá")
                response = response.replace("oi", "olá")
                response = response.replace("Beleza", "Perfeito")
            elif context.get('preferred_tone') == 'casual':
                response = response.replace("Olá", "Oi")
                response = response.replace("olá", "oi")

            # Adicionar contexto de interações anteriores
            if context.get('interaction_count', 0) > 1:
                if "primeira vez" in response:
                    response = response.replace("primeira vez", "novamente")

            return response

        except Exception as e:
            print(f"Erro na personalização: {e}")
            return response

    async def handle_rag_query(self, query: str, conversation_history: List[Dict], user_id: str = None) -> Optional[str]:
        """
        Processa consultas que precisam de busca em documentos.
        Agora com cache inteligente.

        Args:
            query: Pergunta do usuário
            conversation_history: Histórico da conversa
            user_id: ID do usuário (para cache personalizado)

        Returns:
            Resposta baseada em documentos ou None se não encontrar
        """
        # Verificar cache RAG primeiro
        if CACHE_ENABLED:
            cached_response = await cache_manager.get_rag_context(query)
            if cached_response:
                print(f"🎯 Cache hit para RAG: {query[:30]}...")
                return process_response(cached_response)

        try:
            # Buscar documentos relevantes
            context = await self._search_documents(query)

            if not context:
                print("📄 Nenhum documento relevante encontrado")
                return None

            # Gerar resposta usando RAG
            response = await generate_rag_response(query, context, conversation_history)

            if response:
                print(f"📚 Resposta RAG gerada com {len(context)} caracteres de contexto")
                processed_response = process_response(response)

                # Armazenar no cache
                if CACHE_ENABLED:
                    await cache_manager.cache_rag_context(query, response)

                # Atualizar histórico de interações
                if MEMORY_ENABLED and user_id:
                    await conversation_memory.update_interaction_history(
                        user_id,
                        topics_discussed=query[:50],  # Primeiras 50 chars como tópico
                        services_requested="rag_search"
                    )

                return processed_response

            return None

        except Exception as e:
            print(f"❌ Erro no handler RAG: {e}")
            return None
    
    async def handle_general_conversation(self, query: str, conversation_history: List[Dict]) -> Optional[str]:
        """
        Processa conversas gerais sem necessidade de documentos.
        
        Args:
            query: Pergunta do usuário
            conversation_history: Histórico da conversa
            
        Returns:
            Resposta de conversa geral
        """
        try:
            # Gerar resposta de conversa geral
            response = await generate_general_response(query, conversation_history)
            
            if response:
                print("💬 Resposta de conversa geral gerada")
                return process_response(response)
            
            return None
            
        except Exception as e:
            print(f"❌ Erro no handler de conversa geral: {e}")
            return None
    
    async def handle_human_escalation(self, conversation_id: Optional[str] = None) -> str:
        """
        Processa escalação para atendimento humano.
        
        Args:
            conversation_id: ID da conversa (opcional)
            
        Returns:
            Mensagem de escalação
        """
        try:
            # Marcar conversa para atenção humana se ID fornecido
            if conversation_id and self.supabase:
                try:
                    self.supabase.table('conversations').update({
                        'requires_human_attention': True
                    }).eq('id', conversation_id).execute()
                    print(f"🚨 Conversa {conversation_id} marcada para atenção humana")
                except Exception as e:
                    print(f"❌ Erro ao marcar conversa: {e}")
            
            # Mensagens de escalação variadas
            escalation_messages = [
                "Entendido. Um membro da nossa equipe entrará em contato com você em breve para te ajudar com isso.",
                "Vou encaminhar sua solicitação para nossa equipe especializada. Retornaremos em breve!",
                "Sua demanda é importante! Nossa equipe foi notificada e responderá o mais rápido possível.",
                "Compreendo a situação. Vou garantir que nossa equipe entre em contato para resolver isso juntos."
            ]
            
            response = random.choice(escalation_messages)
            print("🚨 Escalação para humano ativada")
            return process_response(response)
            
        except Exception as e:
            print(f"❌ Erro no handler de escalação: {e}")
            return process_response("Nossa equipe foi notificada e responderá em breve.")
    
    async def handle_emergency(self, query: str, conversation_id: Optional[str] = None) -> str:
        """
        Processa situações de emergência.
        
        Args:
            query: Pergunta do usuário
            conversation_id: ID da conversa (opcional)
            
        Returns:
            Resposta de emergência
        """
        try:
            # Marcar como emergência
            if conversation_id and self.supabase:
                try:
                    self.supabase.table('conversations').update({
                        'requires_human_attention': True,
                        'priority': 'high',
                        'tags': ['emergencia']
                    }).eq('id', conversation_id).execute()
                    print(f"🚨 EMERGÊNCIA marcada para conversa {conversation_id}")
                except Exception as e:
                    print(f"❌ Erro ao marcar emergência: {e}")
            
            emergency_response = """
Entendo que é uma situação urgente. Nossa equipe foi imediatamente notificada.

Para emergências médicas: SAMU 192
Para emergências gerais: Bombeiros 193
Para segurança: Polícia Militar 190

Nossa equipe entrará em contato o mais rápido possível!
"""
            
            print("🚨 EMERGÊNCIA detectada e processada")
            return process_response(emergency_response.strip(), add_hashtag=False)
            
        except Exception as e:
            print(f"❌ Erro no handler de emergência: {e}")
            return await self.handle_human_escalation(conversation_id)
    
    async def _search_documents(self, query: str, threshold: float = 0.7, limit: int = 5) -> str:
        """
        Busca documentos relevantes usando embeddings.
        
        Args:
            query: Consulta para busca
            threshold: Limiar de similaridade
            limit: Número máximo de chunks
            
        Returns:
            Contexto concatenado dos documentos encontrados
        """
        try:
            if not self.supabase:
                return ""
            
            # Importar embeddings aqui para evitar dependência circular
            from langchain_google_genai import GoogleGenerativeAIEmbeddings
            import os
            
            embeddings_model = GoogleGenerativeAIEmbeddings(
                model="models/embedding-001", 
                google_api_key=os.getenv("GEMINI_API_KEY")
            )
            
            # Gerar embedding da consulta
            query_embedding = embeddings_model.embed_query(query)
            
            # Buscar chunks similares
            response = self.supabase.rpc(
                'match_chunks', 
                {
                    'query_embedding': query_embedding, 
                    'match_threshold': threshold, 
                    'match_count': limit
                }
            ).execute()
            
            if not response.data:
                return ""
            
            # Ordenar por similaridade e concatenar
            retrieved_chunks = sorted(
                response.data, 
                key=lambda x: x.get('similarity', 0), 
                reverse=True
            )
            
            context = "\n\n".join([chunk['content'] for chunk in retrieved_chunks])
            print(f"📄 {len(retrieved_chunks)} chunks encontrados para RAG")
            
            return context
            
        except Exception as e:
            print(f"❌ Erro na busca de documentos: {e}")
            return ""
    
    def get_fallback_response(self) -> str:
        """
        Retorna uma resposta de fallback quando nenhum handler funciona.
        
        Returns:
            Resposta de fallback
        """
        fallback_messages = [
            "Não tenho certeza sobre sua pergunta, mas nossa equipe foi notificada e responderá em breve.",
            "Vou verificar essa informação com nossa equipe e retorno para você!",
            "Sua pergunta é importante! Vou encaminhar para que possamos te dar a melhor resposta.",
            "Deixe-me consultar nossa equipe especializada para te responder adequadamente."
        ]
        
        response = random.choice(fallback_messages)
        return process_response(response)


# Função de conveniência para criar handler
def create_intent_handler(supabase_client=None) -> IntentHandlers:
    """
    Cria uma instância do handler de intenções.
    
    Args:
        supabase_client: Cliente do Supabase
        
    Returns:
        Instância do IntentHandlers
    """
    return IntentHandlers(supabase_client)
