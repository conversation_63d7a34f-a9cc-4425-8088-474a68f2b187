import pytest
import pytest_asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from app import app, pwd_context, create_access_token
from datetime import datetime, timedelta
from jose import jwt
import uuid
import tempfile
from pydub import AudioSegment
import os
from unittest.mock import patch, MagicMock, AsyncMock
from app import UserInDB, GoogleGenerativeAIEmbeddings, ChatGoogleGenerativeAI

# Use a fixed user for simplicity in mocking
TEST_USERNAME = f"test_user_{uuid.uuid4().hex}"
TEST_PASSWORD = "test_password"
HASHED_PASSWORD = pwd_context.hash(TEST_PASSWORD)
FULL_USER_DATA = {
    "username": TEST_USERNAME,
    "email": f"{TEST_USERNAME}@example.com",
    "full_name": "Test User",
    "disabled": False,
    "hashed_password": HASHED_PASSWORD,
}

@pytest_asyncio.fixture(scope="module")
def client():
    """Provides a TestClient instance for the app."""
    return TestClient(app)

@pytest.fixture(scope="module")
def auth_token():
    """Creates a valid JWT token for the test user."""
    return create_access_token(data={"sub": TEST_USERNAME})

@pytest.fixture
def mock_supabase():
    """Mocks the Supabase client to prevent real DB calls."""
    with patch('app.supabase', new_callable=MagicMock) as mock_supabase_client:
        # Default behavior: user not found
        mock_supabase_client.table.return_value.select.return_value.eq.return_value.execute.return_value.data = []
        
        # Default behavior for other operations
        mock_supabase_client.table.return_value.insert.return_value.execute.return_value.data = [{'id': 'some-doc-id'}]
        mock_supabase_client.table.return_value.delete.return_value.execute.return_value.data = [{'id': 'some-doc-id'}]
        mock_supabase_client.rpc.return_value.execute.return_value.data = []
        
        yield mock_supabase_client

def mock_user_found(mock_supabase_client):
    """Helper to configure mock_supabase to find the test user."""
    mock_supabase_client.table.return_value.select.return_value.eq.return_value.execute.return_value.data = [FULL_USER_DATA]

def mock_user_not_found(mock_supabase_client):
    """Helper to configure mock_supabase to NOT find a user."""
    mock_supabase_client.table.return_value.select.return_value.eq.return_value.execute.return_value.data = []


def test_register_user(client, mock_supabase):
    new_username = f"new_user_{uuid.uuid4().hex}"
    
    # 1. Test successful registration
    mock_user_not_found(mock_supabase)
    mock_supabase.table.return_value.insert.return_value.execute.return_value.data = [{
        "username": new_username, "email": "<EMAIL>", "full_name": "New", "disabled": False
    }]
    
    response = client.post("/register", json={
        "username": new_username, "email": "<EMAIL>", "full_name": "New", 
        "hashed_password": "pw", "disabled": False
    })
    assert response.status_code == 200
    assert response.json()["username"] == new_username

    # 2. Test registration of existing user
    mock_user_found(mock_supabase) # Simulate user now exists
    response = client.post("/register", json={
        "username": TEST_USERNAME, "email": "<EMAIL>", "full_name": "Test", 
        "hashed_password": "pw", "disabled": False
    })
    assert response.status_code == 400
    assert "Nome de usuário já registrado." in response.json()["detail"]

def test_login_for_access_token(client, mock_supabase):
    # 1. Test successful login
    mock_user_found(mock_supabase)
    response = client.post("/token", data={"username": TEST_USERNAME, "password": TEST_PASSWORD})
    assert response.status_code == 200
    assert "access_token" in response.json()

    # 2. Test wrong password
    response = client.post("/token", data={"username": TEST_USERNAME, "password": "wrongpassword"})
    assert response.status_code == 401

    # 3. Test user not found
    mock_user_not_found(mock_supabase)
    response = client.post("/token", data={"username": "nonexistent", "password": "pw"})
    assert response.status_code == 401

def test_read_users_me(client, mock_supabase, auth_token):
    mock_user_found(mock_supabase)
    response = client.get("/users/me/", headers={"Authorization": f"Bearer {auth_token}"})
    assert response.status_code == 200
    assert response.json()["username"] == TEST_USERNAME

def test_persona_query_requires_auth(client):
    response = client.post("/query-rag/", json={"query": "Bom dia"})
    assert response.status_code == 401 # No token

@patch.object(ChatGoogleGenerativeAI, 'ainvoke')
@patch.object(ChatGoogleGenerativeAI, 'invoke')
@patch.object(GoogleGenerativeAIEmbeddings, 'embed_query')
def test_persona_saudacao_geral(mock_embed_query, mock_invoke_llm, mock_ainvoke_router, client, auth_token, mock_supabase):
    mock_user_found(mock_supabase) # For token validation

    # Mock the intent router to return 'answer_with_template' for greeting
    mock_ainvoke_router.return_value = MagicMock(content="answer_with_template")

    # Mock the embedding and LLM responses
    mock_embed_query.return_value = [0.1] * 1536
    mock_supabase.rpc.return_value.execute.return_value.data = []  # No documents found
    mock_invoke_llm.return_value = MagicMock(content="Oii, bom diaa! Qual seu nome e qual bairro você mora? ✨")

    response = client.post("/query-rag/", json={"query": "Bom dia"}, headers={"Authorization": f"Bearer {auth_token}"})
    assert response.status_code == 200
    assert "Oii, bom diaa! Qual seu nome e qual bairro você mora?" in response.json()["response"]

@patch.object(ChatGoogleGenerativeAI, 'ainvoke')
@patch.object(ChatGoogleGenerativeAI, 'invoke')
@patch.object(GoogleGenerativeAIEmbeddings, 'embed_query')
def test_rag_success_with_document(mock_embed_query, mock_invoke_llm, mock_ainvoke, client, mock_supabase, auth_token):
    mock_user_found(mock_supabase)
    query = "Qual o projeto de lei sobre educação inclusiva?"
    context_from_db = "O projeto de lei 123/2024 propõe a inclusão de mais profissionais de apoio nas escolas."

    # Mock ainvoke to return different responses for different calls
    # First call is for intent routing, second call is for RAG response
    mock_ainvoke.side_effect = [
        MagicMock(content="search_documents_rag"),  # Intent router response
        MagicMock(content=f"Resposta: {context_from_db}")  # RAG response
    ]

    mock_embed_query.return_value = [0.1] * 1536
    mock_supabase.rpc.return_value.execute.return_value.data = [{"content": context_from_db, "similarity": 0.9}]
    mock_invoke_llm.return_value = MagicMock(content=f"Resposta: {context_from_db}")

    response = client.post("/query-rag/", json={"query": query}, headers={"Authorization": f"Bearer {auth_token}"})

    assert response.status_code == 200
    assert context_from_db in response.json()["response"]
    assert mock_ainvoke.call_count == 2  # One for intent routing, one for RAG

@patch.object(GoogleGenerativeAIEmbeddings, 'embed_documents')
def test_ingest_document(mock_embed_docs, client, auth_token, mock_supabase):
    mock_user_found(mock_supabase)
    mock_embed_docs.return_value = [[0.1] * 1536]
    
    files = {"file": ("test.txt", b"Este e um documento de teste.", "text/plain")}
    response = client.post("/ingest-document/", files=files, headers={"Authorization": f"Bearer {auth_token}"})
    
    assert response.status_code == 200
    assert "Documento ingerido com sucesso!" in response.json()["message"]
    mock_supabase.table.return_value.insert.assert_called()

def test_delete_document_success(client, auth_token, mock_supabase):
    mock_user_found(mock_supabase)
    doc_id = uuid.uuid4()
    
    # Endpoint makes two delete calls: one for chunks, one for the document.
    # We mock the execute method to handle both calls.
    execute_mock = MagicMock()
    execute_mock.execute.side_effect = [
        MagicMock(data=[{'id': 'chunk-id'}]),      # Mock result for chunk deletion
        MagicMock(data=[{'id': str(doc_id)}]) # Mock result for document deletion
    ]
    mock_supabase.table.return_value.delete.return_value.eq.return_value = execute_mock
    
    response = client.delete(f"/documents/{doc_id}", headers={"Authorization": f"Bearer {auth_token}"})
    
    assert response.status_code == 200
    assert "excluídos com sucesso" in response.json()["message"]

def test_delete_document_not_found(client, auth_token, mock_supabase):
    mock_user_found(mock_supabase)
    doc_id = uuid.uuid4()

    # Mock the two execute calls. The second one returns empty data.
    execute_mock = MagicMock()
    execute_mock.execute.side_effect = [
        MagicMock(data=[{'id': 'chunk-id'}]), # Mock result for chunk deletion
        MagicMock(data=[])                   # Mock result for document deletion (not found)
    ]
    mock_supabase.table.return_value.delete.return_value.eq.return_value = execute_mock

    response = client.delete(f"/documents/{doc_id}", headers={"Authorization": f"Bearer {auth_token}"})

    assert response.status_code == 404
    assert "Documento não encontrado" in response.json()["detail"]

@patch('speech_recognition.AudioFile')
@patch('app.AudioSegment.from_file')
@patch('speech_recognition.Recognizer.record')
@patch('speech_recognition.Recognizer.recognize_google')
def test_transcribe_audio_success(mock_recognize, mock_record, mock_from_file, mock_audio_file, client, auth_token, mock_supabase):
    mock_user_found(mock_supabase)
    expected_text = "teste de transcrição"
    mock_recognize.return_value = expected_text
    
    files = {"audio_file": ("audio.webm", b"dummy", "audio/webm")}
    response = client.post("/transcribe-audio/", files=files, headers={"Authorization": f"Bearer {auth_token}"})
    
    assert response.status_code == 200
    assert response.json()["transcribed_text"] == expected_text

@patch('httpx.AsyncClient.get', new_callable=AsyncMock)
def test_get_whatsapp_qr_code(mock_get, client, auth_token, mock_supabase):
    mock_user_found(mock_supabase)
    
    # Test success
    mock_get.return_value = MagicMock(status_code=200, json=lambda: {"qr": "code"})
    response = client.get("/whatsapp/qr-code", headers={"Authorization": f"Bearer {auth_token}"})
    assert response.status_code == 200
    assert "qr" in response.json()

    # Test connection error
    mock_get.side_effect = Exception("Connection failed")
    response = client.get("/whatsapp/qr-code", headers={"Authorization": f"Bearer {auth_token}"})
    assert response.status_code == 500

@patch('httpx.AsyncClient.get', new_callable=AsyncMock)
def test_get_whatsapp_conversations(mock_get, client, auth_token, mock_supabase):
    mock_user_found(mock_supabase)
    
    # Test success
    mock_get.return_value = MagicMock(status_code=200, json=lambda: [{"id": "conv1"}])
    response = client.get("/whatsapp/conversations", headers={"Authorization": f"Bearer {auth_token}"})
    assert response.status_code == 200
    assert len(response.json()) == 1

    # Test connection error
    mock_get.side_effect = Exception("Connection failed")
    response = client.get("/whatsapp/conversations", headers={"Authorization": f"Bearer {auth_token}"})
    assert response.status_code == 500