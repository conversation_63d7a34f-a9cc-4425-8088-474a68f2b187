import sys
import os
import time
import tempfile
import speech_recognition as sr
from pydub import AudioSegment
from dotenv import load_dotenv
import random
import io
from datetime import datetime, timedelta, timezone
import asyncio

# Carregar variáveis de ambiente do arquivo .env
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '.env'))

from supabase import create_client, Client
from langchain_google_genai import GoogleGenerativeAIEmbeddings, ChatGoogleGenerativeAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from fastapi import FastAPI, UploadFile, File, HTTPException, Depends, status, WebSocket, WebSocketDisconnect
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from pydantic import BaseModel
import pandas as pd
import httpx
from passlib.context import CryptContext
from jose import JWTError, jwt
from pypdf import PdfReader
from fastapi.middleware.cors import CORSMiddleware

# --- Importações dos Novos Módulos ---
from config.persona import RESPONSE_TEMPLATES, EMOJIS, HASHTAGS, PERSONA_CONFIG
from pipelines.chat_pipeline import route_intent
from pipelines.post_processing import process_response
from handlers.intent_handlers import create_intent_handler
from websocket_manager import websocket_manager, notify_new_message, notify_ai_response

# --- Modelos de IA e Configurações ---

# Inicializar o modelo de embeddings (mantido para compatibilidade)
embeddings_model = GoogleGenerativeAIEmbeddings(model="models/embedding-001", google_api_key=os.getenv("GEMINI_API_KEY"))

# Inicializar o text splitter
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=1000,
    chunk_overlap=200,
    length_function=len,
    is_separator_regex=False,
)

# --- Configurações e Constantes ---
# (Movidas para config/persona.py - mantidas aqui apenas para compatibilidade)
WHATSAPP_BACKEND_URL = os.getenv("WHATSAPP_BACKEND_URL")
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = os.getenv("ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Validações de variáveis de ambiente
if not all([SUPABASE_URL, SUPABASE_KEY, GEMINI_API_KEY, WHATSAPP_BACKEND_URL, SECRET_KEY]):
    raise ValueError("Uma ou mais variáveis de ambiente essenciais não foram definidas.")

# --- Inicialização de Clientes e App ---

supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost",
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "ws://localhost:8000",
        "wss://localhost:8000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Modelos Pydantic ---

class User(BaseModel):
    username: str
    email: str | None = None
    full_name: str | None = None
    disabled: bool | None = None

class UserInDB(User):
    hashed_password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: str | None = None

class QueryRequest(BaseModel):
    query: str
    contact_id: str | None = None
    contact_name: str | None = None

class ReplyRequest(BaseModel):
    message: str

# --- Funções de Utilitário ---
# (Função add_persona_elements movida para pipelines/post_processing.py)

def create_access_token(data: dict, expires_delta: timedelta | None = None):
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=15))
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

# --- Lógica do Roteador de Intenções ---

# Funções movidas para pipelines/chat_pipeline.py

# --- Lógica dos Handlers de Intenção ---
# Funções movidas para handlers/intent_handlers.py

# --- Autenticação e Usuários ---

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

async def get_user(username: str):
    response = supabase.table('users').select('*').eq('username', username).execute()
    return UserInDB(**response.data[0]) if response.data else None

async def authenticate_user(username: str, password: str):
    user = await get_user(username)
    if not user or not pwd_context.verify(password, user.hashed_password):
        return None
    return user

async def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Credenciais inválidas", headers={"WWW-Authenticate": "Bearer"})
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        user = await get_user(username)
        if user is None:
            raise credentials_exception
        return user
    except JWTError:
        raise credentials_exception

async def get_current_active_user(current_user: User = Depends(get_current_user)):
    if current_user.disabled:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Usuário inativo")
    return current_user

async def get_current_user_or_whatsapp_service(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Não foi possível validar as credenciais",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        
        # Se o token for do serviço do WhatsApp, permite o acesso
        if username == "internal_service":
            return User(username="internal_service", email=None, full_name="Internal Service", disabled=False)

        user = await get_user(username)
        if user is None:
            raise credentials_exception
        return user
    except JWTError:
        raise credentials_exception

# --- Endpoints ---

@app.post("/register")
async def register_user(user: UserInDB):
    """
    Endpoint para registrar um novo usuário.
    """
    try:
        # Verificar se o usuário já existe
        existing_user = await get_user(user.username)
        if existing_user:
            raise HTTPException(status_code=400, detail="Nome de usuário já registrado.")

        # Inserir novo usuário no banco
        user_data = {
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "disabled": user.disabled,
            "hashed_password": user.hashed_password
        }

        response = supabase.table('users').insert(user_data).execute()
        if not response.data:
            raise HTTPException(status_code=500, detail="Erro ao criar usuário")

        return response.data[0]
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro interno: {e}")

@app.post("/create-admin")
async def create_admin_user():
    """
    Endpoint para criar usuário admin (apenas para desenvolvimento).
    """
    try:
        # Verificar se já existe
        existing_user = await get_user("admin")
        if existing_user:
            # Atualizar senha do usuário existente
            hashed_password = pwd_context.hash("admin123")
            response = supabase.table('users').update({
                "hashed_password": hashed_password
            }).eq('username', 'admin').execute()

            if response.data:
                return {"message": "Senha do admin atualizada com sucesso", "username": "admin", "password": "admin123"}
            else:
                raise HTTPException(status_code=500, detail="Erro ao atualizar senha")

        # Criar novo usuário admin
        hashed_password = pwd_context.hash("admin123")
        user_data = {
            "username": "admin",
            "email": "<EMAIL>",
            "full_name": "Administrador",
            "disabled": False,
            "hashed_password": hashed_password
        }

        response = supabase.table('users').insert(user_data).execute()
        if not response.data:
            raise HTTPException(status_code=500, detail="Erro ao criar usuário admin")

        return {"message": "Usuário admin criado com sucesso", "username": "admin", "password": "admin123"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro interno: {e}")

@app.get("/users/me/", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """
    Endpoint para obter dados do usuário atual.
    """
    return current_user

@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    user = await authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Usuário ou senha incorretos")
    access_token = create_access_token(data={"sub": user.username}, expires_delta=timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    return {"access_token": access_token, "token_type": "bearer"}

@app.post("/query-rag/")
async def query_rag_endpoint(request: QueryRequest, current_user: User = Depends(get_current_user)):
    """
    Endpoint para consultas RAG (alias para chat_endpoint).
    """
    return await chat_endpoint_logic(request, current_user)

@app.post("/chat/")
async def chat_endpoint(request: QueryRequest, current_user: User = Depends(get_current_user)):
    """
    Endpoint principal para chat.
    """
    return await chat_endpoint_logic(request, current_user)

async def chat_endpoint_logic(request: QueryRequest, current_user: User):
    """
    Lógica compartilhada para endpoints de chat e query-rag.
    """
    query = request.query
    contact_id = request.contact_id
    contact_name = request.contact_name
    conversation_id = None
    response_text = ""

    # 1. Salvar conversa e mensagem do usuário
    conversation_id = None
    if contact_id:
        try:
            # Criar ou atualizar conversa
            convo_data = {
                'contact_id': contact_id,
                'contact_name': contact_name,
                'last_message_at': datetime.utcnow().isoformat()
            }

            # Verificar se conversa já existe
            existing_convo = supabase.table('conversations').select('id').eq('contact_id', contact_id).execute()

            if existing_convo.data:
                # Atualizar conversa existente
                conversation_id = existing_convo.data[0]['id']
                supabase.table('conversations').update(convo_data).eq('id', conversation_id).execute()
            else:
                # Criar nova conversa
                convo_res = supabase.table('conversations').insert(convo_data).execute()
                conversation_id = convo_res.data[0]['id']

            # Salvar mensagem do usuário
            user_message = {
                'conversation_id': conversation_id,
                'sender': 'user',
                'content': query,
                'created_at': datetime.utcnow().isoformat()
            }
            supabase.table('messages').insert(user_message).execute()
            print(f"✅ Conversa e mensagem salvas: {contact_name} ({contact_id})")

            # 🔔 NOTIFICAR NOVA MENSAGEM
            try:
                await notify_new_message(contact_name or "Usuário", query, conversation_id)
            except Exception as e:
                print(f"Erro ao enviar notificação de nova mensagem: {e}")

        except Exception as e:
            print(f"❌ Erro ao salvar mensagem do usuário: {e}")
            # Continuar mesmo se não conseguir salvar

    # 2. Obter histórico da conversa
    conversation_history = ""
    if conversation_id:
        try:
            history_res = supabase.table('messages').select('sender, content').eq('conversation_id', conversation_id).order('created_at', desc=True).limit(6).execute()
            history_messages = reversed(history_res.data)
            conversation_history = "\n".join([f"{msg['sender']}: {msg['content']}" for msg in history_messages])
        except Exception as e:
            print(f"Erro ao buscar histórico: {e}")

    # 3. Roteador de Intenção
    intent = await route_intent(query, conversation_history)
    print(f"Intenção Roteada: {intent} para a query: '{query}'")

    # 4. Executar a ação com base na intenção
    if intent == 'answer_with_template':
        response_text = handle_template_query(query)
    
    if not response_text and intent == 'search_documents_rag':
        response_text = await handle_rag_query(query, conversation_history)

    if not response_text and intent == 'general_conversation':
        response_text = await handle_general_conversation(query, conversation_history)

    if intent == 'escalate_to_human':
        response_text = await handle_human_escalation(conversation_id)

    # 5. Fallback e verificação final
    if not response_text or "não consigo responder" in response_text.lower():
        # Se RAG ou chat geral falharem, use uma resposta padrão e escale
        response_text = await handle_human_escalation(conversation_id)
        response_text = add_persona_elements("Não tenho certeza sobre a sua pergunta. Nossa equipe foi notificada e responderá em breve.")

    # 6. Salvar resposta da IA
    if conversation_id:
        try:
            supabase.table('messages').insert({'conversation_id': conversation_id, 'sender': 'ai', 'content': response_text}).execute()

            # 🔔 NOTIFICAR RESPOSTA DA IA
            try:
                await notify_ai_response(contact_name or "Usuário", response_text, conversation_id)
            except Exception as e:
                print(f"Erro ao enviar notificação de resposta da IA: {e}")

        except Exception as e:
            print(f"Erro ao salvar resposta da IA: {e}")

    # 7. Salvar resposta da IA
    if conversation_id:
        try:
            ai_message = {
                'conversation_id': conversation_id,
                'sender': 'ai',
                'content': response_text,
                'created_at': datetime.utcnow().isoformat()
            }
            supabase.table('messages').insert(ai_message).execute()
            print(f"✅ Resposta da IA salva")
        except Exception as e:
            print(f"❌ Erro ao salvar resposta da IA: {e}")

    # 8. Enviar notificação WebSocket
    try:
        await notify_new_message(contact_name, response_text)
    except Exception as e:
        print(f"Erro ao enviar notificação WebSocket: {e}")

    return {"response": response_text}

# Manter os outros endpoints como estão (ingest-document, etc.)
# ... (O restante do código de /ingest-document/ até o final permanece o mesmo)
@app.post("/ingest-document/")
async def ingest_document_endpoint(file: UploadFile = File(...), current_user: User = Depends(get_current_active_user)):
    """
    Endpoint para ingestão de documentos via upload.
    Suporta arquivos .txt, .pdf e .csv.
    """
    filename = file.filename
    file_type = file.content_type

    try:
        content = await file.read()
        if not content:
            raise HTTPException(status_code=400, detail="Conteúdo do arquivo vazio.")
        
        # 1. Registrar o documento na tabela 'documents'
        response = supabase.table('documents').insert({
            "filename": filename,
            "file_type": file_type,
            "status": "processing"
        }).execute()

        document_id = response.data[0]['id']
        print(f"Documento '{filename}' registrado com ID: {document_id}")

        all_chunks_with_metadata = []

        # Verifica pelo tipo de conteúdo ou pela extensão do arquivo
        if file_type == "application/pdf" or filename.endswith(".pdf"):
            # Imports para OCR
            import pytesseract
            from pdf2image import convert_from_bytes

            reader = PdfReader(io.BytesIO(content))
            for page_num, page in enumerate(reader.pages):
                page_content = page.extract_text()
                
                # Se a extração de texto falhar, tenta OCR
                if not page_content or page_content.strip() == "":
                    print(f"Página {page_num + 1} não contém texto. Tentando OCR...")
                    try:
                        # Converte a página específica do PDF em imagem
                        images = convert_from_bytes(
                            content,
                            dpi=200,
                            first_page=page_num + 1,
                            last_page=page_num + 1
                        )
                        if images:
                            # Extrai texto da imagem usando Tesseract
                            page_content = pytesseract.image_to_string(images[0], lang='por') # 'por' for Portuguese
                            print(f"OCR da página {page_num + 1} concluído.")
                    except Exception as ocr_error:
                        print(f"Erro durante o OCR na página {page_num + 1}: {ocr_error}")
                        continue # Pula para a próxima página em caso de erro de OCR

                if not page_content or page_content.strip() == "":
                    print(f"Página {page_num + 1} pulada (sem conteúdo de texto ou OCR).")
                    continue # Pula páginas sem texto extraído

                page_chunks = text_splitter.split_text(page_content)
                for chunk_content in page_chunks:
                    all_chunks_with_metadata.append({
                        "content": chunk_content,
                        "page_number": page_num + 1,
                        "document_id": document_id
                    })
        elif file_type in ["text/plain", "text/markdown"] or filename.endswith((".txt", ".md")):
            content_str = content.decode("utf-8")
            chunks = text_splitter.split_text(content_str)
            for chunk_content in chunks:
                all_chunks_with_metadata.append({
                    "content": chunk_content,
                    "page_number": None,
                    "document_id": document_id
                })
        elif file_type == "text/csv" or filename.endswith(".csv"):
            df = pd.read_csv(io.BytesIO(content))
            content_str = df.to_string()
            chunks = text_splitter.split_text(content_str)
            for chunk_content in chunks:
                all_chunks_with_metadata.append({
                    "content": chunk_content,
                    "page_number": None,
                    "document_id": document_id
                })
        else:
            raise HTTPException(status_code=400, detail=f"Tipo de arquivo não suportado: {file_type} (filename: {filename})")

        if not all_chunks_with_metadata:
            raise HTTPException(status_code=400, detail="Não foi possível extrair conteúdo do documento. O arquivo pode estar vazio ou em um formato não textual.")

        print(f"Conteúdo dividido em {len(all_chunks_with_metadata)} chunks.")

        # 3. Gerar Embeddings com Gemini
        # Extract just content for embedding generation
        contents_for_embedding = [item["content"] for item in all_chunks_with_metadata]
        start_time = time.time()
        embeddings = embeddings_model.embed_documents(contents_for_embedding)
        end_time = time.time()
        print(f"Embeddings gerados para {len(embeddings)} chunks em {end_time - start_time:.2f} segundos.")

        # 4. Armazenar chunks e embeddings na tabela 'chunks'
        chunks_to_insert = []
        for i, chunk_data in enumerate(all_chunks_with_metadata):
            chunks_to_insert.append({
                "document_id": chunk_data["document_id"],
                "content": chunk_data["content"],
                "embedding": embeddings[i],
                "chunk_order": i
                # "page_number": chunk_data["page_number"] # Coluna removida para evitar erro
            })
        
        response = supabase.table('chunks').insert(chunks_to_insert).execute()
        print(f"{len(chunks_to_insert)} chunks inseridos para o documento ID: {document_id}")

        # 5. Atualizar status do documento para 'completed'
        supabase.table('documents').update({"status": "completed"}).eq("id", document_id).execute()
        print(f"Status do documento ID {document_id} atualizado para 'completed'.")

        return {"message": "Documento ingerido com sucesso!", "document_id": document_id}

    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"Erro ao ingerir documento: {e}")
        if 'document_id' in locals():
            supabase.table('documents').update({"status": "failed"}).eq("id", document_id).execute()
        raise HTTPException(status_code=500, detail=f"Erro interno do servidor: {e}")

@app.post("/conversations/{conversation_id}/reply")
async def reply_to_conversation(conversation_id: str, request: ReplyRequest, current_user: User = Depends(get_current_active_user)):
    """
    Endpoint para um humano responder a uma conversa e desmarcá-la da atenção necessária.
    """
    try:
        # 1. Buscar o contact_id da conversa
        convo_res = supabase.table('conversations').select('contact_id').eq('id', conversation_id).single().execute()
        if not convo_res.data:
            raise HTTPException(status_code=404, detail="Conversa não encontrada.")
        contact_id = convo_res.data['contact_id']

        # 2. Enviar a mensagem para o backend do WhatsApp
        # O backend do WhatsApp precisa de um endpoint para enviar mensagens diretas
        # Assumindo que o endpoint é /send-message
        async with httpx.AsyncClient() as client:
            whatsapp_payload = {"chatId": contact_id, "message": request.message}
            # Precisamos de um token de serviço para autenticar a chamada interna
            service_token = create_access_token(data={"sub": "python_service"})
            headers = {"Authorization": f"Bearer {service_token}"}
            
            # Este endpoint /send-message precisa ser criado no whatsapp_backend
            response = await client.post(f"{WHATSAPP_BACKEND_URL}/send-message", json=whatsapp_payload, headers=headers)
            response.raise_for_status()

        # 3. Salvar a mensagem do humano no banco de dados
        supabase.table('messages').insert({
            'conversation_id': conversation_id,
            'sender': 'human', # Novo tipo de remetente
            'content': request.message
        }).execute()

        # 4. Desmarcar a conversa
        supabase.table('conversations').update({
            'requires_human_attention': False,
            'last_message_at': datetime.utcnow().isoformat() # Atualiza timestamp
        }).eq('id', conversation_id).execute()

        return {"message": "Resposta enviada com sucesso."}
    except httpx.HTTPStatusError as exc:
        raise HTTPException(status_code=exc.response.status_code, detail=f"Erro do backend do WhatsApp: {exc.response.text}")
    except Exception as e:
        print(f"Erro ao responder conversa: {e}")
        raise HTTPException(status_code=500, detail=f"Erro ao processar a resposta: {e}")


@app.get("/conversations/")
async def list_conversations_endpoint(
    limit: int = 10, 
    offset: int = 0,
    requires_attention: bool = None,
    current_user: User = Depends(get_current_active_user)
):
    """
    Endpoint para listar todas as conversas com filtros.
    """
    try:
        query = supabase.table('conversations').select('*', count='exact')
        
        if requires_attention is not None:
            query = query.eq('requires_human_attention', requires_attention)

        response = query.order('last_message_at', desc=True).range(offset, offset + limit - 1).execute()
        return {"conversations": response.data, "total_count": response.count}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao buscar conversas: {e}")

@app.get("/conversations/{conversation_id}/messages")
async def get_conversation_messages_endpoint(conversation_id: str, current_user: User = Depends(get_current_active_user)):
    """
    Endpoint para obter as mensagens de uma conversa específica.
    Aceita tanto conversation_id (UUID) quanto contact_id (WhatsApp ID).
    """
    try:
        # Verificar se é um UUID (conversation_id) ou contact_id (WhatsApp ID)
        import uuid
        try:
            # Tentar parsear como UUID
            uuid.UUID(conversation_id)
            # É um UUID válido, buscar diretamente
            response = supabase.table('messages').select('*').eq('conversation_id', conversation_id).order('created_at', desc=False).execute()
            return response.data
        except ValueError:
            # Não é um UUID, deve ser um contact_id do WhatsApp
            print(f"🔍 Buscando conversa pelo contact_id: {conversation_id}")
            # Buscar conversation_id pelo contact_id
            conv_response = supabase.table('conversations').select('id, contact_name').eq('contact_id', conversation_id).execute()

            if not conv_response.data:
                # Conversa não encontrada - retornar lista vazia (sem criar dados simulados)
                print(f"❌ Conversa não encontrada para contact_id: {conversation_id}")
                print(f"ℹ️  Para ter mensagens, é necessário que a conversa exista no banco de dados")
                return []

            actual_conversation_id = conv_response.data[0]['id']
            contact_name = conv_response.data[0]['contact_name']
            print(f"✅ Conversa encontrada: {actual_conversation_id} ({contact_name})")
            # Buscar mensagens usando o conversation_id real
            response = supabase.table('messages').select('*').eq('conversation_id', actual_conversation_id).order('created_at', desc=False).execute()
            return response.data

    except Exception as e:
        print(f"❌ Erro ao buscar mensagens para {conversation_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Erro ao buscar mensagens: {e}")

@app.get("/dashboard/")
async def get_dashboard_stats(current_user: User = Depends(get_current_active_user)):
    """
    Endpoint para obter estatísticas do dashboard.
    """
    try:
        # Total de conversas
        total_conversations_response = supabase.table('conversations').select('*', count='exact').execute()
        total_conversations = total_conversations_response.count or 0

        # Conversas pendentes (que requerem atenção humana)
        pending_conversations_response = supabase.table('conversations').select('*', count='exact').eq('requires_human_attention', True).execute()
        pending_conversations = pending_conversations_response.count or 0

        # Mensagens de hoje
        from datetime import datetime, timezone
        today = datetime.now(timezone.utc).strftime('%Y-%m-%d')
        messages_today_response = supabase.table('messages').select('*', count='exact').gte('created_at', f'{today}T00:00:00').execute()
        messages_today = messages_today_response.count or 0

        # Conversas ativas (com mensagens nas últimas 24h)
        yesterday = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        active_conversations_response = supabase.table('conversations').select('*', count='exact').gte('last_message_at', yesterday.isoformat()).execute()
        active_conversations = active_conversations_response.count or 0

        return {
            "total_conversations": total_conversations,
            "pending_conversations": pending_conversations,
            "messages_today": messages_today,
            "active_conversations": active_conversations,
            "whatsapp_status": "CONNECTED"  # Pode ser obtido do backend WhatsApp
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao buscar estatísticas do dashboard: {e}")

@app.post("/sync-conversation")
async def sync_conversation_endpoint(request: dict, current_user: User = Depends(get_current_user_or_whatsapp_service)):
    """
    Endpoint para sincronizar uma conversa do WhatsApp com o banco de dados.
    """
    try:
        contact_id = request.get('contact_id')
        contact_name = request.get('contact_name')
        last_message_at = request.get('last_message_at')
        whatsapp_data = request.get('whatsapp_data', {})

        if not contact_id:
            raise HTTPException(status_code=400, detail="contact_id é obrigatório")

        # Upsert da conversa (apenas campos básicos)
        conversation_data = {
            'contact_id': contact_id,
            'contact_name': contact_name,
            'last_message_at': last_message_at
        }

        response = supabase.table('conversations').upsert(
            conversation_data,
            on_conflict='contact_id'
        ).execute()

        return {"message": "Conversa sincronizada com sucesso", "conversation": response.data[0]}
    except Exception as e:
        print(f"Erro ao sincronizar conversa: {e}")
        raise HTTPException(status_code=500, detail=f"Erro ao sincronizar conversa: {e}")

@app.post("/whatsapp/message-received")
async def whatsapp_message_received(request: dict):
    """
    Endpoint para receber mensagens do WhatsApp em tempo real.
    """
    try:
        from_number = request.get('from', '').replace('@c.us', '')
        message_body = request.get('body', '')
        timestamp = request.get('timestamp')
        chat_id = request.get('chatId', request.get('from', ''))

        if not from_number or not message_body:
            return {"message": "Mensagem ignorada - dados insuficientes"}

        print(f"📨 Mensagem recebida do WhatsApp: {from_number} -> {message_body[:50]}...")

        # Buscar ou criar conversa
        contact_id = from_number + '@c.us'
        existing_conv = supabase.table('conversations').select('id, contact_name').eq('contact_id', contact_id).execute()

        conversation_id = None
        contact_name = from_number  # Fallback para o número

        if existing_conv.data:
            conversation_id = existing_conv.data[0]['id']
            contact_name = existing_conv.data[0]['contact_name'] or from_number
        else:
            # Criar nova conversa
            conv_data = {
                'contact_id': contact_id,
                'contact_name': contact_name,
                'last_message_at': datetime.utcnow().isoformat()
            }
            conv_result = supabase.table('conversations').insert(conv_data).execute()
            conversation_id = conv_result.data[0]['id']

        # Salvar mensagem do usuário
        user_message = {
            'conversation_id': conversation_id,
            'sender': 'user',
            'content': message_body,
            'created_at': datetime.utcnow().isoformat()
        }
        supabase.table('messages').insert(user_message).execute()

        # Processar mensagem com IA (usando a mesma lógica do chat)
        try:
            # Detectar intenção usando o roteador existente
            intent = await route_intent(message_body, "")
            print(f"Intenção detectada: {intent}")

            # Gerar resposta usando a mesma lógica do chat
            response_text = None

            if intent == 'answer_with_template':
                response_text = handle_template_query(message_body)

            if not response_text and intent == 'search_documents_rag':
                response_text = await handle_rag_query(message_body, "")

            if not response_text and intent == 'general_conversation':
                response_text = await handle_general_conversation(message_body, "")

            if intent == 'escalate_to_human':
                response_text = await handle_human_escalation(conversation_id)

            # Fallback se nenhuma resposta foi gerada
            if not response_text:
                response_text = await handle_general_conversation(message_body, "")

            # Salvar resposta da IA
            ai_message = {
                'conversation_id': conversation_id,
                'sender': 'ai',
                'content': response_text,
                'created_at': datetime.utcnow().isoformat()
            }
            supabase.table('messages').insert(ai_message).execute()

            # Enviar resposta via WhatsApp
            try:
                async with httpx.AsyncClient() as client:
                    whatsapp_response = await client.post(
                        f"{WHATSAPP_BACKEND_URL}/send-message",
                        json={
                            "chatId": chat_id,
                            "message": response_text
                        },
                        headers={"Authorization": f"Bearer {create_access_token(data={'sub': 'internal_service'})}"},
                        timeout=10.0
                    )

                if whatsapp_response.status_code == 200:
                    print(f"✅ Resposta enviada via WhatsApp para {contact_name}")
                else:
                    print(f"❌ Erro ao enviar resposta via WhatsApp: {whatsapp_response.status_code}")

            except Exception as e:
                print(f"❌ Erro ao enviar resposta via WhatsApp: {e}")

        except Exception as e:
            print(f"❌ Erro ao processar mensagem com IA: {e}")

        return {"message": "Mensagem processada com sucesso"}

    except Exception as e:
        print(f"❌ Erro ao processar mensagem recebida: {e}")
        return {"message": f"Erro ao processar mensagem: {e}"}

@app.get("/dashboard/stats")
async def get_dashboard_stats(current_user: User = Depends(get_current_active_user)):
    """
    Endpoint para obter estatísticas para o dashboard.
    """
    try:
        from collections import Counter
        from itertools import groupby

        # --- Métricas Básicas ---
        convo_count_res = supabase.table('conversations').select('id', count='exact').execute()
        total_conversations = convo_count_res.count
        msg_count_res = supabase.table('messages').select('id', count='exact').execute()
        total_messages = msg_count_res.count
        doc_count_res = supabase.table('documents').select('id', count='exact').execute()
        total_documents = doc_count_res.count

        # --- Conversas Diárias (Últimos 7 dias) ---
        daily_conversations = []
        try:
            seven_days_ago = datetime.utcnow() - timedelta(days=7)
            daily_convos_res = supabase.table('conversations').select('created_at').gte('created_at', seven_days_ago.isoformat()).execute()
            if daily_convos_res.data:
                dates = [datetime.fromisoformat(c['created_at'].split('+')[0]).strftime('%Y-%m-%d') for c in daily_convos_res.data]
                date_counts = Counter(dates)
                all_days = [(datetime.utcnow() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(7)]
                daily_conversations = [{"day": day, "count": date_counts.get(day, 0)} for day in sorted(all_days)]
            else: # Garante que sempre retorne os 7 dias, mesmo que vazios
                all_days = [(datetime.utcnow() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(7)]
                daily_conversations = [{"day": day, "count": 0} for day in sorted(all_days)]
        except Exception as e:
            print(f"Erro ao calcular conversas diárias: {e}")

        # --- Tópicos Mais Frequentes e Tempo Médio de Resposta ---
        top_topics = []
        avg_response_time = 0
        try:
            all_messages_res = supabase.table('messages').select('conversation_id, sender, content, created_at').order('created_at', desc=False).execute()
            all_messages = all_messages_res.data

            # Cálculo dos Tópicos
            topic_counter = Counter()
            # Mapeia o nome do template para um nome mais amigável
            template_friendly_names = {
                "saudacao_geral": "Saudação", "assistencia_social": "Assistência Social",
                "medicamentos": "Medicamentos", "pedido_emprego": "Emprego",
                "cirurgias_exames": "Cirurgias/Exames", "tratamentos_dentarios": "Trat. Dentário",
                "mensagem_fe": "Mensagem de Fé", "encontro_presencial": "Reunião"
            }

            user_messages = [msg['content'].lower() for msg in all_messages if msg['sender'] == 'user']
            for message in user_messages:
                for template_name, data in RESPONSE_TEMPLATES.items():
                    if any(keyword in message for keyword in data['keywords']):
                        topic_counter[template_friendly_names.get(template_name, template_name)] += 1
                        break # Conta apenas a primeira correspondência por mensagem
            
            # Formata para o gráfico de pizza
            top_topics = [{"name": name, "value": value} for name, value in topic_counter.most_common(5)]

            # Cálculo do Tempo de Resposta
            response_times = []
            # Agrupa mensagens por conversation_id
            for _, messages_in_convo in groupby(all_messages, key=lambda x: x['conversation_id']):
                msg_list = list(messages_in_convo)
                for i in range(len(msg_list) - 1):
                    # Se uma mensagem de 'user' é seguida por uma de 'ai'
                    if msg_list[i]['sender'] == 'user' and msg_list[i+1]['sender'] == 'ai':
                        user_time = datetime.fromisoformat(msg_list[i]['created_at'].split('+')[0])
                        ai_time = datetime.fromisoformat(msg_list[i+1]['created_at'].split('+')[0])
                        time_diff = (ai_time - user_time).total_seconds()
                        response_times.append(time_diff)
            
            if response_times:
                avg_response_time = sum(response_times) / len(response_times)

        except Exception as e:
            print(f"Erro ao calcular métricas avançadas: {e}")

        # Calcular conversas diárias (últimos 7 dias)
        daily_conversations = []
        try:
            seven_days_ago = datetime.utcnow() - timedelta(days=7)
            daily_convos_res = supabase.table('conversations').select('created_at').gte('created_at', seven_days_ago.isoformat()).execute()

            if daily_convos_res.data:
                from collections import Counter
                dates = []
                for c in daily_convos_res.data:
                    try:
                        # Limpar formato de data para parsing
                        date_str = c['created_at'].split('+')[0].split('.')[0]  # Remove timezone e microsegundos
                        date_obj = datetime.fromisoformat(date_str)
                        dates.append(date_obj.strftime('%Y-%m-%d'))
                    except Exception as e:
                        print(f"Erro ao processar data {c['created_at']}: {e}")
                        continue
                date_counts = Counter(dates)
                all_days = [(datetime.utcnow() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(7)]
                daily_conversations = [{"day": day, "count": date_counts.get(day, 0)} for day in sorted(all_days)]
            else:
                # Se não há dados, retornar 7 dias com zero
                all_days = [(datetime.utcnow() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(7)]
                daily_conversations = [{"day": day, "count": 0} for day in sorted(all_days)]
        except Exception as e:
            print(f"Erro ao calcular conversas diárias: {e}")
            # Fallback para dados vazios
            all_days = [(datetime.utcnow() - timedelta(days=i)).strftime('%Y-%m-%d') for i in range(7)]
            daily_conversations = [{"day": day, "count": 0} for day in sorted(all_days)]

        return {
            "total_conversations": total_conversations,
            "total_messages": total_messages,
            "total_documents": total_documents,
            "daily_conversations": daily_conversations,
            "avg_response_time": round(avg_response_time) if avg_response_time else 0
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao buscar estatísticas: {e}")


@app.get("/documents/")
async def list_documents_endpoint(
    limit: int = 10,
    offset: int = 0,
    file_type: str = None,
    filename_contains: str = None,
    current_user: User = Depends(get_current_active_user)
):

    """
    Endpoint para listar todos os documentos ingeridos com paginação e filtragem.
    """
    try:
        query = supabase.table('documents').select('id, filename, file_type, status, created_at', count='exact')

        if file_type:
            query = query.eq('file_type', file_type)
        if filename_contains:
            query = query.ilike('filename', f'%{filename_contains}%')

        # Get total count before applying limit and offset
        count_response = query.execute()
        total_count = count_response.count

        response = query.order('created_at', desc=True).range(offset, offset + limit - 1).execute()
        
        return {"documents": response.data, "total_count": total_count}
    except Exception as e:
        print(f"Erro ao listar documentos: {e}")
        raise HTTPException(status_code=500, detail=f"Erro interno do servidor ao listar documentos: {e}")

@app.get("/documents/{document_id}/content")
async def get_document_content(document_id: str, current_user: User = Depends(get_current_active_user)):
    """
    Endpoint para obter o conteúdo de um documento.
    """
    try:
        # Buscar documento no banco
        doc_response = supabase.table('documents').select('*').eq('id', document_id).execute()
        if not doc_response.data:
            raise HTTPException(status_code=404, detail="Documento não encontrado")

        document = doc_response.data[0]

        # Implementar visualização real baseada no tipo de arquivo
        try:
            # Buscar chunks do documento para reconstruir o conteúdo
            chunks_response = supabase.table('chunks').select('content').eq('document_id', document_id).order('chunk_order', desc=False).execute()

            filename = document['filename']
            file_type = document['file_type']
            file_size = document.get('file_size', 'N/A')
            created_at = document.get('created_at', 'N/A')

            if chunks_response.data:
                # Reconstruir conteúdo completo dos chunks
                full_content = "".join([chunk['content'] for chunk in chunks_response.data])

                # Processar conteúdo baseado no tipo de arquivo
                if file_type.lower() == 'pdf':
                    content = f"""📄 DOCUMENTO PDF: {filename}
📊 Informações:
   • Tipo: {file_type.upper()}
   • Tamanho: {file_size} bytes
   • Data: {created_at}
   • Chunks processados: {len(chunks_response.data)}

📝 CONTEÚDO EXTRAÍDO:
{'-' * 50}
{full_content[:2000]}{'...' if len(full_content) > 2000 else ''}
{'-' * 50}

✅ Conteúdo extraído e processado com sucesso!"""

                elif file_type.lower() == 'txt':
                    content = f"""📝 ARQUIVO DE TEXTO: {filename}
📊 Informações:
   • Tipo: {file_type.upper()}
   • Tamanho: {file_size} bytes
   • Data: {created_at}
   • Linhas: {len(full_content.split(chr(10)))}

📄 CONTEÚDO COMPLETO:
{'-' * 50}
{full_content}
{'-' * 50}

✅ Arquivo de texto carregado com sucesso!"""

                elif file_type.lower() == 'csv':
                    lines = full_content.split('\n')
                    content = f"""📊 PLANILHA CSV: {filename}
📊 Informações:
   • Tipo: {file_type.upper()}
   • Tamanho: {file_size} bytes
   • Data: {created_at}
   • Linhas: {len(lines)}
   • Colunas estimadas: {len(lines[0].split(',')) if lines else 0}

📋 PRIMEIRAS LINHAS:
{'-' * 50}
{chr(10).join(lines[:20])}
{f'{chr(10)}... ({len(lines) - 20} linhas restantes)' if len(lines) > 20 else ''}
{'-' * 50}

✅ Planilha CSV processada com sucesso!"""

                else:
                    content = f"""📄 DOCUMENTO: {filename}
📊 Informações:
   • Tipo: {file_type.upper()}
   • Tamanho: {file_size} bytes
   • Data: {created_at}
   • Chunks processados: {len(chunks_response.data)}

📝 CONTEÚDO PROCESSADO:
{'-' * 50}
{full_content[:1500]}{'...' if len(full_content) > 1500 else ''}
{'-' * 50}

✅ Documento processado e indexado com sucesso!"""
            else:
                content = f"""📄 DOCUMENTO: {filename}
📊 Informações:
   • Tipo: {file_type.upper()}
   • Tamanho: {file_size} bytes
   • Data: {created_at}

⚠️ Conteúdo não disponível
Este documento foi enviado mas ainda não foi processado em chunks.
Tente novamente em alguns instantes."""

        except Exception as e:
            content = f"""📄 DOCUMENTO: {filename}
📊 Informações:
   • Tipo: {file_type.upper()}
   • Tamanho: {file_size} bytes

❌ Erro ao processar conteúdo
Detalhes: {str(e)}

💡 O documento está disponível para download."""

        return {"content": content}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao buscar conteúdo: {e}")

@app.get("/documents/{document_id}/download")
async def download_document(document_id: str, current_user: User = Depends(get_current_active_user)):
    """
    Endpoint para download de um documento.
    """
    try:
        # Buscar documento no banco
        doc_response = supabase.table('documents').select('*').eq('id', document_id).execute()
        if not doc_response.data:
            raise HTTPException(status_code=404, detail="Documento não encontrado")

        document = doc_response.data[0]

        # Para desenvolvimento, retornar conteúdo simulado
        content = f"Conteúdo simulado do arquivo {document['filename']}"

        from fastapi.responses import Response
        return Response(
            content=content.encode(),
            media_type="application/octet-stream",
            headers={"Content-Disposition": f"attachment; filename={document['filename']}"}
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro ao baixar documento: {e}")

@app.delete("/documents/{document_id}")
async def delete_document_endpoint(document_id: str, current_user: User = Depends(get_current_active_user)):
    """
    Endpoint para excluir um documento e seus chunks associados.
    """
    try:
        # Excluir chunks associados ao documento
        supabase.table('chunks').delete().eq("document_id", document_id).execute()
        print(f"Chunks associados ao documento ID {document_id} excluídos.")

        # Excluir o documento da tabela 'documents'
        response = supabase.table('documents').delete().eq("id", document_id).execute()
        
        if not response.data:
            raise HTTPException(status_code=404, detail="Documento não encontrado.")

        print(f"Documento ID {document_id} excluído com sucesso.")
        return {"message": f"Documento {document_id} e seus chunks associados excluídos com sucesso."}

    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"Erro ao excluir documento: {e}")
        raise HTTPException(status_code=500, detail=f"Erro interno do servidor ao excluir documento: {e}")

@app.get("/documents/{document_id}/content")
async def get_document_content_endpoint(document_id: str, current_user: User = Depends(get_current_active_user)):
    """
    Endpoint para obter o conteúdo completo de um documento pelo seu ID.
    """
    try:
        # Buscar o documento para verificar sua existência e tipo
        doc_response = supabase.table('documents').select('filename, file_type').eq('id', document_id).single().execute()
        if not doc_response.data:
            raise HTTPException(status_code=404, detail="Documento não encontrado.")
        
        filename = doc_response.data['filename']
        file_type = doc_response.data['file_type']

        # Buscar todos os chunks associados a este documento, ordenados por chunk_order
        chunks_response = supabase.table('chunks').select('content').eq('document_id', document_id).order('chunk_order', desc=False).execute()
        
        if not chunks_response.data:
            return {"filename": filename, "file_type": file_type, "content": "Conteúdo não disponível ou documento sem chunks."}

        # Reconstruir o conteúdo completo
        full_content = "".join([chunk['content'] for chunk in chunks_response.data])

        return {"filename": filename, "file_type": file_type, "content": full_content}

    except HTTPException as e:
        raise e
    except Exception as e:
        print(f"Erro ao obter conteúdo do documento: {e}")
        raise HTTPException(status_code=500, detail=f"Erro interno do servidor ao obter conteúdo do documento: {e}")

@app.post("/transcribe-audio/")
async def transcribe_audio_endpoint(audio_file: UploadFile = File(...), current_user: User = Depends(get_current_user)):
    """
    Endpoint para receber e transcrever um arquivo de áudio.
    """
    try:
        # Salvar o arquivo de áudio temporariamente como .webm
        with tempfile.NamedTemporaryFile(delete=False, suffix=".webm") as tmp_webm_file:
            tmp_webm_file.write(await audio_file.read())
            temp_webm_file_path = tmp_webm_file.name
        tmp_webm_file.close() # Explicitly close the file handle

        # Converter .webm para .wav usando pydub
        temp_wav_file_path = temp_webm_file_path.replace(".webm", ".wav")
        AudioSegment.from_file(temp_webm_file_path).export(temp_wav_file_path, format="wav")

        r = sr.Recognizer()
        with sr.AudioFile(temp_wav_file_path) as source:
            audio_data = r.record(source)  # read the entire audio file

        # Transcrever o áudio usando o Google Web Speech API
        try:
            transcribed_text = r.recognize_google(audio_data, language="pt-BR")
            print(f"Áudio transcrito: {transcribed_text}")
            return {"message": "Áudio transcrito com sucesso!", "transcribed_text": transcribed_text}
        except sr.UnknownValueError:
            raise HTTPException(status_code=400, detail="Não foi possível entender o áudio.")
        except sr.RequestError as e:
            import traceback
            print(f"Erro no serviço de transcrição; {e}")
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Erro no serviço de transcrição; {e}")
    except Exception as e:
        import traceback
        print(f"Erro ao processar áudio: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Erro interno do servidor ao processar áudio: {e}")
    finally:
        # Remover os arquivos temporários
        if 'temp_webm_file_path' in locals() and os.path.exists(temp_webm_file_path):
            os.remove(temp_webm_file_path)
        if 'temp_wav_file_path' in locals() and os.path.exists(temp_wav_file_path):
            os.remove(temp_wav_file_path)


@app.get("/whatsapp/qr-code")
async def get_whatsapp_qr_code(current_user: User = Depends(get_current_user_or_whatsapp_service)):
    """
    Endpoint para obter o QR Code do backend do WhatsApp.
    """
    try:
        # Gera um token de serviço para a comunicação interna
        service_token = create_access_token(data={"sub": "internal_service"})
        headers = {"Authorization": f"Bearer {service_token}"}
        
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{WHATSAPP_BACKEND_URL}/qr-code", headers=headers)
            response.raise_for_status()
            return response.json()
    except httpx.RequestError as exc:
        raise HTTPException(status_code=500, detail=f"Erro ao conectar com o backend do WhatsApp: {exc}")
    except httpx.HTTPStatusError as exc:
        raise HTTPException(status_code=exc.response.status_code, detail=f"Erro do backend do WhatsApp: {exc.response.text}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro inesperado ao obter QR Code: {e}")

@app.get("/whatsapp/conversations")
async def get_whatsapp_conversations(current_user: User = Depends(get_current_user_or_whatsapp_service)):
    """
    Endpoint para obter as conversas do backend do WhatsApp.
    """
    try:
        # Gera um token de serviço para a comunicação interna
        service_token = create_access_token(data={"sub": "internal_service"})
        headers = {"Authorization": f"Bearer {service_token}"}

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{WHATSAPP_BACKEND_URL}/conversations", headers=headers)
            response.raise_for_status()
            return response.json()
    except httpx.RequestError as exc:
        raise HTTPException(status_code=500, detail=f"Erro ao conectar com o backend do WhatsApp: {exc}")
    except httpx.HTTPStatusError as exc:
        raise HTTPException(status_code=exc.response.status_code, detail=f"Erro do backend do WhatsApp: {exc.response.text}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro inesperado ao obter conversas: {e}")

@app.get("/whatsapp/conversations/{chat_id}/messages")
async def get_whatsapp_conversation_messages(chat_id: str, current_user: User = Depends(get_current_user_or_whatsapp_service)):
    """
    Endpoint para obter mensagens reais de uma conversa específica do WhatsApp.
    """
    try:
        # Gera um token de serviço para a comunicação interna
        service_token = create_access_token(data={"sub": "internal_service"})
        headers = {"Authorization": f"Bearer {service_token}"}

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{WHATSAPP_BACKEND_URL}/conversations/{chat_id}/messages", headers=headers)
            response.raise_for_status()
            return response.json()
    except httpx.RequestError as exc:
        raise HTTPException(status_code=500, detail=f"Erro ao conectar com o backend do WhatsApp: {exc}")
    except httpx.HTTPStatusError as exc:
        raise HTTPException(status_code=exc.response.status_code, detail=f"Erro do backend do WhatsApp: {exc.response.text}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro inesperado ao obter mensagens: {e}")

@app.post("/whatsapp/conversations/{chat_id}/load-history")
async def load_whatsapp_conversation_history(chat_id: str, current_user: User = Depends(get_current_user_or_whatsapp_service)):
    """
    Endpoint para forçar carregamento de histórico de uma conversa específica do WhatsApp.
    """
    try:
        # Gera um token de serviço para a comunicação interna
        service_token = create_access_token(data={"sub": "internal_service"})
        headers = {"Authorization": f"Bearer {service_token}"}

        async with httpx.AsyncClient(timeout=30.0) as client:  # Timeout maior para carregamento de histórico
            response = await client.post(f"{WHATSAPP_BACKEND_URL}/conversations/{chat_id}/load-history", headers=headers)
            response.raise_for_status()
            return response.json()
    except httpx.RequestError as exc:
        raise HTTPException(status_code=500, detail=f"Erro ao conectar com o backend do WhatsApp: {exc}")
    except httpx.HTTPStatusError as exc:
        raise HTTPException(status_code=exc.response.status_code, detail=f"Erro do backend do WhatsApp: {exc.response.text}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erro inesperado ao carregar histórico: {e}")

async def get_current_user_or_service(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Credenciais inválidas",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
        
        # Permite acesso se for o serviço interno
        if username == "internal_service":
            return User(username="internal_service", email=None, full_name="Internal Service", disabled=False)

        user = await get_user(username)
        if user is None:
            raise credentials_exception
        return user
    except JWTError:
        raise credentials_exception

@app.get("/test-whatsapp")
async def test_whatsapp_connection():
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://whatsapp-backend:3001/status", timeout=30.0)
            return {"status": response.status_code, "text": response.text}
    except Exception as e:
        return {"error": str(e)}

@app.get("/whatsapp/status")
async def get_whatsapp_status():
    """
    Endpoint para obter o status do cliente do WhatsApp com retentativas.
    """
    service_token = create_access_token(data={"sub": "internal_service"})
    headers = {"Authorization": f"Bearer {service_token}"}
    
    # Tenta se comunicar com o backend do WhatsApp por até 15 segundos
    for attempt in range(5):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{WHATSAPP_BACKEND_URL}/status", headers=headers, timeout=5.0)
                response.raise_for_status()
                return response.json()
        except (httpx.ConnectError, httpx.TimeoutException) as e:
            print(f"Tentativa {attempt + 1} falhou: Não foi possível conectar ao whatsapp-backend. Tentando novamente em 3 segundos... Erro: {e}")
            await asyncio.sleep(3)
        except httpx.HTTPStatusError as e:
            print(f"Erro de status HTTP do whatsapp-backend: {e.response.status_code} - {e.response.text}")
            raise HTTPException(status_code=e.response.status_code, detail=f"Erro do serviço WhatsApp: {e.response.text}")
        except Exception as e:
            import traceback
            print("--- ERRO DETALHADO EM /whatsapp/status ---")
            traceback.print_exc()
            print("-----------------------------------------")
            raise HTTPException(status_code=500, detail=f"Erro interno detalhado: {e}")
            
    raise HTTPException(status_code=503, detail="Serviço WhatsApp indisponível após múltiplas tentativas.")

async def create_default_user():
    """
    Cria um usuário padrão se não existir.
    """
    try:
        # Verificar se já existe um usuário admin
        existing_user = await get_user("admin")
        if existing_user:
            print("✅ Usuário admin já existe")
            return

        # Criar usuário admin padrão
        hashed_password = pwd_context.hash("admin123")
        user_data = {
            "username": "admin",
            "email": "<EMAIL>",
            "full_name": "Administrador",
            "disabled": False,
            "hashed_password": hashed_password
        }

        response = supabase.table('users').insert(user_data).execute()
        if response.data:
            print("✅ Usuário admin criado com sucesso!")
            print("   Username: admin")
            print("   Password: admin123")
        else:
            print("❌ Erro ao criar usuário admin")
    except Exception as e:
        print(f"⚠️ Erro ao criar usuário padrão: {e}")
        print("   Continuando sem usuário padrão...")

# Executar criação de usuário padrão na inicialização
import asyncio

async def initialize_app():
    """
    Inicializa a aplicação criando usuário padrão.
    """
    print("🚀 Servidor iniciado com sucesso!")
    print(f"📊 Conectado ao Supabase: {SUPABASE_URL}")

    # Criar usuário padrão
    await create_default_user()

    print("🔗 Endpoints disponíveis:")
    print("   - POST /chat/ - Chat com IA")
    print("   - GET /conversations/ - Listar conversas")
    print("   - POST /register - Registrar usuário")
    print("   - POST /token - Login")
    print("   - GET /whatsapp/status - Status do WhatsApp")
    print("   - GET /whatsapp/qr-code - QR Code do WhatsApp")
    print("   - GET /whatsapp/conversations - Conversas do WhatsApp")

# --- Sistema de WebSocket para Notificações ---

class ConnectionManager:
    """Gerenciador de conexões WebSocket."""

    def __init__(self):
        self.active_connections: list[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        print(f"✅ Nova conexão WebSocket: {len(self.active_connections)} conexões ativas")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            print(f"❌ Conexão WebSocket removida: {len(self.active_connections)} conexões ativas")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception as e:
            print(f"Erro ao enviar mensagem pessoal: {e}")
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        """Envia mensagem para todas as conexões ativas."""
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                print(f"Erro ao enviar broadcast: {e}")
                disconnected.append(connection)

        # Remove conexões desconectadas
        for conn in disconnected:
            self.disconnect(conn)

# Instância global do gerenciador
manager = ConnectionManager()

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Endpoint WebSocket simples para teste."""
    print("🔌 TENTATIVA DE CONEXÃO WEBSOCKET DETECTADA!")
    await websocket.accept()
    print("✅ WEBSOCKET CONECTADO!")

    try:
        while True:
            data = await websocket.receive_text()
            print(f"📨 Recebido: {data}")
            await websocket.send_text(f"Echo: {data}")
            print(f"📤 Enviado: Echo: {data}")
    except WebSocketDisconnect:
        print("❌ WebSocket desconectado")
    except Exception as e:
        print(f"❌ Erro WebSocket: {e}")

@app.websocket("/test")
async def websocket_test(websocket: WebSocket):
    """Endpoint WebSocket de teste alternativo."""
    print("🧪 TESTE WEBSOCKET ALTERNATIVO!")
    await websocket.accept()
    await websocket.send_text("Conexão de teste estabelecida!")

    try:
        while True:
            data = await websocket.receive_text()
            await websocket.send_text(f"Teste Echo: {data}")
    except WebSocketDisconnect:
        print("❌ Teste WebSocket desconectado")

async def notify_new_message(contact_name: str, message: str):
    """Notifica sobre nova mensagem via WebSocket."""
    notification = {
        "type": "new_message",
        "contact_name": contact_name,
        "message": message,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    import json
    await manager.broadcast(json.dumps(notification))

async def notify_status_change(status: str):
    """Notifica sobre mudança de status via WebSocket."""
    notification = {
        "type": "status_change",
        "status": status,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    import json
    await manager.broadcast(json.dumps(notification))


# --- ENDPOINTS WEBSOCKET ---

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, client_id: str = None, user_id: str = None):
    """Endpoint principal do WebSocket"""
    from fastapi import WebSocket, WebSocketDisconnect
    import json

    client_id = await websocket_manager.connect(websocket, client_id, user_id)

    try:
        while True:
            # Receber mensagem do cliente
            data = await websocket.receive_text()
            message_data = json.loads(data)

            # Processar mensagem
            await websocket_manager.handle_client_message(client_id, message_data)

    except WebSocketDisconnect:
        await websocket_manager.disconnect(client_id)
    except Exception as e:
        print(f"Erro no WebSocket: {e}")
        await websocket_manager.disconnect(client_id)


@app.get("/ws/stats")
async def get_websocket_stats():
    """Retorna estatísticas do WebSocket"""
    return websocket_manager.get_stats()


@app.get("/ws/connections")
async def get_active_connections():
    """Retorna conexões ativas"""
    return websocket_manager.get_active_connections()


@app.post("/ws/notify")
async def send_notification(notification_data: dict):
    """Envia notificação via WebSocket"""
    title = notification_data.get("title", "Notificação")
    message = notification_data.get("message", "")
    priority = notification_data.get("priority", "normal")

    await websocket_manager.send_system_alert(title, message, priority)
    return {"status": "sent"}


@app.post("/ws/broadcast")
async def broadcast_message(message_data: dict):
    """Envia mensagem para todos os clientes conectados"""
    sent_count = await websocket_manager.broadcast_message(message_data)
    return {"status": "sent", "clients_reached": sent_count}


# Executar inicialização quando o módulo for carregado
if __name__ == "__main__":
    asyncio.run(initialize_app())

