import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Avatar,
  Chip,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  CircularProgress
} from '@mui/material';
import {
  Close as CloseIcon,
  Send as SendIcon,
  Person as PersonIcon,
  SmartToy as BotIcon,
  Phone as PhoneIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import axiosInstance from '../api/axiosInstance';

const ConversationDetail = ({ open, onClose, conversation }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (open && conversation) {
      fetchMessages();
    }
  }, [open, conversation]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchMessages = async () => {
    if (!conversation?.id) return;
    
    setLoading(true);
    try {
      const response = await axiosInstance.get(`/conversations/${conversation.id}/messages`);
      setMessages(response.data || []);
    } catch (error) {
      console.error('Erro ao buscar mensagens:', error);
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    setSending(true);
    try {
      const response = await axiosInstance.post('/chat/', {
        query: newMessage,
        contact_id: conversation.contact_id,
        contact_name: conversation.contact_name
      });

      // Adicionar mensagem do usuário
      const userMessage = {
        id: Date.now(),
        sender: 'user',
        content: newMessage,
        created_at: new Date().toISOString()
      };

      // Adicionar resposta da IA
      const aiMessage = {
        id: Date.now() + 1,
        sender: 'ai',
        content: response.data.response,
        created_at: new Date().toISOString()
      };

      setMessages(prev => [...prev, userMessage, aiMessage]);
      setNewMessage('');
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  };

  const formatMessageTime = (timestamp) => {
    try {
      const date = new Date(timestamp);
      return format(date, 'HH:mm', { locale: ptBR });
    } catch {
      return '';
    }
  };

  const getMessageAvatar = (sender) => {
    if (sender === 'user') {
      return (
        <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
          <PersonIcon fontSize="small" />
        </Avatar>
      );
    } else {
      return (
        <Avatar sx={{ bgcolor: 'secondary.main', width: 32, height: 32 }}>
          <BotIcon fontSize="small" />
        </Avatar>
      );
    }
  };

  if (!conversation) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh', display: 'flex', flexDirection: 'column' }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              <PersonIcon />
            </Avatar>
            <Box>
              <Typography variant="h6">
                {conversation.contact_name || 'Contato'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PhoneIcon fontSize="small" color="action" />
                <Typography variant="body2" color="text.secondary">
                  {conversation.contact_id}
                </Typography>
              </Box>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {conversation.requires_human_attention && (
              <Chip
                label="Requer Atenção"
                color="warning"
                size="small"
              />
            )}
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', p: 0 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flex: 1 }}>
            <CircularProgress />
          </Box>
        ) : (
          <List sx={{ flex: 1, overflow: 'auto', px: 2 }}>
            {messages.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography color="text.secondary">
                  Nenhuma mensagem encontrada
                </Typography>
              </Box>
            ) : (
              messages.map((message, index) => (
                <ListItem
                  key={message.id || index}
                  sx={{
                    flexDirection: message.sender === 'user' ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    gap: 1,
                    mb: 1
                  }}
                >
                  <ListItemAvatar sx={{ minWidth: 'auto' }}>
                    {getMessageAvatar(message.sender)}
                  </ListItemAvatar>
                  <Paper
                    elevation={1}
                    sx={{
                      p: 2,
                      maxWidth: '70%',
                      bgcolor: message.sender === 'user' ? 'primary.light' : 'grey.100',
                      color: message.sender === 'user' ? 'primary.contrastText' : 'text.primary'
                    }}
                  >
                    <Typography variant="body1" sx={{ mb: 0.5 }}>
                      {message.content}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <TimeIcon fontSize="small" sx={{ opacity: 0.7 }} />
                      <Typography variant="caption" sx={{ opacity: 0.7 }}>
                        {formatMessageTime(message.created_at)}
                      </Typography>
                    </Box>
                  </Paper>
                </ListItem>
              ))
            )}
            <div ref={messagesEndRef} />
          </List>
        )}
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <TextField
          fullWidth
          multiline
          maxRows={3}
          placeholder="Digite sua mensagem..."
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={sending}
          variant="outlined"
          size="small"
        />
        <Button
          variant="contained"
          onClick={sendMessage}
          disabled={!newMessage.trim() || sending}
          sx={{ minWidth: 'auto', px: 2 }}
        >
          {sending ? <CircularProgress size={20} /> : <SendIcon />}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConversationDetail;
