import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
  Divider,
  Avatar,
  Chip,
  CircularProgress
} from '@mui/material';
import {
  Close as CloseIcon,
  Send as SendIcon,
  Phone as PhoneIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import MessageList from './whatsapp/MessageList';
import axiosInstance from '../api/axiosInstance';

const ConversationDetail = ({ open, onClose, conversation }) => {
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);

  const sendMessage = async () => {
    if (!newMessage.trim() || sending) return;

    setSending(true);
    try {
      await axiosInstance.post('/chat/', {
        query: newMessage,
        contact_id: conversation.contact_id,
        contact_name: conversation.contact_name
      });

      setNewMessage('');
      // O MessageList irá atualizar automaticamente via refetch
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
    } finally {
      setSending(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  };



  if (!conversation) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { height: '80vh', display: 'flex', flexDirection: 'column' }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              <PersonIcon />
            </Avatar>
            <Box>
              <Typography variant="h6">
                {conversation.contact_name || 'Contato'}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PhoneIcon fontSize="small" color="action" />
                <Typography variant="body2" color="text.secondary">
                  {conversation.contact_id}
                </Typography>
              </Box>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {conversation.requires_human_attention && (
              <Chip
                label="Requer Atenção"
                color="warning"
                size="small"
              />
            )}
            <IconButton onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ flex: 1, display: 'flex', flexDirection: 'column', p: 0 }}>
        <MessageList
          chatId={conversation.contact_id}
          chatName={conversation.contact_name}
        />
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <TextField
          fullWidth
          multiline
          maxRows={3}
          placeholder="Digite sua mensagem..."
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={sending}
          variant="outlined"
          size="small"
        />
        <Button
          variant="contained"
          onClick={sendMessage}
          disabled={!newMessage.trim() || sending}
          sx={{ minWidth: 'auto', px: 2 }}
        >
          {sending ? <CircularProgress size={20} /> : <SendIcon />}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConversationDetail;
