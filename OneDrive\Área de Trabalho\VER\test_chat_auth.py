#!/usr/bin/env python3

"""
Teste do chat com autenticação
"""

import requests
import json


def get_auth_token():
    """Obtém token de autenticação"""
    print("🔐 Obtendo token de autenticação...")
    
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/token",
            data=login_data,  # Form data para OAuth2
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print(f"✅ Token obtido: {token[:20]}...")
            return token
        else:
            print(f"❌ Erro no login: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Erro: {e}")
        return None


def test_chat_with_auth(token):
    """Testa chat com autenticação"""
    print("💬 Testando chat com autenticação...")
    
    chat_data = {
        "query": "Bom dia! Preciso de ajuda com cesta básica",
        "contact_id": "558488501582",
        "contact_name": "Italo Cabral"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/chat/",
            json=chat_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            response_text = data.get('response', '')
            print(f"✅ Chat funcionando!")
            print(f"📝 Resposta: {response_text[:200]}...")
            return True
        else:
            print(f"❌ Erro: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False


def test_send_message_with_auth(token):
    """Testa envio de mensagem com autenticação"""
    print("📤 Testando envio de mensagem...")
    
    message_data = {
        "contact_id": "558488501582",
        "contact_name": "Italo Cabral",
        "message": "Esta é uma mensagem de teste após as correções! 🙏",
        "conversation_id": "conv_teste_corrigido",
        "channel": "whatsapp"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/send-message",
            json=message_data,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Mensagem enviada: {data}")
            return True
        else:
            print(f"❌ Erro: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False


def main():
    """Função principal"""
    print("🔧 TESTE COMPLETO COM AUTENTICAÇÃO")
    print("=" * 50)
    
    # Obter token
    token = get_auth_token()
    if not token:
        print("❌ Não foi possível obter token. Teste cancelado.")
        return
    
    print("\n" + "=" * 50)
    
    tests = [
        ("Chat com Auth", lambda: test_chat_with_auth(token)),
        ("Send Message", lambda: test_send_message_with_auth(token))
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print("📊 RESULTADO FINAL")
    print("=" * 50)
    print(f"✅ Aprovados: {passed}/{total}")
    print(f"📈 Taxa: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 SISTEMA TOTALMENTE FUNCIONAL!")
        print("✅ Chat funcionando")
        print("✅ Notificações funcionando")
        print("✅ Envio de mensagens funcionando")
        print("🚀 Pronto para produção!")
    else:
        print("\n⚠️ Alguns problemas ainda existem")
        print("🔧 Verifique os logs para mais detalhes")


if __name__ == "__main__":
    main()
