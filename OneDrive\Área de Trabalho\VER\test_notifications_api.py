#!/usr/bin/env python3

"""
Teste específico das funcionalidades de notificações e mensagens
"""

import requests
import json
import time


def test_websocket_stats():
    """Testa estatísticas do WebSocket"""
    print("🔔 Testando estatísticas WebSocket...")
    
    try:
        response = requests.get("http://localhost:8000/ws/stats")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ WebSocket Stats: {data}")
            return True
        else:
            print(f"   ❌ Erro: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_whatsapp_status():
    """Testa status do WhatsApp"""
    print("📱 Testando status WhatsApp...")
    
    try:
        response = requests.get("http://localhost:8000/whatsapp/status")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ WhatsApp Status: {data}")
            return True
        else:
            print(f"   ❌ Erro: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_send_message():
    """Testa envio de mensagem"""
    print("📤 Testando envio de mensagem...")
    
    message_data = {
        "contact_id": "5511999999999",
        "contact_name": "Usuário Teste",
        "message": "Esta é uma mensagem de teste do sistema! Nossa equipe está aqui para ajudar você! 🙏",
        "conversation_id": "conv_teste_123",
        "channel": "whatsapp"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/send-message",
            json=message_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Mensagem enviada: {data}")
            return True
        else:
            print(f"   ❌ Erro {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_send_notification():
    """Testa envio de notificação"""
    print("🔔 Testando envio de notificação...")
    
    notification_data = {
        "title": "Teste de Notificação",
        "message": "Esta é uma notificação de teste do sistema",
        "priority": "normal"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/ws/notify",
            json=notification_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Notificação enviada: {data}")
            return True
        else:
            print(f"   ❌ Erro {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_chat_endpoint():
    """Testa endpoint de chat"""
    print("💬 Testando endpoint de chat...")
    
    chat_data = {
        "query": "Bom dia! Preciso de ajuda com cesta básica",
        "contact_id": "5511888888888",
        "contact_name": "Maria Teste"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/chat/",
            json=chat_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Chat respondeu: {data.get('response', '')[:100]}...")
            return True
        else:
            print(f"   ❌ Erro {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_message_stats():
    """Testa estatísticas de mensagens"""
    print("📊 Testando estatísticas de mensagens...")
    
    try:
        response = requests.get("http://localhost:8000/messages/stats")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Message Stats: {data}")
            return True
        else:
            print(f"   ❌ Erro: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def test_broadcast():
    """Testa broadcast de mensagem"""
    print("📢 Testando broadcast...")
    
    broadcast_data = {
        "type": "test_broadcast",
        "title": "Teste de Broadcast",
        "message": "Esta é uma mensagem de broadcast de teste",
        "timestamp": "2024-01-01T10:00:00Z"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/ws/broadcast",
            json=broadcast_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Broadcast enviado: {data}")
            return True
        else:
            print(f"   ❌ Erro {response.status_code}: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return False


def main():
    """Função principal"""
    print("🧪 TESTE DAS FUNCIONALIDADES DE NOTIFICAÇÕES E MENSAGENS")
    print("=" * 70)
    
    tests = [
        ("WebSocket Stats", test_websocket_stats),
        ("WhatsApp Status", test_whatsapp_status),
        ("Chat Endpoint", test_chat_endpoint),
        ("Send Message", test_send_message),
        ("Send Notification", test_send_notification),
        ("Message Stats", test_message_stats),
        ("Broadcast", test_broadcast)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        if test_func():
            passed += 1
        
        time.sleep(1)
    
    print("\n" + "=" * 70)
    print("📊 RESULTADO FINAL")
    print("=" * 70)
    print(f"✅ Testes aprovados: {passed}/{total}")
    print(f"📈 Taxa de sucesso: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Sistema de notificações e mensagens funcionando perfeitamente!")
        print("🔔 Notificações instantâneas: ATIVO")
        print("📱 Envio de mensagens: ATIVO")
        print("🌐 WebSocket: FUNCIONANDO")
        print("📊 Monitoramento: ATIVO")
    elif passed >= total * 0.8:
        print("\n✅ SISTEMA FUNCIONANDO BEM!")
        print("⚠️ Alguns recursos podem precisar de configuração (ex: WhatsApp)")
        print("🔔 Notificações: FUNCIONANDO")
        print("📱 Envio: PARCIALMENTE ATIVO")
    else:
        print("\n⚠️ ALGUNS PROBLEMAS ENCONTRADOS")
        print("🔧 Verifique a configuração do sistema")
    
    print("\n🚀 Para usar o sistema completo:")
    print("   1. ✅ Backend rodando em http://localhost:8000")
    print("   2. ✅ WebSocket ativo em ws://localhost:8000/ws")
    print("   3. ⚠️ Configure WhatsApp (WPPConnect) se necessário")
    print("   4. ✅ Frontend pode conectar e usar todas as funcionalidades")


if __name__ == "__main__":
    main()
