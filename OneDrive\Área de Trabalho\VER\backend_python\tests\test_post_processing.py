"""
Testes unitários para o pipeline de pós-processamento
"""

import unittest
import sys
import os

# Adicionar o diretório pai ao path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pipelines.post_processing import PostProcessor, process_response
from config.persona import EMOJIS, HASHTAGS


class TestPostProcessing(unittest.TestCase):
    """
    Testes para o pipeline de pós-processamento
    """
    
    def setUp(self):
        """Configurar testes"""
        self.processor = PostProcessor()
    
    def test_clean_text(self):
        """Testa a limpeza de texto"""
        # Texto com espaços extras
        dirty_text = "  Olá,   como   você está?  \n\n  "
        clean_text = self.processor.clean_text(dirty_text)
        self.assertEqual(clean_text, "Olá, como você está?")
        
        # Texto com quebras de linha desnecessárias
        dirty_text2 = "Primeira linha\n\n\nSegunda linha"
        clean_text2 = self.processor.clean_text(dirty_text2)
        self.assertEqual(clean_text2, "Primeira linha\nSegunda linha")
        
        # Texto com pontuação sem espaço
        dirty_text3 = "Olá.Como vai?Tudo bem!"
        clean_text3 = self.processor.clean_text(dirty_text3)
        self.assertEqual(clean_text3, "Olá. Como vai? Tudo bem!")
    
    def test_add_emoji(self):
        """Testa a adição de emojis"""
        text = "Olá, como posso ajudar?"
        
        # Teste com força
        text_with_emoji = self.processor.add_emoji(text, force=True)
        self.assertNotEqual(text, text_with_emoji)
        
        # Verificar se emoji foi adicionado
        added_emoji = text_with_emoji.replace(text, "").strip()
        self.assertIn(added_emoji, EMOJIS)
        
        # Teste sem duplicação
        text_with_emoji2 = self.processor.add_emoji(text_with_emoji, force=False)
        self.assertEqual(text_with_emoji, text_with_emoji2)
    
    def test_add_hashtag(self):
        """Testa a adição de hashtags"""
        text = "Vamos trabalhar juntos!"
        
        # Teste com força
        text_with_hashtag = self.processor.add_hashtag(text, force=True)
        self.assertNotEqual(text, text_with_hashtag)
        
        # Verificar se hashtag foi adicionada
        self.assertIn("#", text_with_hashtag)
        
        # Teste sem duplicação
        text_with_hashtag2 = self.processor.add_hashtag(text_with_hashtag, force=False)
        self.assertEqual(text_with_hashtag, text_with_hashtag2)
    
    def test_limit_length(self):
        """Testa a limitação de comprimento"""
        # Texto curto (não deve ser alterado)
        short_text = "Texto curto"
        limited_text = self.processor.limit_length(short_text)
        self.assertEqual(short_text, limited_text)
        
        # Texto longo (deve ser limitado)
        long_text = "Este é um texto muito longo que deveria ser cortado. " * 20
        limited_text = self.processor.limit_length(long_text)
        self.assertLess(len(limited_text), len(long_text))
        self.assertLessEqual(len(limited_text), self.processor.config["max_response_length"])
    
    def test_contextual_emoji_selection(self):
        """Testa a seleção contextual de emojis"""
        # Teste contexto de saúde
        health_text = "Vou verificar na UBS sobre seu medicamento"
        health_emoji = self.processor._select_contextual_emoji(health_text)
        self.assertIn(health_emoji, EMOJIS)
        
        # Teste contexto de trabalho
        work_text = "Vou verificar oportunidades de trabalho"
        work_emoji = self.processor._select_contextual_emoji(work_text)
        self.assertIn(work_emoji, EMOJIS)
        
        # Teste contexto de fé
        faith_text = "Que Deus nos abençoe"
        faith_emoji = self.processor._select_contextual_emoji(faith_text)
        self.assertIn(faith_emoji, EMOJIS)
    
    def test_contextual_hashtag_selection(self):
        """Testa a seleção contextual de hashtags"""
        # Teste contexto de inclusão
        inclusion_text = "A inclusão é fundamental para nossa sociedade"
        inclusion_hashtag = self.processor._select_contextual_hashtag(inclusion_text)
        self.assertEqual(inclusion_hashtag, "#AcessibilidadeParaTodos")
        
        # Teste contexto de transparência
        transparency_text = "Todos os gastos estão disponíveis para consulta"
        transparency_hashtag = self.processor._select_contextual_hashtag(transparency_text)
        self.assertEqual(transparency_hashtag, "#TransparênciaTotal")
        
        # Teste contexto de Parnamirim
        parnamirim_text = "Nossa cidade Parnamirim merece o melhor"
        parnamirim_hashtag = self.processor._select_contextual_hashtag(parnamirim_text)
        self.assertEqual(parnamirim_hashtag, "#ParnamirimRN")
    
    def test_has_emoji_detection(self):
        """Testa a detecção de emojis existentes"""
        text_without_emoji = "Texto sem emoji"
        text_with_emoji = "Texto com emoji ✨"
        
        self.assertFalse(self.processor._has_emoji(text_without_emoji))
        self.assertTrue(self.processor._has_emoji(text_with_emoji))
    
    def test_has_hashtag_detection(self):
        """Testa a detecção de hashtags existentes"""
        text_without_hashtag = "Texto sem hashtag"
        text_with_hashtag = "Texto com hashtag #RafaelaDeNilda"
        
        self.assertFalse(self.processor._has_hashtag(text_without_hashtag))
        self.assertTrue(self.processor._has_hashtag(text_with_hashtag))
    
    def test_process_response_complete_pipeline(self):
        """Testa o pipeline completo de processamento"""
        original_text = "  Olá,   como posso ajudar você hoje?  "
        
        # Processar com emoji e hashtag
        processed_text = self.processor.process_response(
            original_text, 
            add_emoji=True, 
            add_hashtag=True
        )
        
        # Verificar se foi limpo
        self.assertNotIn("  ", processed_text)
        
        # Verificar se tem comprimento adequado
        self.assertLessEqual(len(processed_text), self.processor.config["max_response_length"])
        
        # Verificar se contém o texto original (limpo)
        self.assertIn("Olá, como posso ajudar você hoje?", processed_text)
    
    def test_process_response_function(self):
        """Testa a função de conveniência process_response"""
        text = "Teste de processamento"
        processed = process_response(text)
        
        self.assertIsInstance(processed, str)
        self.assertGreaterEqual(len(processed), len(text))
    
    def test_edge_cases(self):
        """Testa casos extremos"""
        # Texto vazio
        empty_text = ""
        processed_empty = self.processor.process_response(empty_text)
        self.assertIsInstance(processed_empty, str)
        
        # Texto muito longo
        very_long_text = "Palavra " * 1000
        processed_long = self.processor.process_response(very_long_text)
        self.assertLessEqual(len(processed_long), self.processor.config["max_response_length"])
        
        # Texto só com espaços
        spaces_text = "   \n\n   "
        processed_spaces = self.processor.clean_text(spaces_text)
        self.assertEqual(processed_spaces, "")
    
    def test_emoji_probability_respect(self):
        """Testa se a probabilidade de emoji é respeitada"""
        text = "Teste de probabilidade"
        
        # Com probabilidade 0, não deve adicionar emoji
        original_config = self.processor.config["emoji_probability"]
        self.processor.config["emoji_probability"] = 0.0
        
        processed = self.processor.add_emoji(text, force=False)
        self.assertEqual(text, processed)
        
        # Restaurar configuração
        self.processor.config["emoji_probability"] = original_config


if __name__ == "__main__":
    unittest.main()
