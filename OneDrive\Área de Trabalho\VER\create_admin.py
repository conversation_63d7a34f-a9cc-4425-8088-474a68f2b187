#!/usr/bin/env python3
"""
Script para criar usuário admin diretamente no Supabase.
"""
import os
from supabase import create_client, Client
from passlib.context import CryptContext
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

# Configurações do Supabase
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

# Configuração de hash de senha
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def create_admin_user():
    """Cria ou atualiza o usuário admin."""
    try:
        # Conectar ao Supabase
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        print(f"✅ Conectado ao Supabase: {SUPABASE_URL}")
        
        # Verificar se usuário admin já existe
        response = supabase.table('users').select('*').eq('username', 'admin').execute()
        
        # Hash da senha
        hashed_password = pwd_context.hash("admin123")
        
        if response.data:
            # Usuário existe, atualizar senha
            print("👤 Usuário admin já existe, atualizando senha...")
            update_response = supabase.table('users').update({
                'hashed_password': hashed_password,
                'email': '<EMAIL>',
                'full_name': 'Administrador',
                'disabled': False
            }).eq('username', 'admin').execute()
            
            if update_response.data:
                print("✅ Senha do usuário admin atualizada com sucesso!")
                print("   Username: admin")
                print("   Password: admin123")
                return True
            else:
                print("❌ Erro ao atualizar usuário admin")
                return False
        else:
            # Usuário não existe, criar novo
            print("👤 Criando novo usuário admin...")
            user_data = {
                'username': 'admin',
                'email': '<EMAIL>',
                'full_name': 'Administrador',
                'disabled': False,
                'hashed_password': hashed_password
            }
            
            insert_response = supabase.table('users').insert(user_data).execute()
            
            if insert_response.data:
                print("✅ Usuário admin criado com sucesso!")
                print("   Username: admin")
                print("   Password: admin123")
                return True
            else:
                print("❌ Erro ao criar usuário admin")
                return False
                
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def test_login():
    """Testa o login após criar o usuário."""
    import requests
    
    try:
        response = requests.post(
            "http://localhost:8000/token",
            data={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Teste de login bem-sucedido!")
            print(f"   Token: {data['access_token'][:50]}...")
            return True
        else:
            print(f"❌ Erro no teste de login: {response.status_code}")
            print(f"   Resposta: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro no teste de login: {e}")
        return False

def main():
    print("🔧 Criando usuário admin...")
    
    if create_admin_user():
        print("\n🧪 Testando login...")
        test_login()
    else:
        print("❌ Falha ao criar usuário admin")

if __name__ == "__main__":
    main()
